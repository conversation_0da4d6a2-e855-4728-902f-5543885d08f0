/**
 * Formata um número de telefone durante a digitação
 * @param phone - String contendo o número de telefone
 * @returns String formatada no padrão brasileiro
 */
export const formatPhoneOnInput = (phone: string): string => {
    const cleaned = phone.replace(/\D/g, '');
    const truncated = cleaned.slice(0, 11);

    if (truncated.length <= 2) {
        return truncated;
    } else if (truncated.length <= 6) {
        return `(${truncated.substring(0, 2)}) ${truncated.substring(2)}`;
    } else if (truncated.length <= 10) {
        return `(${truncated.substring(0, 2)}) ${truncated.substring(2, 6)}-${truncated.substring(6)}`;
    } else {
        return `(${truncated.substring(0, 2)}) ${truncated.substring(2, 7)}-${truncated.substring(7)}`;
    }
};

/**
 * Formata um número de telefone para exibição
 * @param phone - String contendo o número de telefone ou undefined
 * @returns String formatada no padrão brasileiro
 */
export const formatPhoneNumber = (phone: string | undefined): string => {
    if (!phone) return '';

    const cleaned = phone.replace(/\D/g, '');

    if (cleaned.length === 11) {
        return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 7)}-${cleaned.substring(7)}`;
    } else if (cleaned.length === 10) {
        return `(${cleaned.substring(0, 2)}) ${cleaned.substring(2, 6)}-${cleaned.substring(6)}`;
    }

    return phone;
};

/**
 * Valida um número de telefone brasileiro
 * @param phone - String contendo o número de telefone
 * @returns Booleano indicando se o número é válido
 */
export const isValidPhone = (phone: string): boolean => {
    const cleaned = phone.replace(/\D/g, '');
    return cleaned.length === 10 || cleaned.length === 11;
};

/**
 * Formata um CPF durante a digitação
 * @param cpf - String contendo o CPF
 * @returns String formatada no padrão XXX.XXX.XXX-XX
 */
export const formatCpfOnInput = (cpf: string): string => {
    const cleaned = cpf.replace(/\D/g, '');
    const truncated = cleaned.slice(0, 11);

    if (truncated.length <= 3) {
        return truncated;
    } else if (truncated.length <= 6) {
        return `${truncated.substring(0, 3)}.${truncated.substring(3)}`;
    } else if (truncated.length <= 9) {
        return `${truncated.substring(0, 3)}.${truncated.substring(3, 6)}.${truncated.substring(6)}`;
    } else {
        return `${truncated.substring(0, 3)}.${truncated.substring(3, 6)}.${truncated.substring(6, 9)}-${truncated.substring(9)}`;
    }
};

/**
 * Formata um CPF para exibição
 * @param cpf - String contendo o CPF ou undefined
 * @returns String formatada no padrão XXX.XXX.XXX-XX
 */
export const formatCpf = (cpf: string | undefined): string => {
    if (!cpf) return '';

    const cleaned = cpf.replace(/\D/g, '');

    if (cleaned.length === 11) {
        return `${cleaned.substring(0, 3)}.${cleaned.substring(3, 6)}.${cleaned.substring(6, 9)}-${cleaned.substring(9)}`;
    }

    return cpf;
};

/**
 * Valida um CPF brasileiro
 * @param cpf - String contendo o CPF
 * @returns Booleano indicando se o CPF é válido
 */
export const isValidCpf = (cpf: string): boolean => {
    const cleaned = cpf.replace(/\D/g, '');

    if (cleaned.length !== 11) return false;
    if (/^(\d)\1+$/.test(cleaned)) return false; // Sequência de números iguais

    // Validação do primeiro dígito verificador
    let sum = 0;
    for (let i = 0; i < 9; i++) {
        sum += parseInt(cleaned.charAt(i)) * (10 - i);
    }
    let remainder = 11 - (sum % 11);
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cleaned.charAt(9))) return false;

    // Validação do segundo dígito verificador
    sum = 0;
    for (let i = 0; i < 10; i++) {
        sum += parseInt(cleaned.charAt(i)) * (11 - i);
    }
    remainder = 11 - (sum % 11);
    if (remainder === 10 || remainder === 11) remainder = 0;
    if (remainder !== parseInt(cleaned.charAt(10))) return false;

    return true;
};

/**
 * Formata um CNPJ durante a digitação
 * @param cnpj - String contendo o CNPJ
 * @returns String formatada no padrão XX.XXX.XXX/XXXX-XX
 */
export const formatCnpjOnInput = (cnpj: string): string => {
    const cleaned = cnpj.replace(/\D/g, '');
    const truncated = cleaned.slice(0, 14);

    if (truncated.length <= 2) {
        return truncated;
    } else if (truncated.length <= 5) {
        return `${truncated.substring(0, 2)}.${truncated.substring(2)}`;
    } else if (truncated.length <= 8) {
        return `${truncated.substring(0, 2)}.${truncated.substring(2, 5)}.${truncated.substring(5)}`;
    } else if (truncated.length <= 12) {
        return `${truncated.substring(0, 2)}.${truncated.substring(2, 5)}.${truncated.substring(5, 8)}/${truncated.substring(8)}`;
    } else {
        return `${truncated.substring(0, 2)}.${truncated.substring(2, 5)}.${truncated.substring(5, 8)}/${truncated.substring(8, 12)}-${truncated.substring(12)}`;
    }
};

/**
 * Formata um CNPJ para exibição
 * @param cnpj - String contendo o CNPJ ou undefined
 * @returns String formatada no padrão XX.XXX.XXX/XXXX-XX
 */
export const formatCnpj = (cnpj: string | undefined): string => {
    if (!cnpj) return '';

    const cleaned = cnpj.replace(/\D/g, '');

    if (cleaned.length === 14) {
        return `${cleaned.substring(0, 2)}.${cleaned.substring(2, 5)}.${cleaned.substring(5, 8)}/${cleaned.substring(8, 12)}-${cleaned.substring(12)}`;
    }

    return cnpj;
};

/**
 * Valida um CNPJ brasileiro
 * @param cnpj - String contendo o CNPJ
 * @returns Booleano indicando se o CNPJ é válido
 */
export const isValidCnpj = (cnpj: string): boolean => {
    const cleaned = cnpj.replace(/\D/g, '');

    if (cleaned.length !== 14) return false;
    if (/^(\d)\1+$/.test(cleaned)) return false; // Sequência de números iguais

    // Validação do primeiro dígito verificador
    let sum = 0;
    let weight = 5;
    for (let i = 0; i < 12; i++) {
        sum += parseInt(cleaned.charAt(i)) * weight;
        weight = weight === 2 ? 9 : weight - 1;
    }
    let remainder = sum % 11;
    const firstDigit = remainder < 2 ? 0 : 11 - remainder;
    if (firstDigit !== parseInt(cleaned.charAt(12))) return false;

    // Validação do segundo dígito verificador
    sum = 0;
    weight = 6;
    for (let i = 0; i < 13; i++) {
        sum += parseInt(cleaned.charAt(i)) * weight;
        weight = weight === 2 ? 9 : weight - 1;
    }
    remainder = sum % 11;
    const secondDigit = remainder < 2 ? 0 : 11 - remainder;
    if (secondDigit !== parseInt(cleaned.charAt(13))) return false;

    return true;
};

/**
 * Valida valor de salário
 * @param value - String contendo o valor do salário
 * @returns Booleano indicando se o valor é válido
 */

export const formatSalary = (value: string | number | null | undefined): string => {
    if (!value) return '';

    const numericValue = typeof value === 'string' ?
        parseFloat(value.replace(/[^\d,.-]/g, '').replace(',', '.')) :
        value;

    if (isNaN(numericValue)) return '';

    return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
    }).format(numericValue);
};

export const parseSalary = (value: string): number | null => {
    if (!value) return null;

    const cleaned = value.replace(/[^\d,.-]/g, '').replace(',', '.');
    const parsed = parseFloat(cleaned);

    return isNaN(parsed) ? null : parsed;
};