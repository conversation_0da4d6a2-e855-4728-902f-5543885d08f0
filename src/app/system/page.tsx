"use client"

import { useRouter } from "next/navigation";
import { Footer } from "../components/footer";
import { Header } from "../components/header";
import { Button } from "../components/ui/button";
import { ListRestart, Logs, MoveLeft } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { NotAllowed } from "../components/not-allowed";

export default function SystemPage() {
    const { status, data: session } = useSession();
    const [userRole, setUserRole] = useState('');
    const router = useRouter();

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        setUserRole(user?.role || '');
                    }
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
            }
        };

        fetchUserRole();
    }, [session]);

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {userRole !== 'VIEWER' ? (
                    <><Button
                        variant="outline"
                        onClick={() => router.push('/dashboard')}
                    >
                        <MoveLeft className="h-4 w-4" />
                        Voltar
                    </Button>
                        <h2 className="text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800 pb-2 mt-6">
                            Painel do sistema
                        </h2>
                        <div className="mt-4">
                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                Bem-vindo ao painel do sistema. Aqui você pode visualizar atualizações e informações sobre o sistema.
                            </p>
                            <div className="mt-8 grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <Link href="/updates" className="group flex items-center justify-center p-6 bg-white dark:bg-zinc-800 rounded-lg shadow-sm border border-zinc-200 dark:border-zinc-700 hover:border-primary2 dark:hover:border-primary2 transition-colors">
                                    <div className="text-center">
                                        <div className="mb-2 flex justify-center">
                                            <ListRestart size={28} className="text-gray-700 dark:text-gray-300 group-hover:text-primary2 transition-colors" />
                                        </div>
                                        <h3 className="text-lg font-medium">Atualizações</h3>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">
                                            Visualizar atualizações recentes do sistema
                                        </p>
                                    </div>
                                </Link>

                                <Link href="/user-activity" className="group flex items-center justify-center p-6 bg-white dark:bg-zinc-800 rounded-lg border border-zinc-200 dark:border-zinc-700 hover:border-primary2 dark:hover:border-primary2 transition-colors">
                                    <div className="text-center">
                                        <div className="mb-2 flex justify-center">
                                            <Logs size={28} className="text-gray-700 dark:text-gray-300 group-hover:text-primary2 transition-colors" />
                                        </div>
                                        <h3 className="text-lg font-medium">Atividades</h3>
                                        <p className="text-sm text-gray-500 dark:text-gray-400">
                                            Visualizar atividades dos usuários
                                        </p>
                                    </div>
                                </Link>
                            </div>
                        </div></>
                ) : (
                    <NotAllowed
                        page='/'
                    />
                )}
            </div>
            <Footer />
        </div>
    );
}