"use client"

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Header } from '../components/header';
import { Footer } from '../components/footer';
import { Button } from '../components/ui/button';
import { Card, CardContent } from '../components/ui/card';
import { MoveLeft, FileClock } from 'lucide-react';
import Loading from '../components/ui/loading';
import { NotAllowed } from '../components/not-allowed';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Badge } from '../components/ui/badge';

interface PointRecord {
    id: string;
    clockIn?: string;
    clockOut?: string;
    date: string;
    totalHours?: number;
    createdAt: string;
}

export default function HistoryPointRecordPage() {
    const { data: session, status } = useSession();
    const [records, setRecords] = useState<PointRecord[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [hasPermission, setHasPermission] = useState(false);
    const router = useRouter();

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        if (status === "authenticated" && session?.user?.email) {
            const fetchUserPermissions = async () => {
                try {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        const allowedRoles = ["DESIGNER", "COPY", "DESIGNER_SENIOR", "DESIGNER_JUNIOR", "ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"];
                        setHasPermission(allowedRoles.includes(user?.role));
                    }
                } catch (error) {
                    console.error("Erro ao verificar permissões:", error);
                    setHasPermission(false);
                }
            };

            fetchUserPermissions();
        }
    }, [status, session]);

    useEffect(() => {
        if (hasPermission && session?.user?.email) {
            fetchRecords();
        }
    }, [hasPermission, session]);

    const fetchRecords = async () => {
        try {
            const response = await fetch('/api/point-record/history');
            if (response.ok) {
                const data = await response.json();
                setRecords(data);
            }
        } catch (error) {
            console.error('Erro ao buscar histórico:', error);
        } finally {
            setIsLoading(false);
        }
    };

    const formatTime = (timeString?: string) => {
        if (!timeString) return '-';
        return format(new Date(timeString), 'HH:mm', { locale: ptBR });
    };

    const formatDate = (dateString: string) => {
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day);
        return format(date, 'dd/MM/yyyy', { locale: ptBR });
    };

    const formatHours = (hours?: number) => {
        if (!hours) return '-';
        const h = Math.floor(hours);
        const m = Math.round((hours - h) * 60);
        return `${h}h ${m}m`;
    };

    const groupedRecords = records.reduce((acc: Record<string, PointRecord[]>, record) => {
        if (!acc[record.date]) {
            acc[record.date] = [];
        }
        acc[record.date].push(record);
        return acc;
    }, {});

    const getDayTotalHours = (dayRecords: PointRecord[]) => {
        return dayRecords.reduce((total, record) => total + (record.totalHours || 0), 0);
    };

    const getStatusBadge = (record: PointRecord) => {
        if (record.clockIn && record.clockOut) {
            return <Badge variant="success" className='rounded-full'>Completo</Badge>;
        } else if (record.clockIn && !record.clockOut) {
            return <Badge variant="secondary" className='rounded-full'>Em aberto</Badge>;
        } else {
            return <Badge variant="outline" className='rounded-full'>Incompleto</Badge>;
        }
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isLoading ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : hasPermission ? (
                    <>
                        <div className="flex flex-row justify-between items-start gap-2 mb-4">
                            <Button variant="outline" onClick={() => router.push("/")}>
                                <MoveLeft />
                            </Button>
                            <div className="flex items-center gap-2 group">
                                <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                    Meu histórico de ponto
                                </h1>
                                <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                    <FileClock size={24} color="#db5743" />
                                </div>
                            </div>
                        </div>

                        <Card>
                            <CardContent className="p-4">
                                {records.length === 0 ? (
                                    <p className="text-gray-500 text-sm text-center p-8">Nenhum registro de ponto encontrado</p>
                                ) : (
                                    <div className="space-y-6">
                                        {Object.entries(groupedRecords)
                                            .sort(([a], [b]) => new Date(b).getTime() - new Date(a).getTime())
                                            .map(([date, dayRecords]) => {
                                                const dayTotal = getDayTotalHours(dayRecords);
                                                return (
                                                    <div key={date} className="space-y-2">
                                                        <div className="flex justify-between items-center p-3 bg-muted rounded-lg">
                                                            <div>
                                                                <p className="font-semibold">{formatDate(date)}</p>
                                                                <p className="text-xs text-muted-foreground">
                                                                    {format(new Date(date), 'EEEE', { locale: ptBR })}
                                                                </p>
                                                            </div>
                                                            <div className="text-right">
                                                                <p className="text-sm text-muted-foreground">Total do dia</p>
                                                                <p className="font-bold text-lg">{formatHours(dayTotal)}</p>
                                                            </div>
                                                        </div>
                                                        <div className="space-y-2 ml-4">
                                                            {dayRecords.map((record) => (
                                                                <div key={record.id} className="flex items-center justify-between p-3 border rounded-lg overflow-x-auto">
                                                                    <div className="flex items-center gap-6">
                                                                        <div className="text-center">
                                                                            <p className="text-xs text-muted-foreground">Entrada</p>
                                                                            <p className="font-mono font-semibold">{formatTime(record.clockIn)}</p>
                                                                        </div>
                                                                        <div className="text-center">
                                                                            <p className="text-xs text-muted-foreground">Saída</p>
                                                                            <p className="font-mono font-semibold">{formatTime(record.clockOut)}</p>
                                                                        </div>
                                                                        <div className="text-center">
                                                                            <p className="text-xs text-muted-foreground">Duração</p>
                                                                            <p className="font-semibold">{formatHours(record.totalHours)}</p>
                                                                        </div>
                                                                        <div className="text-center">
                                                                            <p className="text-xs text-muted-foreground">Status</p>
                                                                            {getStatusBadge(record)}
                                                                        </div>
                                                                    </div>
                                                                </div>
                                                            ))}
                                                        </div>
                                                    </div>
                                                );
                                            })}
                                    </div>
                                )}
                            </CardContent>
                        </Card>
                    </>
                ) : (
                    <NotAllowed page="/dashboard" />
                )}
            </div>
            <Footer />
        </div>
    );
}