"use client"

import { useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { AppWindowMac, Archive, CirclePlus, MoveLeft, Search, SortAsc, SortDesc, Users } from 'lucide-react';
import { toast } from "sonner";
import { Header } from '../components/header';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '../components/ui/table';
import { Footer } from '../components/footer';
import Loading from '../components/ui/loading';
import { EditClient } from '../components/edit-client.tsx';
import { NotAllowed } from '../components/not-allowed';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '../components/ui/dropdown-menu';
import { Dialog, DialogTrigger } from '../components/ui/dialog';
import { ClientNavigationModal } from '../components/client-navigation-modal';


import { formatPhoneNumber } from '@/lib/formatters';
import Link from 'next/link';

export interface Client {
    id: string;
    name: string;
    instagramUsername?: string;
    phone?: string;
    monthlyPostsLimit?: number;
    monthlyStoriesLimit?: number;
    additionalContent?: number;
    monthlyPlannings?: [];
    createdAt?: string;
    archived?: boolean;
    archivedAt?: string;
}

export default function ClientsPage() {
    const { data: session, status } = useSession();
    const [clients, setClients] = useState<Client[]>([]);
    const [filteredClients, setFilteredClients] = useState<Client[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [sortOption, setSortOption] = useState<'name_asc' | 'name_desc' | 'date_asc' | 'date_desc'>('name_asc');
    const [isAdmin, setIsAdmin] = useState(false);
    const [userRole, setUserRole] = useState('');
    const [userAccessLevel, setUserAccessLevel] = useState('VIEWER');
    const [isLoading, setIsLoading] = useState(true);
    const router = useRouter();
    const [showArchived, setShowArchived] = useState(false);
    const [openModalForClientId, setOpenModalForClientId] = useState<string | null>(null);

    useEffect(() => {
        const checkQueryParams = () => {
            const urlParams = new URLSearchParams(window.location.search);
            const clientId = urlParams.get('openModal');
            if (clientId) {
                setOpenModalForClientId(clientId);
                const newUrl = window.location.pathname;
                window.history.replaceState({}, document.title, newUrl);
            }
        };

        if (typeof window !== 'undefined') {
            checkQueryParams();
        }

        if (status === "authenticated") {
            setIsLoading(true);
            fetch(`/api/clients?archived=${showArchived}`)
                .then((res) => res.json())
                .then((data) => {
                    const sortedData = [...data].sort((a, b) => a.name.localeCompare(b.name));
                    setClients(sortedData);
                    setFilteredClients(sortedData);
                })
                .finally(() => setIsLoading(false));

            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const response = await fetch(`/api/users/${session.user.email}`);
                        if (response.ok) {
                            const user = await response.json();
                            const adminRoles = ["ADMIN", "DEVELOPER", "COPY", "DESIGNER_SENIOR", "GENERAL_ASSISTANT"];
                            setUserRole(user?.role || '');
                            setUserAccessLevel(user?.accessLevel || 'VIEWER');
                            setIsAdmin(user?.role ? adminRoles.includes(user.role) : false);
                        }
                    }
                } catch (error) {
                    console.error("Error fetching user role:", error);
                }
            };

            fetchUserRole();
        } else if (status === 'unauthenticated') {
            router.push('/');
        }
    }, [status, session, router, showArchived]);

    useEffect(() => {
        let result = [...clients];

        if (searchQuery.trim() !== '') {
            const query = searchQuery.toLowerCase();
            result = result.filter(client =>
                client.name?.toLowerCase().includes(query) ||
                client.instagramUsername?.toLowerCase().includes(query) ||
                client.phone?.includes(query)
            );
        }

        switch (sortOption) {
            case 'name_asc':
                result.sort((a, b) => a.name.localeCompare(b.name));
                break;
            case 'name_desc':
                result.sort((a, b) => b.name.localeCompare(a.name));
                break;
            case 'date_asc':
                result.sort((a, b) => {
                    return new Date(a.createdAt || '').getTime() - new Date(b.createdAt || '').getTime();
                });
                break;
            case 'date_desc':
                result.sort((a, b) => {
                    return new Date(b.createdAt || '').getTime() - new Date(a.createdAt || '').getTime();
                });
                break;
        }

        setFilteredClients(result);
    }, [clients, searchQuery, sortOption]);

    const refreshData = () => {
        setIsLoading(true);
        fetch(`/api/clients?archived=${showArchived}`)
            .then((res) => res.json())
            .then((data) => {
                setClients(data);
                setFilteredClients(data);
            })
            .catch((error) => {
                console.error("Erro ao buscar clientes:", error);
                toast.error("Erro ao atualizar dados dos clientes");
            })
            .finally(() => setIsLoading(false));
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isLoading ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <>
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2">
                            <Button variant="outline" onClick={() => router.push("/")}>
                                <MoveLeft /> Voltar
                            </Button>
                            <div className="flex items-center gap-2 group">
                                <h1 className="text-xl uppercase font-geistMono font-semibold tracking-tight">
                                    {showArchived ? "Clientes arquivados" : "Clientes ativos"}
                                </h1>
                                <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                    <Users size={24} color="#db5743" />
                                </div>
                            </div>
                        </div>

                        <div className='my-4 w-full text-right'>
                            {(userRole === 'ADMIN' || userRole === 'DEVELOPER' || userRole === 'GENERAL_ASSISTANT') && !showArchived && (
                                <Link href="/create-client">
                                    <Button className='w-full sm:w-auto'>
                                        <CirclePlus />
                                        Novo cliente
                                    </Button>
                                </Link>
                            )}
                        </div>

                        <div className="flex flex-col sm:flex-row gap-2 justify-between mb-4">
                            <div className='flex flex-col xs:flex-row gap-2 xs:items-center'>
                                <div className="relative w-full max-w-lg">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400" size={18} />
                                    <Input
                                        type="text"
                                        placeholder="Pesquisar"
                                        className="pl-10 placeholder:text-sm"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                    />
                                </div>
                                <Button
                                    variant="outline"
                                    className={`${showArchived ? 'text-green-600' : 'text-amber-600'} ${['COPY', 'DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR'].includes(userRole) ? 'hidden' : ''}`}
                                    onClick={() => setShowArchived(!showArchived)}
                                >
                                    <Archive size={16} />
                                    {showArchived ? "Ver clientes ativos" : "Ver clientes arquivados"}
                                </Button>
                            </div>

                            <div className="flex gap-2">
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="outline" className="flex gap-2 w-full">
                                            {sortOption.includes('name')
                                                ? <span>Ordenar por nome</span>
                                                : <span>Ordenar por data</span>
                                            }
                                            {sortOption.includes('asc') ? <SortAsc size={16} /> : <SortDesc size={16} />}
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem
                                            onClick={() => setSortOption('name_asc')}
                                            className={sortOption === 'name_asc' ? 'bg-muted' : ''}
                                        >
                                            <span className="pr-2">Nome (A-Z)</span>
                                            <SortAsc size={16} />
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => setSortOption('name_desc')}>
                                            <span className="pr-2">Nome (Z-A)</span>
                                            <SortDesc size={16} />
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem onClick={() => setSortOption('date_desc')}>
                                            <span className="pr-2">Mais recentes</span>
                                            <SortDesc size={16} />
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => setSortOption('date_asc')}>
                                            <span className="pr-2">Mais antigos</span>
                                            <SortAsc size={16} />
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>

                        <Table>
                            <TableCaption>Total de clientes: {filteredClients.length}</TableCaption>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Menu</TableHead>
                                    <TableHead>Nome</TableHead>
                                    <TableHead>Instagram</TableHead>
                                    <TableHead>Telefone</TableHead>
                                    <TableHead>Editar</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredClients.length > 0 ? (
                                    filteredClients.map((client) =>
                                        client.id ? (
                                            <TableRow key={client.id}>
                                                <TableCell className='w-16'>
                                                    <Dialog open={openModalForClientId === client.id} onOpenChange={(open) => {
                                                        if (!open) setOpenModalForClientId(null);
                                                    }}>
                                                        <DialogTrigger asChild>
                                                            <Button variant="outline" size="icon" disabled={showArchived} onClick={() => setOpenModalForClientId(client.id)}>
                                                                <AppWindowMac />
                                                            </Button>
                                                        </DialogTrigger>
                                                        <ClientNavigationModal
                                                            open={openModalForClientId === client.id}
                                                            onOpenChange={(open) => {
                                                                if (!open) setOpenModalForClientId(null);
                                                            }}
                                                            clientId={client.id}
                                                            clientName={client.name}
                                                        />
                                                    </Dialog>
                                                </TableCell>
                                                <TableCell>{client.name}</TableCell>
                                                <TableCell>{client.instagramUsername}</TableCell>
                                                <TableCell>{formatPhoneNumber(client.phone)}</TableCell>
                                                <TableCell className='text-right'>
                                                    <EditClient
                                                        client={client}
                                                        onClientUpdate={refreshData}
                                                        disabled={['COPY', 'DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR'].includes(userRole) || userAccessLevel === 'VIEWER'}
                                                    />
                                                </TableCell>
                                            </TableRow>
                                        ) : null
                                    )
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={5} className="text-center py-6">
                                            Nenhum cliente encontrado
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </>
                ) : (
                    <NotAllowed
                        page='/'
                    />
                )}
            </div>
            <Footer />
        </div >
    );
}
