"use client"

import { useEffect, useState } from "react"
import { useSession } from "next-auth/react"
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation"
import {
    AppWindowMac,
    MoveLeft,
    User,
    Hash,
    Calendar,
    Type,
    MapPin,
    CreditCard,
    Target,
    AlertCircle,
    Briefcase,
    SquarePen,
    Users,
    Images,
    PlusCircle
} from "lucide-react"
import { Header } from "@/app/components/header"
import { Footer } from "@/app/components/footer"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import Loading from "@/app/components/ui/loading"
import { NotAllowed } from "@/app/components/not-allowed"
import { ClientNavigationModal } from "@/app/components/client-navigation-modal"
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/app/components/ui/dialog"
import { Input } from "@/app/components/ui/input"
import { toast } from "sonner"
import { Textarea } from "@/app/components/ui/textarea"
import { Separator } from "@/app/components/ui/separator"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { MultiSelect } from "@/app/components/ui/multi-select";
import { OwnersManager } from "@/app/components/owners-manager"
import Image from "next/image"
import Link from "next/link"
import { formatPhoneOnInput, formatCnpjOnInput, isValidPhone, isValidCnpj } from "@/lib/formatters"

interface Address {
    id?: string;
    zipCode?: string;
    street?: string;
    number?: string;
    neighborhood?: string;
    city?: string;
    state?: string;
    country?: string;
}

interface Owner {
    id?: string;
    name: string;
    cpf?: string;
    birthDate?: Date | string;
    rg?: string;
    issuingAgency?: string;
    maritalStatus?: string;
    nationality?: string;
    profession?: string;
    isPrimary?: boolean;
    clientId?: string;
    addressId?: string;
    address?: Address;
}

interface Calendar {
    id: string;
    name: string;
    description?: string;
    color?: string;
    clientId: string;
    createdAt: Date | string;
    updatedAt: Date | string;
    archived: boolean;
    events?: Event[];
}

interface Event {
    id: string;
    title: string;
    description?: string;
    startDate: Date | string;
    endDate?: Date | string;
    allDay: boolean;
    location?: string;
    calendarId: string;
    createdAt: Date | string;
    updatedAt: Date | string;
}

interface Client {
    id: string;
    name: string;
    companyName?: string
    tradingName?: string;
    instagramUsername?: string;
    phone?: string;
    monthlyPostsLimit?: number;
    monthlyStoriesLimit?: number;
    additionalContent?: number;
    cnpj?: string;
    brandAnniversary?: Date | string;
    mainTypography?: string;
    secondaryTypography?: string;
    importantDetails?: string;
    restrictions?: string;
    profileDescription?: string;
    segment?: string[];
    services?: string[];
    products?: string[];
    storeAddress?: Address;
    factoryAddress?: Address;
    storeAddressId?: string;
    factoryAddressId?: string;
    paymentMethod?: string;
    paymentDay?: number;
    mission?: string;
    vision?: string;
    values?: string;
    owners?: Owner[];
    urlLogoGoogleDrive?: string;
}

const PAYMENT_METHODS = [
    { value: "PIX", label: "PIX" },
    { value: "Transferência bancária", label: "Transferência bancária" },
    { value: "Boleto", label: "Boleto" },
    { value: "Cartão de crédito", label: "Cartão de crédito" },
    { value: "Dinheiro", label: "Dinheiro" },
    { value: "Outro", label: "Outro" }
];

const SEGMENTS = [
    { value: "Alimentação", label: "Alimentação" },
    { value: "Moda", label: "Moda" },
    { value: "Tecnologia", label: "Tecnologia" },
    { value: "Saúde", label: "Saúde" },
    { value: "Beleza", label: "Beleza" },
    { value: "Educação", label: "Educação" },
    { value: "Entretenimento", label: "Entretenimento" },
    { value: "Serviços", label: "Serviços" },
    { value: "Varejo", label: "Varejo" },
    { value: "Loja de Moda Fitness", label: "Loja de Moda Fitness" },
    { value: "Pizzaria", label: "Pizzaria" },
    { value: 'Lavagem de carros', label: "Lavagem de carros" },
    { value: 'Loja de departamentos', label: "Loja de departamentos" },
    { value: 'Corretor(a) de imóveis', label: "Corretor(a) de imóveis" },
    { value: "Loja de alfaiataria", label: "Loja de alfaiataria" },
    { value: "Outro", label: "Outro" }
];

const SERVICES = [
    { value: "Consultoria", label: "Consultoria" },
    { value: "Desenvolvimento", label: "Desenvolvimento" },
    { value: "Design", label: "Design" },
    { value: "Marketing", label: "Marketing" },
    { value: "Vendas", label: "Vendas" },
    { value: "Suporte", label: "Suporte" },
    { value: "Treinamento", label: "Treinamento" },
    { value: "Outro", label: "Outro" }
];

const PRODUCTS = [
    { value: "Alimentos", label: "Alimentos" },
    { value: "Bebidas", label: "Bebidas" },
    { value: "Roupas", label: "Roupas" },
    { value: "Calçados", label: "Calçados" },
    { value: "Acessórios", label: "Acessórios" },
    { value: "Eletrônicos", label: "Eletrônicos" },
    { value: "Móveis", label: "Móveis" },
    { value: "Decoração", label: "Decoração" },
    { value: "Cosméticos", label: "Cosméticos" },
    { value: "Outro", label: "Outro" }
];

const extractGoogleDriveId = (url: string): string | null => {
    if (!url) return null;

    const match1 = url.match(/\/d\/([^/]+)/);
    if (match1) return match1[1];

    const match2 = url.match(/id=([^&]+)/);
    if (match2) return match2[1];

    return null;
};

export default function ClientPage() {
    const { data: session, status } = useSession()
    const router = useRouter()
    const { id } = useParams()
    const [isAdmin, setIsAdmin] = useState(false)
    const [isLoading, setIsLoading] = useState(true)
    const [isEditor, setIsEditor] = useState(false)
    const [client, setClient] = useState<Client & {
        storeAddress?: Address | null;
        factoryAddress?: Address | null;
        owners?: Owner[];
    } | null>(null)
    const [clientNavigationOpen, setClientNavigationOpen] = useState(false)
    const [calendars, setCalendars] = useState<Calendar[]>([])
    const [isLoadingCalendars, setIsLoadingCalendars] = useState(true)

    const [editingSection, setEditingSection] = useState<string | null>(null)
    const [editFormData, setEditFormData] = useState<Partial<Client & {
        storeAddress?: Partial<Address> | null;
        factoryAddress?: Partial<Address> | null;
        owners?: Owner[];
    }>>({})
    const [driveId, setDriveId] = useState<string | null>(null);
    const [isSubmitting, setIsSubmitting] = useState(false)

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/")
        }
    }, [status, router])

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`)
                    if (response.ok) {
                        const user = await response.json()
                        setIsAdmin(["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"].includes(user?.role || ""))
                        setIsEditor(user.accessLevel === "EDITOR")
                    }
                }
                setIsLoading(false)
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error)
                setIsLoading(false)
            }
        }

        if (session?.user?.email) {
            fetchUserRole()
        }
    }, [session])

    useEffect(() => {
        const fetchClient = async () => {
            if (id) {
                try {
                    const response = await fetch(`/api/clients/${id}`)
                    if (response.ok) {
                        const data = await response.json()
                        setClient(data)
                    }
                } catch (error) {
                    console.error("Erro ao buscar dados do cliente:", error)
                }
                setIsLoading(false)
            }
        }

        fetchClient()
    }, [id])

    useEffect(() => {
        const fetchCalendars = async () => {
            if (id) {
                try {
                    setIsLoadingCalendars(true)
                    const response = await fetch(`/api/calendars?clientId=${id}`)
                    if (response.ok) {
                        const data = await response.json()
                        setCalendars(data)
                    }
                } catch (error) {
                    console.error("Erro ao buscar calendários:", error)
                } finally {
                    setIsLoadingCalendars(false)
                }
            }
        }

        if (!isLoading && (isAdmin || isEditor)) {
            fetchCalendars()
        }
    }, [id, isLoading, isAdmin, isEditor])

    const handleOpenClientNavigation = () => {
        setClientNavigationOpen(true)
    }

    useEffect(() => {
        if (client?.urlLogoGoogleDrive) {
            setDriveId(extractGoogleDriveId(client.urlLogoGoogleDrive));
        }
    }, [client]);

    const formatDate = (date: Date | string | null | undefined) => {
        if (!date) return "Não informado"
        const dateObj = new Date(date)
        return dateObj.toLocaleDateString("pt-BR")
    }

    const formatPaymentDay = (day: number | null | undefined) => {
        if (!day) return "Não informado"
        return `Todo dia ${day}`
    }

    const handleOpenEditModal = (section: string) => {
        if (client) {
            setEditFormData({
                ...client,
                storeAddress: client.storeAddress || {},
                factoryAddress: client.factoryAddress || {}
            });
            setEditingSection(section);
        }
    }

    const handleCloseEditModal = () => {
        setEditingSection(null)
        setEditFormData({})
    }

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target

        if (name === 'phone') {
            setEditFormData(prev => ({
                ...prev,
                [name]: formatPhoneOnInput(value)
            }));
        } else if (name === 'cnpj') {
            setEditFormData(prev => ({
                ...prev,
                [name]: formatCnpjOnInput(value)
            }));
        } else if (['monthlyPostsLimit', 'monthlyStoriesLimit', 'additionalContent', 'paymentDay'].includes(name)) {
            const numValue = Number(value);
            if (name === 'paymentDay') {
                const validDay = !isNaN(numValue) ? Math.min(Math.max(numValue, 1), 31) : '';
                setEditFormData(prev => ({
                    ...prev,
                    [name]: validDay === '' ? undefined : validDay
                }));
            } else {
                setEditFormData(prev => ({
                    ...prev,
                    [name]: numValue || 0
                }));
            }
        } else if (['brandAnniversary'].includes(name)) {
            setEditFormData(prev => ({
                ...prev,
                [name]: value
            }));
        } else {
            setEditFormData(prev => ({
                ...prev,
                [name]: value
            }));
        }
    }

    const handleSaveChanges = async () => {
        if (!client) return;

        if (editFormData.phone && !isValidPhone(editFormData.phone)) {
            toast.error("O número de telefone deve ter 10 ou 11 dígitos, incluindo o DDD.");
            return;
        }

        if (editFormData.cnpj && !isValidCnpj(editFormData.cnpj)) {
            toast.error("CNPJ inválido. Verifique o formato e os dígitos verificadores.");
            return;
        }

        setIsSubmitting(true);

        try {
            const processedData = { ...editFormData };
            
            if (editFormData.owners && editFormData.owners.length > 0) {
                processedData.owners = editFormData.owners.map(owner => {
                    const cleanOwnerData = { ...owner };

                    if ('clientId' in cleanOwnerData) delete cleanOwnerData.clientId;
                    if ('addressId' in cleanOwnerData) delete cleanOwnerData.addressId;
                    if ('createdAt' in cleanOwnerData) delete cleanOwnerData.createdAt;
                    if ('updatedAt' in cleanOwnerData) delete cleanOwnerData.updatedAt;

                    if (cleanOwnerData.birthDate) {
                        try {
                            if (typeof cleanOwnerData.birthDate === 'string' && !cleanOwnerData.birthDate.includes('T')) {
                                cleanOwnerData.birthDate = cleanOwnerData.birthDate;
                            } else {
                                const date = new Date(cleanOwnerData.birthDate as string | number | Date);
                                cleanOwnerData.birthDate = date.toISOString().split('T')[0];
                            }
                        } catch {
                            delete cleanOwnerData.birthDate;
                        }
                    }

                    const cleanAddress = owner.address ? { ...owner.address } : undefined;
                    if (cleanAddress) {
                        if ('id' in cleanAddress) delete cleanAddress.id;
                        if ('createdAt' in cleanAddress) delete cleanAddress.createdAt;
                        if ('updatedAt' in cleanAddress) delete cleanAddress.updatedAt;
                    }

                    return {
                        ...cleanOwnerData,
                        address: cleanAddress
                    };
                });
            }

            const response = await fetch(`/api/clients/${client.id}`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(processedData),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || "Erro ao salvar alterações");
            }

            const updatedClient = await response.json();
            setClient(updatedClient);
            setEditingSection(null);
            setEditFormData({});
            
            toast.success("Alterações salvas com sucesso!", {
                position: "bottom-left"
            });
        } catch (error) {
            console.error("Erro ao salvar alterações:", error);
            toast.error("Erro ao salvar alterações.");
        } finally {
            setIsSubmitting(false);
        }
    }

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isLoading ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <>
                        <div className="flex justify-between items-center mb-8">
                            <div className="flex items-center gap-2">
                                <Button variant="outline" size="icon" onClick={() => router.push("/clients")}>
                                    <MoveLeft />
                                </Button>
                                <Button variant="outline" size="icon" onClick={handleOpenClientNavigation}>
                                    <AppWindowMac className="h-4 w-4" />
                                </Button>
                            </div>

                            <ClientNavigationModal
                                open={clientNavigationOpen}
                                onOpenChange={setClientNavigationOpen}
                                clientId={id as string}
                                clientName={client?.name}
                            />
                            <div className="flex items-center gap-3 group">
                                <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                    Perfil do cliente
                                </h1>
                                <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                    <User size={24} color="#db5743" />
                                </div>
                            </div>
                        </div>

                        <Dialog
                            open={!!editingSection}
                            onOpenChange={(open) => !open && handleCloseEditModal()}
                        >
                            <DialogContent className="sm:max-w-[500px] overflow-y-auto max-h-[90vh]">
                                <DialogHeader>
                                    <DialogTitle>
                                        Editar
                                        <Separator className="my-2" />
                                        <p>
                                            {editingSection === 'basic' && 'Informações básicas'}
                                            {editingSection === 'content' && 'Limites de conteúdo'}
                                            {editingSection === 'personal' && 'Dados pessoais'}
                                            {editingSection === 'dates' && 'Datas importantes'}
                                            {editingSection === 'visual' && 'Identidade visual'}
                                            {editingSection === 'description' && 'Descrição e segmento'}
                                            {editingSection === 'addresses' && 'Endereços'}
                                            {editingSection === 'owners' && null}
                                            {editingSection === 'payment' && 'Informações de pagamento'}
                                            {editingSection === 'values' && 'Valores da empresa'}
                                            {editingSection === 'notes' && 'Observações'}
                                        </p>
                                    </DialogTitle>
                                </DialogHeader>

                                <div className="grid gap-4 py-4">
                                    {editingSection === 'basic' && (
                                        <>
                                            <div>
                                                <label htmlFor="urlLogoGoogleDrive" className="block text-sm font-medium mb-1">URL da logo no Google Drive</label>
                                                <Input
                                                    id="urlLogoGoogleDrive"
                                                    name="urlLogoGoogleDrive"
                                                    value={editFormData.urlLogoGoogleDrive || ''}
                                                    onChange={handleInputChange}
                                                    placeholder="Cole aqui a URL do Google Drive"
                                                />
                                                <p className="text-xs text-muted-foreground mt-1">
                                                    Exemplo: https://drive.google.com/file/d/XXXXXXXX/view
                                                </p>
                                            </div>
                                            <div>
                                                <label htmlFor="name" className="block text-sm font-medium mb-1">Nome</label>
                                                <Input id="name" name="name" value={editFormData.name || ''} onChange={handleInputChange} />
                                            </div>
                                            <div>
                                                <label htmlFor="instagramUsername" className="block text-sm font-medium mb-1">Instagram</label>
                                                <Input id="instagramUsername" name="instagramUsername" value={editFormData.instagramUsername || ''} onChange={handleInputChange} />
                                            </div>
                                            <div>
                                                <label htmlFor="phone" className="block text-sm font-medium mb-1">Telefone</label>
                                                <Input id="phone" name="phone" value={editFormData.phone || ''} onChange={handleInputChange} />
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'content' && (
                                        <>
                                            <div>
                                                <label htmlFor="monthlyPostsLimit" className="block text-sm font-medium mb-1">Limite de Posts</label>
                                                <Input id="monthlyPostsLimit" name="monthlyPostsLimit" type="number" value={editFormData.monthlyPostsLimit || 0} onChange={handleInputChange} />
                                            </div>
                                            <div>
                                                <label htmlFor="monthlyStoriesLimit" className="block text-sm font-medium mb-1">Limite de Stories</label>
                                                <Input id="monthlyStoriesLimit" name="monthlyStoriesLimit" type="number" value={editFormData.monthlyStoriesLimit || 0} onChange={handleInputChange} />
                                            </div>
                                            <div>
                                                <label htmlFor="additionalContent" className="block text-sm font-medium mb-1">Conteúdo Adicional</label>
                                                <Input id="additionalContent" name="additionalContent" type="number" value={editFormData.additionalContent || 0} onChange={handleInputChange} />
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'personal' && (
                                        <>
                                            <div>
                                                <label htmlFor="companyName" className="block text-sm font-medium mb-1">Razão Social</label>
                                                <Input id="companyName" name="companyName" value={editFormData.companyName || ''} onChange={handleInputChange} />
                                            </div>
                                            <div>
                                                <label htmlFor="tradingName" className="block text-sm font-medium mb-1">Nome Fantasia</label>
                                                <Input id="tradingName" name="tradingName" value={editFormData.tradingName || ''} onChange={handleInputChange} />
                                            </div>
                                            <div>
                                                <label htmlFor="cnpj" className="block text-sm font-medium mb-1">CNPJ</label>
                                                <Input id="cnpj" name="cnpj" value={editFormData.cnpj || ''} onChange={handleInputChange} />
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'dates' && (
                                        <>
                                            <div>
                                                <label htmlFor="brandAnniversary" className="block text-sm font-medium mb-1">Aniversário da marca</label>
                                                <Input
                                                    id="brandAnniversary"
                                                    name="brandAnniversary"
                                                    type="date"
                                                    value={
                                                        (() => {
                                                            if (!editFormData.brandAnniversary) return '';

                                                            if (typeof editFormData.brandAnniversary === 'string') {
                                                                if (editFormData.brandAnniversary.includes('T')) {
                                                                    return editFormData.brandAnniversary.split('T')[0];
                                                                }
                                                                return editFormData.brandAnniversary;
                                                            }

                                                            const date = editFormData.brandAnniversary as unknown as Date;
                                                            return date.toISOString().split('T')[0];
                                                        })()
                                                    }
                                                    onChange={handleInputChange}
                                                />
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'visual' && (
                                        <>
                                            <div>
                                                <label htmlFor="mainTypography" className="block text-sm font-medium mb-1">Tipografia principal</label>
                                                <Input id="mainTypography" name="mainTypography" value={editFormData.mainTypography || ''} onChange={handleInputChange} />
                                            </div>
                                            <div>
                                                <label htmlFor="secondaryTypography" className="block text-sm font-medium mb-1">Tipografia secundária</label>
                                                <Input id="secondaryTypography" name="secondaryTypography" value={editFormData.secondaryTypography || ''} onChange={handleInputChange} />
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'description' && (
                                        <>
                                            <div>
                                                <label htmlFor="profileDescription" className="block text-sm font-medium mb-1">Descrição do perfil</label>
                                                <Input id="profileDescription" name="profileDescription" value={editFormData.profileDescription || ''} onChange={handleInputChange} />
                                            </div>
                                            <div>
                                                <label htmlFor="segment" className="block text-sm font-medium mb-1">Segmento</label>
                                                <MultiSelect
                                                    options={SEGMENTS}
                                                    selected={editFormData.segment || []}
                                                    onChange={(values) => setEditFormData(prev => ({ ...prev, segment: values }))}
                                                    placeholder="Selecione segmentos"
                                                />
                                            </div>
                                            <div>
                                                <label htmlFor="services" className="block text-sm font-medium mb-1">Serviços</label>
                                                <MultiSelect
                                                    options={SERVICES}
                                                    selected={editFormData.services || []}
                                                    onChange={(values) => setEditFormData(prev => ({ ...prev, services: values }))}
                                                    placeholder="Selecione serviços"
                                                />
                                            </div>
                                            <div>
                                                <label htmlFor="products" className="block text-sm font-medium mb-1">Produtos</label>
                                                <MultiSelect
                                                    options={PRODUCTS}
                                                    selected={editFormData.products || []}
                                                    onChange={(values) => setEditFormData(prev => ({ ...prev, products: values }))}
                                                    placeholder="Selecione produtos"
                                                />
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'addresses' && (
                                        <>
                                            <div className="mb-4">
                                                <label className="block text-sm font-medium mb-1">Endereço da loja</label>
                                                <div className="grid grid-cols-2 gap-2 mb-2">
                                                    <div>
                                                        <label htmlFor="storeZipCode" className="block text-xs font-medium mb-1">CEP</label>
                                                        <Input
                                                            id="storeZipCode"
                                                            name="storeZipCode"
                                                            value={editFormData.storeAddress?.zipCode || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    storeAddress: {
                                                                        ...prev.storeAddress,
                                                                        zipCode: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="storeStreet" className="block text-xs font-medium mb-1">Rua</label>
                                                        <Input
                                                            id="storeStreet"
                                                            name="storeStreet"
                                                            value={editFormData.storeAddress?.street || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    storeAddress: {
                                                                        ...prev.storeAddress,
                                                                        street: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                                <div className="grid grid-cols-2 gap-2 mb-2">
                                                    <div>
                                                        <label htmlFor="storeNumber" className="block text-xs font-medium mb-1">Número</label>
                                                        <Input
                                                            id="storeNumber"
                                                            name="storeNumber"
                                                            value={editFormData.storeAddress?.number || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    storeAddress: {
                                                                        ...prev.storeAddress,
                                                                        number: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="storeNeighborhood" className="block text-xs font-medium mb-1">Bairro</label>
                                                        <Input
                                                            id="storeNeighborhood"
                                                            name="storeNeighborhood"
                                                            value={editFormData.storeAddress?.neighborhood || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    storeAddress: {
                                                                        ...prev.storeAddress,
                                                                        neighborhood: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                                <div className="grid grid-cols-2 gap-2">
                                                    <div>
                                                        <label htmlFor="storeCity" className="block text-xs font-medium mb-1">Cidade</label>
                                                        <Input
                                                            id="storeCity"
                                                            name="storeCity"
                                                            value={editFormData.storeAddress?.city || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    storeAddress: {
                                                                        ...prev.storeAddress,
                                                                        city: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="storeState" className="block text-xs font-medium mb-1">Estado</label>
                                                        <Input
                                                            id="storeState"
                                                            name="storeState"
                                                            value={editFormData.storeAddress?.state || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    storeAddress: {
                                                                        ...prev.storeAddress,
                                                                        state: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                            </div>

                                            <div className="mt-6">
                                                <label className="block text-sm font-medium mb-1">Endereço da fábrica</label>
                                                <div className="grid grid-cols-2 gap-2 mb-2">
                                                    <div>
                                                        <label htmlFor="factoryZipCode" className="block text-xs font-medium mb-1">CEP</label>
                                                        <Input
                                                            id="factoryZipCode"
                                                            name="factoryZipCode"
                                                            value={editFormData.factoryAddress?.zipCode || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    factoryAddress: {
                                                                        ...prev.factoryAddress,
                                                                        zipCode: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="factoryStreet" className="block text-xs font-medium mb-1">Rua</label>
                                                        <Input
                                                            id="factoryStreet"
                                                            name="factoryStreet"
                                                            value={editFormData.factoryAddress?.street || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    factoryAddress: {
                                                                        ...prev.factoryAddress,
                                                                        street: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                                <div className="grid grid-cols-2 gap-2 mb-2">
                                                    <div>
                                                        <label htmlFor="factoryNumber" className="block text-xs font-medium mb-1">Número</label>
                                                        <Input
                                                            id="factoryNumber"
                                                            name="factoryNumber"
                                                            value={editFormData.factoryAddress?.number || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    factoryAddress: {
                                                                        ...prev.factoryAddress,
                                                                        number: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="factoryNeighborhood" className="block text-xs font-medium mb-1">Bairro</label>
                                                        <Input
                                                            id="factoryNeighborhood"
                                                            name="factoryNeighborhood"
                                                            value={editFormData.factoryAddress?.neighborhood || ''} onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    factoryAddress: {
                                                                        ...prev.factoryAddress,
                                                                        neighborhood: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                                <div className="grid grid-cols-2 gap-2">
                                                    <div>
                                                        <label htmlFor="factoryCity" className="block text-xs font-medium mb-1">Cidade</label>
                                                        <Input
                                                            id="factoryCity"
                                                            name="factoryCity"
                                                            value={editFormData.factoryAddress?.city || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    factoryAddress: {
                                                                        ...prev.factoryAddress,
                                                                        city: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                    <div>
                                                        <label htmlFor="factoryState" className="block text-xs font-medium mb-1">Estado</label>
                                                        <Input
                                                            id="factoryState"
                                                            name="factoryState"
                                                            value={editFormData.factoryAddress?.state || ''}
                                                            onChange={(e) => {
                                                                setEditFormData(prev => ({
                                                                    ...prev,
                                                                    factoryAddress: {
                                                                        ...prev.factoryAddress,
                                                                        state: e.target.value
                                                                    }
                                                                }));
                                                            }}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'payment' && (
                                        <>
                                            <div>
                                                <label htmlFor="paymentMethod" className="block text-sm font-medium mb-1">Método de pagamento</label>
                                                <Select
                                                    value={editFormData.paymentMethod || ''}
                                                    onValueChange={(value) => setEditFormData(prev => ({ ...prev, paymentMethod: value }))}
                                                >
                                                    <SelectTrigger className="w-full" id="paymentMethod">
                                                        <SelectValue placeholder="Selecione um método de pagamento" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {PAYMENT_METHODS.map((option) => (
                                                            <SelectItem key={option.value} value={option.value}>
                                                                {option.label}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            </div>
                                            <div>
                                                <label htmlFor="paymentDay" className="block text-sm font-medium mb-1">Dia de pagamento</label>
                                                <Input
                                                    id="paymentDay"
                                                    name="paymentDay"
                                                    type="number"
                                                    min="1"
                                                    max="31"
                                                    value={editFormData.paymentDay || ''}
                                                    onChange={handleInputChange}
                                                />
                                                <p className="text-xs text-muted-foreground mt-1">
                                                    Informe o dia do mês (1-31) em que o pagamento deve ser realizado
                                                </p>
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'values' && (
                                        <>
                                            <div>
                                                <label htmlFor="mission" className="block text-sm font-medium mb-1">Missão</label>
                                                <Textarea id="mission" name="mission" value={editFormData.mission || ''} onChange={handleInputChange} rows={6} />
                                            </div>
                                            <div>
                                                <label htmlFor="vision" className="block text-sm font-medium mb-1">Visão</label>
                                                <Textarea id="vision" name="vision" value={editFormData.vision || ''} onChange={handleInputChange} rows={6} />
                                            </div>
                                            <div>
                                                <label htmlFor="values" className="block text-sm font-medium mb-1">Valores</label>
                                                <Textarea id="values" name="values" value={editFormData.values || ''} onChange={handleInputChange} rows={6} />
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'notes' && (
                                        <>
                                            <div>
                                                <label htmlFor="importantDetails" className="block text-sm font-medium mb-1">Detalhes importantes</label>
                                                <Textarea id="importantDetails" name="importantDetails" value={editFormData.importantDetails || ''} onChange={handleInputChange} rows={6} />
                                            </div>
                                            <div>
                                                <label htmlFor="restrictions" className="block text-sm font-medium mb-1">Restrições</label>
                                                <Textarea id="restrictions" name="restrictions" value={editFormData.restrictions || ''} onChange={handleInputChange} rows={6} />
                                            </div>
                                        </>
                                    )}

                                    {editingSection === 'owners' && (
                                        <>
                                            <OwnersManager
                                                owners={editFormData.owners || []}
                                                onChange={(owners) => setEditFormData(prev => ({ ...prev, owners }))}
                                            />
                                        </>
                                    )}
                                </div>

                                <div className="flex justify-end gap-2">
                                    <Button variant="outline" onClick={handleCloseEditModal}>
                                        Cancelar
                                    </Button>
                                    <Button onClick={handleSaveChanges} disabled={isSubmitting}>
                                        Salvar
                                    </Button>
                                </div>
                            </DialogContent>
                        </Dialog>

                        {client && (
                            <div className="space-y-6">
                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <User className="h-5 w-5" />
                                                Informações básicas
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('basic')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                                        <div>
                                            {client.urlLogoGoogleDrive ? (
                                                <div className="flex flex-col gap-4 items-start">
                                                    <Image
                                                        src={`/api/drive-proxy?id=${driveId}&quality=high&size=large`}
                                                        alt="Logo do cliente"
                                                        width={100}
                                                        height={70}
                                                        className="max-w-full max-h-full object-contain"
                                                        style={{
                                                            width: 'auto',
                                                            height: 'auto',
                                                            maxWidth: '100%',
                                                            maxHeight: '100%'
                                                        }}
                                                    />
                                                    <Link
                                                        href={client.urlLogoGoogleDrive}
                                                        target="_blank"
                                                        className="text-xs text-blue-500 hover:underline flex items-center gap-1"
                                                    >
                                                        <svg role="img" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg" width={12} height={12} fill="oklch(62.3% 0.214 259.815)"><title>Google Drive</title><path d="M12.01 1.485c-2.082 0-3.754.02-3.743.047.01.02 1.708 3.001 3.774 6.62l3.76 6.574h3.76c2.081 0 3.753-.02 3.742-.047-.005-.02-1.708-3.001-3.775-6.62l-3.76-6.574zm-4.76 1.73a789.828 789.861 0 0 0-3.63 6.319L0 15.868l1.89 3.298 1.885 3.297 3.62-6.335 3.618-6.33-1.88-3.287C8.1 4.704 7.255 3.22 7.25 3.214zm2.259 12.653-.203.348c-.114.198-.96 1.672-1.88 3.287a423.93 423.948 0 0 1-1.698 2.97c-.01.026 3.24.042 7.222.042h7.244l1.796-3.157c.992-1.734 1.85-3.23 1.906-3.323l.104-.167h-7.249z" /></svg>
                                                        Ver no Google Drive
                                                    </Link>
                                                </div>
                                            ) : (
                                                <>
                                                    <Images />
                                                    <span>
                                                        <p className="text-sm text-muted-foreground">Nenhuma logo cadastrada</p>
                                                    </span>
                                                </>
                                            )}
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Nome</label>
                                            <p className="text-sm font-semibold">{client.name || "Não informado"}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Instagram</label>
                                            <p className="text-sm">
                                                {client.instagramUsername ? `@${client.instagramUsername}` : "Não informado"}
                                            </p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Telefone</label>
                                            <p className="text-sm">{client.phone || "Não informado"}</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <Hash className="h-5 w-5" />
                                                Limites de conteúdo
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('content')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        <div className="text-center p-4 bg-muted/50 rounded-lg">
                                            <p className="text-2xl font-bold text-primary">{client.monthlyPostsLimit || 0}</p>
                                            <p className="text-sm text-muted-foreground">Posts mensais</p>
                                        </div>
                                        <div className="text-center p-4 bg-muted/50 rounded-lg">
                                            <p className="text-2xl font-bold text-primary">{client.monthlyStoriesLimit || 0}</p>
                                            <p className="text-sm text-muted-foreground">Stories mensais</p>
                                        </div>
                                        <div className="text-center p-4 bg-muted/50 rounded-lg">
                                            <p className="text-2xl font-bold text-primary">{client.additionalContent || 0}</p>
                                            <p className="text-sm text-muted-foreground">Conteúdo adicional</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <Briefcase className="h-5 w-5" />
                                                Dados empresariais
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('personal')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Razão Social</label>
                                            <p className="text-sm">{client.companyName || "Não informado"}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Nome Fantasia</label>
                                            <p className="text-sm">{client.tradingName || "Não informado"}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">CNPJ</label>
                                            <p className="text-sm">{client.cnpj || "Não informado"}</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <Users className="h-5 w-5" />
                                                Proprietários
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('owners')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {!client.owners || client.owners.length === 0 ? (
                                            <p className="text-sm text-muted-foreground">Nenhum proprietário cadastrado</p>
                                        ) : (
                                            <div className="space-y-4">
                                                {client.owners.map((owner, index) => (
                                                    <div key={owner.id || index} className="border rounded-lg p-4">
                                                        <div className="flex items-center gap-2 mb-2">
                                                            <span className="text-sm font-medium">{owner.name}</span>
                                                            {owner.isPrimary && (
                                                                <span className="bg-primary/10 text-primary text-xs rounded-full px-2 py-0.5">
                                                                    Principal
                                                                </span>
                                                            )}
                                                        </div>
                                                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm">
                                                            <div>
                                                                <label className="text-xs font-medium text-muted-foreground">CPF</label>
                                                                <p>{owner.cpf || "Não informado"}</p>
                                                            </div>
                                                            <div>
                                                                <label className="text-xs font-medium text-muted-foreground">Data de nascimento</label>
                                                                <p>{owner.birthDate ? formatDate(owner.birthDate) : "Não informado"}</p>
                                                            </div>
                                                            {owner.rg && (
                                                                <div>
                                                                    <label className="text-xs font-medium text-muted-foreground">RG</label>
                                                                    <p>{owner.rg}</p>
                                                                </div>
                                                            )}
                                                            {owner.issuingAgency && (
                                                                <div>
                                                                    <label className="text-xs font-medium text-muted-foreground">Órgão emissor</label>
                                                                    <p>{owner.issuingAgency}</p>
                                                                </div>
                                                            )}
                                                            {owner.maritalStatus && (
                                                                <div>
                                                                    <label className="text-xs font-medium text-muted-foreground">Estado civil</label>
                                                                    <p>{owner.maritalStatus}</p>
                                                                </div>
                                                            )}
                                                            {owner.nationality && (
                                                                <div>
                                                                    <label className="text-xs font-medium text-muted-foreground">Nacionalidade</label>
                                                                    <p>{owner.nationality}</p>
                                                                </div>
                                                            )}
                                                            {owner.profession && (
                                                                <div>
                                                                    <label className="text-xs font-medium text-muted-foreground">Profissão</label>
                                                                    <p>{owner.profession}</p>
                                                                </div>
                                                            )}
                                                        </div>

                                                        {owner.address && (
                                                            <div className="mt-4 border-t pt-3">
                                                                <p className="text-xs font-medium text-muted-foreground mb-2">Endereço</p>
                                                                <p className="text-sm">
                                                                    {[
                                                                        owner.address.street,
                                                                        owner.address.number && `nº ${owner.address.number}`,
                                                                        owner.address.neighborhood
                                                                    ].filter(Boolean).join(', ')}
                                                                </p>
                                                                <p className="text-sm">
                                                                    {[
                                                                        owner.address.city,
                                                                        owner.address.state,
                                                                        owner.address.zipCode && `CEP: ${owner.address.zipCode}`
                                                                    ].filter(Boolean).join(' - ')}
                                                                </p>
                                                            </div>
                                                        )}
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <Calendar className="h-5 w-5" />
                                                Datas importantes
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('dates')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Aniversário da marca</label>
                                            <p className="text-sm">{formatDate(client.brandAnniversary)}</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <Type className="h-5 w-5" />
                                                Identidade visual
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('visual')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Tipografia principal</label>
                                            <p className="text-sm">{client.mainTypography || "Não informado"}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Tipografia secundária</label>
                                            <p className="text-sm">{client.secondaryTypography || "Não informado"}</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <Briefcase className="h-5 w-5" />
                                                Descrição e segmento
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('description')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Descrição do perfil</label>
                                            <p className="text-sm mt-1">{client.profileDescription || "Não informado"}</p>
                                        </div>
                                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                            <div>
                                                <label className="text-sm font-medium text-muted-foreground">Segmento</label>
                                                <div className="mt-1">
                                                    {client.segment && client.segment.length > 0 ? (
                                                        <div className="flex flex-wrap gap-1">
                                                            {client.segment.map((item, index) => (
                                                                <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary">
                                                                    {item}
                                                                </span>
                                                            ))}
                                                        </div>
                                                    ) : (
                                                        <p className="text-sm">Não informado</p>
                                                    )}
                                                </div>
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium text-muted-foreground">Serviços</label>
                                                <div className="mt-1">
                                                    {client.services && client.services.length > 0 ? (
                                                        <div className="flex flex-wrap gap-1">
                                                            {client.services.map((item, index) => (
                                                                <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-secondary/50 text-secondary-foreground">
                                                                    {item}
                                                                </span>
                                                            ))}
                                                        </div>
                                                    ) : (
                                                        <p className="text-sm">Não informado</p>
                                                    )}
                                                </div>
                                            </div>
                                            <div>
                                                <label className="text-sm font-medium text-muted-foreground">Produtos</label>
                                                <div className="mt-1">
                                                    {client.products && client.products.length > 0 ? (
                                                        <div className="flex flex-wrap gap-1">
                                                            {client.products.map((item, index) => (
                                                                <span key={index} className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-accent text-accent-foreground">
                                                                    {item}
                                                                </span>
                                                            ))}
                                                        </div>
                                                    ) : (
                                                        <p className="text-sm">Não informado</p>
                                                    )}
                                                </div>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <MapPin className="h-5 w-5" />
                                                Endereços
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('addresses')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Endereço da loja</label>
                                            {client.storeAddress ? (
                                                <div className="space-y-1 mt-1">
                                                    <p className="text-sm">{client.storeAddress.street}, {client.storeAddress.number}</p>
                                                    <p className="text-sm">{client.storeAddress.neighborhood} - CEP: {client.storeAddress.zipCode}</p>
                                                    <p className="text-sm">{client.storeAddress.city}/{client.storeAddress.state}</p>
                                                </div>
                                            ) : (
                                                <p className="text-sm">Não informado</p>
                                            )}
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Endereço da fábrica</label>
                                            {client.factoryAddress ? (
                                                <div className="space-y-1 mt-1">
                                                    <p className="text-sm">{client.factoryAddress.street}, {client.factoryAddress.number}</p>
                                                    <p className="text-sm">{client.factoryAddress.neighborhood} - CEP: {client.factoryAddress.zipCode}</p>
                                                    <p className="text-sm">{client.factoryAddress.city}/{client.factoryAddress.state}</p>
                                                </div>
                                            ) : (
                                                <p className="text-sm">Não informado</p>
                                            )}
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <CreditCard className="h-5 w-5" />
                                                Informações de pagamento
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('payment')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Método de pagamento</label>
                                            <p className="text-sm">{client.paymentMethod || "Não informado"}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Dia de pagamento</label>
                                            <p className="text-sm">{formatPaymentDay(client.paymentDay)}</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <Target className="h-5 w-5" />
                                                Valores da empresa
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('values')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Missão</label>
                                            <p className="text-sm mt-1">{client.mission || "Não informado"}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Visão</label>
                                            <p className="text-sm mt-1">{client.vision || "Não informado"}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Valores</label>
                                            <p className="text-sm mt-1">{client.values || "Não informado"}</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <AlertCircle className="h-5 w-5" />
                                                Observações
                                            </div>
                                            <Button variant="outline" onClick={() => handleOpenEditModal('notes')} className={!isEditor ? "hidden" : ""}>
                                                <SquarePen className="h-4 w-4" />
                                                Editar
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent className="space-y-4">
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Detalhes importantes</label>
                                            <p className="text-sm mt-1">{client.importantDetails || "Não informado"}</p>
                                        </div>
                                        <div>
                                            <label className="text-sm font-medium text-muted-foreground">Restrições</label>
                                            <p className="text-sm mt-1">{client.restrictions || "Não informado"}</p>
                                        </div>
                                    </CardContent>
                                </Card>

                                <Card>
                                    <CardHeader>
                                        <CardTitle className="flex flex-col sm:flex-row items-start sm:items-center gap-2 justify-between">
                                            <div className="flex items-center gap-2">
                                                <Calendar className="h-5 w-5" />
                                                Calendários personalizados
                                            </div>
                                            <Button 
                                                variant="outline" 
                                                onClick={() => router.push(`/clients/${id}/calendars`)}
                                                className={!isEditor || calendars.length === 0 ? "hidden" : ""}
                                            >
                                                <PlusCircle className="h-4 w-4" />
                                                Gerenciar calendários
                                            </Button>
                                        </CardTitle>
                                    </CardHeader>
                                    <CardContent>
                                        {isLoadingCalendars ? (
                                            <div className="flex justify-center py-4">
                                                <Loading />
                                            </div>
                                        ) : calendars.length === 0 ? (
                                            <div className="text-center p-8">
                                                <Calendar className="mx-auto text-muted-foreground" />
                                                <h3 className="font-medium">Nenhum calendário encontrado</h3>
                                                <p className="text-muted-foreground mb-4">
                                                    Crie um novo calendário para gerenciar eventos e datas importantes.
                                                </p>
                                                <Button
                                                    onClick={() => router.push(`/clients/${id}/calendars`)}
                                                >
                                                    <PlusCircle size={16} />
                                                    Criar calendário
                                                </Button>
                                            </div>
                                        ) : (
                                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                {calendars.slice(0, 3).map((calendar) => (
                                                    <Card key={calendar.id} className="overflow-hidden">
                                                        <div
                                                            className="h-2"
                                                            style={{ backgroundColor: calendar.color || "#4285F4" }}
                                                        ></div>
                                                        <CardHeader className="pb-2">
                                                            <CardTitle className="text-sm">{calendar.name}</CardTitle>
                                                        </CardHeader>
                                                        <CardContent>
                                                            {calendar.description && (
                                                                <p className="text-xs text-muted-foreground mb-4 line-clamp-2">
                                                                    {calendar.description}
                                                                </p>
                                                            )}
                                                            <Button
                                                                variant="outline"
                                                                className="w-full text-xs"
                                                                onClick={() => router.push(`/clients/${id}/calendars/${calendar.id}`)}
                                                            >
                                                                Ver eventos
                                                            </Button>
                                                        </CardContent>
                                                    </Card>
                                                ))}
                                                {calendars.length > 3 && (
                                                    <Card className="flex items-center justify-center">
                                                        <CardContent className="text-center p-6">
                                                            <Button
                                                                variant="link"
                                                                onClick={() => router.push(`/clients/${id}/calendars`)}
                                                            >
                                                                Ver todos os {calendars.length} calendários
                                                            </Button>
                                                        </CardContent>
                                                    </Card>
                                                )}
                                            </div>
                                        )}
                                    </CardContent>
                                </Card>
                            </div>
                        )}
                    </>
                ) : (
                    <NotAllowed page="clients" />
                )}
            </div>
            <Footer />
        </div>
    )
}
