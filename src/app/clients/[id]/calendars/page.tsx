"use client"

import { useState, useEffect } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { Calendar as CalendarIcon, PlusCircle, Edit, Trash2, MoveLeft } from "lucide-react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/app/components/ui/alert-dialog";
import { toast } from "sonner";
import { Header } from "@/app/components/header";
import { Footer } from "@/app/components/footer";
import Loading from "@/app/components/ui/loading";
import { NotAllowed } from "@/app/components/not-allowed";
import { Calendar } from "@/types/calendar";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { Switch } from "@/app/components/ui/switch";
import { CalendarBreadcrumb } from "@/app/components/calendar-breadcrumb";

export default function CalendarsPage() {
    const { data: session, status } = useSession();
    const router = useRouter();
    const { id } = useParams();

    const [isAdmin, setIsAdmin] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [clientName, setClientName] = useState("");
    const [calendars, setCalendars] = useState<Calendar[]>([]);
    const [allCalendars, setAllCalendars] = useState<Calendar[]>([]);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [useExistingCalendar, setUseExistingCalendar] = useState(false);
    const [selectedSourceCalendarId, setSelectedSourceCalendarId] = useState("");

    const [formData, setFormData] = useState({
        name: "",
        description: "",
        color: "#4285F4",
    });

    const [editingCalendar, setEditingCalendar] = useState<Calendar | null>(null);

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        setIsAdmin(["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"].includes(user?.role || ""));
                    }
                }
                setIsLoading(false);
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
                setIsLoading(false);
            }
        };

        if (session?.user?.email) {
            fetchUserRole();
        }
    }, [session]);

    useEffect(() => {
        const fetchClientData = async () => {
            if (id) {
                try {
                    const response = await fetch(`/api/clients/${id}`);
                    if (response.ok) {
                        const data = await response.json();
                        setClientName(data.name);
                    }
                } catch (error) {
                    console.error("Erro ao buscar dados do cliente:", error);
                }
            }
        };

        fetchClientData();
    }, [id]);

    useEffect(() => {
        const fetchCalendars = async () => {
            if (id) {
                try {
                    const response = await fetch(`/api/calendars?clientId=${id}`);
                    if (response.ok) {
                        const data = await response.json();
                        setCalendars(data);
                    }
                } catch (error) {
                    console.error("Erro ao buscar calendários:", error);
                }
                setIsLoading(false);
            }
        };

        fetchCalendars();
    }, [id]);

    useEffect(() => {
        const fetchAllCalendars = async () => {
            try {
                const response = await fetch(`/api/calendars?all=true`);
                if (response.ok) {
                    const data = await response.json();
                    setAllCalendars(data);
                }
            } catch (error) {
                console.error("Erro ao buscar todos os calendários:", error);
            }
        };

        fetchAllCalendars();
    }, []);

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleSwitchChange = (checked: boolean) => {
        setUseExistingCalendar(checked);
        if (!checked) {
            setSelectedSourceCalendarId("");
            setFormData({
                name: "",
                description: "",
                color: "#4285F4",
            });
        }
    };

    const handleSourceCalendarChange = (value: string) => {
        setSelectedSourceCalendarId(value);

        const selectedCalendar = allCalendars.find(cal => cal.id === value);
        if (selectedCalendar) {
            setFormData(prevData => ({
                ...prevData,
                name: `${selectedCalendar.name}`,
                description: selectedCalendar.description || "",
                color: selectedCalendar.color || "#4285F4"
            }));
        }
    };

    const resetForm = () => {
        setFormData({
            name: "",
            description: "",
            color: "#4285F4",
        });
        setEditingCalendar(null);
        setUseExistingCalendar(false);
        setSelectedSourceCalendarId("");
    };

    const handleCloseDialog = () => {
        setIsDialogOpen(false);
        resetForm();
    };

    const handleEditCalendar = (calendar: Calendar) => {
        setEditingCalendar(calendar);
        setFormData({
            name: calendar.name,
            description: calendar.description || "",
            color: calendar.color || "#4285F4",
        });
        setIsDialogOpen(true);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.name) {
            toast.error("O nome do calendário é obrigatório");
            return;
        }

        if (useExistingCalendar && !selectedSourceCalendarId) {
            toast.error("Selecione um calendário de origem");
            return;
        }

        setIsSubmitting(true);

        try {
            if (editingCalendar) {
                const response = await fetch(`/api/calendars/${editingCalendar.id}`, {
                    method: "PATCH",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(formData),
                });

                if (!response.ok) {
                    throw new Error("Erro ao atualizar calendário");
                }

                const updatedCalendar = await response.json();

                setCalendars(calendars.map((cal) =>
                    cal.id === updatedCalendar.id ? updatedCalendar : cal
                ));

                toast.success("Calendário atualizado com sucesso");
            } else {
                const requestBody: {
                    name: string;
                    description: string;
                    color: string;
                    clientId: string;
                    sourceCalendarId?: string;
                } = {
                    ...formData,
                    clientId: id as string,
                };

                if (useExistingCalendar && selectedSourceCalendarId) {
                    requestBody.sourceCalendarId = selectedSourceCalendarId;
                }

                const response = await fetch("/api/calendars", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(requestBody),
                });

                if (!response.ok) {
                    throw new Error("Erro ao criar calendário");
                }

                const newCalendar = await response.json();
                setCalendars([...calendars, newCalendar]);

                toast.success("Calendário criado com sucesso");
            }

            handleCloseDialog();
        } catch (error) {
            console.error("Erro:", error);
            toast.error(error instanceof Error ? error.message : "Ocorreu um erro");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleDeleteCalendar = async (calendarId: string) => {
        try {
            const response = await fetch(`/api/calendars/${calendarId}`, {
                method: "DELETE",
            });

            if (!response.ok) {
                throw new Error("Erro ao excluir calendário");
            } setCalendars(calendars.filter((cal) => cal.id !== calendarId));

            toast.success("Calendário excluído com sucesso");
        } catch (error) {
            console.error("Erro:", error);
            toast.error(error instanceof Error ? error.message : "Ocorreu um erro");
        }
    };

    const viewCalendarEvents = (calendarId: string) => {
        router.push(`/clients/${id}/calendars/${calendarId}`);
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isLoading ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <>
                        <CalendarBreadcrumb
                            clientId={id as string}
                            clientName={clientName}
                        />

                        <div className="flex justify-between gap-2 items-center mb-8">
                            <div className="flex items-center gap-2">
                                <Button variant="outline" size="icon" onClick={() => router.push(`/clients/${id}`)}>
                                    <MoveLeft />
                                </Button>
                            </div>
                            <div className="flex items-center gap-2 group">
                                <h1 className="text-sm xs:text-lg text-right uppercase font-geistMono font-semibold tracking-tight">
                                    Calendários de {clientName}
                                </h1>
                                <div className="bg-blue-50 dark:bg-blue-950 p-2 rounded-full">
                                    <CalendarIcon size={24} color="#4285F4" />
                                </div>
                            </div>
                        </div>

                        <div className="mb-6">
                            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                                <DialogTrigger asChild>
                                    <Button >
                                        <PlusCircle size={16} />
                                        Novo calendário
                                    </Button>
                                </DialogTrigger>
                                <DialogContent>
                                    <DialogHeader>
                                        <DialogTitle>
                                            {editingCalendar ? "Editar calendário" : "Novo calendário"}
                                        </DialogTitle>
                                    </DialogHeader>
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        <div>
                                            <Label htmlFor="name">Nome</Label>
                                            <Input
                                                id="name"
                                                name="name"
                                                value={formData.name}
                                                onChange={handleInputChange}
                                                placeholder="Nome do calendário"
                                                required
                                                className={useExistingCalendar && selectedSourceCalendarId ? "border-blue-400 dark:border-blue-600" : ""}
                                            />
                                            {useExistingCalendar && selectedSourceCalendarId && (
                                                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                                    Preenchido automaticamente com base no calendário selecionado
                                                </p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="description">Descrição</Label>
                                            <Textarea
                                                id="description"
                                                name="description"
                                                value={formData.description}
                                                onChange={handleInputChange}
                                                placeholder="Descrição (opcional)"
                                                rows={3}
                                                className={useExistingCalendar && selectedSourceCalendarId && formData.description ? "border-blue-400 dark:border-blue-600" : ""}
                                            />
                                            {useExistingCalendar && selectedSourceCalendarId && formData.description && (
                                                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                                    Preenchido automaticamente com base no calendário selecionado
                                                </p>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="color">Cor</Label>
                                            <div className="flex gap-2">
                                                <Input
                                                    id="color"
                                                    name="color"
                                                    type="color"
                                                    value={formData.color}
                                                    onChange={handleInputChange}
                                                    className={`w-16 h-10 p-1 ${useExistingCalendar && selectedSourceCalendarId ? "border-blue-400 dark:border-blue-600" : ""}`}
                                                />
                                                <Input
                                                    type="text"
                                                    value={formData.color}
                                                    onChange={handleInputChange}
                                                    name="color"
                                                    className={`flex-1 ${useExistingCalendar && selectedSourceCalendarId ? "border-blue-400 dark:border-blue-600" : ""}`}
                                                />
                                            </div>
                                            {useExistingCalendar && selectedSourceCalendarId && (
                                                <p className="text-xs text-blue-600 dark:text-blue-400 mt-1">
                                                    Cor do calendário selecionado
                                                </p>
                                            )}
                                        </div>

                                        {!editingCalendar && (
                                            <div className="space-y-4">
                                                <div className="flex items-center space-x-2">
                                                    <Switch
                                                        id="useExistingCalendar"
                                                        checked={useExistingCalendar}
                                                        onCheckedChange={handleSwitchChange}
                                                    />
                                                    <Label htmlFor="useExistingCalendar">
                                                        Usar estrutura de um calendário existente
                                                    </Label>
                                                </div>

                                                {useExistingCalendar && (
                                                    <div>
                                                        <Label htmlFor="sourceCalendar">
                                                            Selecione um calendário para copiar
                                                        </Label>
                                                        <Select
                                                            value={selectedSourceCalendarId}
                                                            onValueChange={handleSourceCalendarChange}
                                                        >
                                                            <SelectTrigger className="w-full">
                                                                <SelectValue placeholder="Selecione um calendário" />
                                                            </SelectTrigger>
                                                            <SelectContent>
                                                                {allCalendars.length > 1 ? allCalendars
                                                                    .filter(cal => cal.clientId !== id)
                                                                    .map(calendar => (
                                                                        <SelectItem key={calendar.id} value={calendar.id}>
                                                                            {calendar.name} ({calendar.client?.name})
                                                                        </SelectItem>
                                                                    ))
                                                                    : (
                                                                        <SelectItem value="no-calendars" disabled>
                                                                            Nenhum outro calendário encontrado
                                                                        </SelectItem>
                                                                    )
                                                                }
                                                            </SelectContent>
                                                        </Select>
                                                        <p className="text-xs text-muted-foreground mt-1">
                                                            Esta opção copia todos os eventos e datas comemorativas do calendário selecionado.
                                                        </p>
                                                    </div>
                                                )}
                                            </div>
                                        )}

                                        <div className="flex justify-end gap-2 pt-4">
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={handleCloseDialog}
                                            >
                                                Cancelar
                                            </Button>
                                            <Button type="submit" disabled={isSubmitting}>
                                                Salvar
                                            </Button>
                                        </div>
                                    </form>
                                </DialogContent>
                            </Dialog>
                        </div>

                        {calendars.length === 0 ? (
                            <div className="text-center p-8 border rounded-lg bg-muted/20">
                                <CalendarIcon className="mx-auto text-muted-foreground" />
                                <h3 className="font-medium">Nenhum calendário encontrado</h3>
                                <p className="text-muted-foreground mb-4">
                                    Crie um novo calendário para gerenciar eventos e datas importantes.
                                </p>
                                <Button
                                    onClick={() => setIsDialogOpen(true)}
                                >
                                    <PlusCircle size={16} />
                                    Criar calendário
                                </Button>
                            </div>
                        ) : (
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                {calendars.map((calendar) => (
                                    <Card key={calendar.id} className="overflow-hidden">
                                        <div
                                            className="h-2"
                                            style={{ backgroundColor: calendar.color || "#4285F4" }}
                                        ></div>
                                        <CardHeader className="pb-2">
                                            <CardTitle className="flex justify-between items-center">
                                                <span className="truncate">{calendar.name}</span>
                                                <div className="flex gap-1">
                                                    <Button
                                                        variant="outline"
                                                        size="icon"
                                                        onClick={() => handleEditCalendar(calendar)}
                                                    >
                                                        <Edit className="h-4 w-4" />
                                                    </Button>
                                                    <AlertDialog>
                                                        <AlertDialogTrigger asChild>
                                                            <Button variant="outline" size="icon">
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </AlertDialogTrigger>
                                                        <AlertDialogContent>
                                                            <AlertDialogHeader>
                                                                <AlertDialogTitle>Excluir calendário</AlertDialogTitle>
                                                                <AlertDialogDescription>
                                                                    Tem certeza que deseja excluir este calendário? Esta ação não pode ser desfeita e todos os eventos associados serão excluídos.
                                                                </AlertDialogDescription>
                                                            </AlertDialogHeader>
                                                            <AlertDialogFooter>
                                                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                                                <AlertDialogAction
                                                                    onClick={() => handleDeleteCalendar(calendar.id)}
                                                                >
                                                                    Excluir
                                                                </AlertDialogAction>
                                                            </AlertDialogFooter>
                                                        </AlertDialogContent>
                                                    </AlertDialog>
                                                </div>
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            {calendar.description && (
                                                <p className="text-sm text-muted-foreground mb-4 line-clamp-2">
                                                    {calendar.description}
                                                </p>
                                            )}
                                            <Button
                                                variant="outline"
                                                className="w-full"
                                                onClick={() => viewCalendarEvents(calendar.id)}
                                            >
                                                Ver eventos
                                            </Button>
                                        </CardContent>
                                    </Card>
                                ))}
                            </div>
                        )}
                    </>
                ) : (
                    <NotAllowed page="clients" />
                )}
            </div>
            <Footer />
        </div>
    );
}
