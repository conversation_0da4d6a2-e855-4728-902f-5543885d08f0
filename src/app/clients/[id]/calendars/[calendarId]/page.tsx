"use client"

import { useState, useEffect, useCallback } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { Calendar as CalendarIcon, PlusCircle, Edit, Trash2, MoveLeft, MapPinned, CalendarClock, Gift } from "lucide-react";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { Calendar, Event, HolidayEvent } from "@/types/calendar";
import { Header } from "@/app/components/header";
import Loading from "@/app/components/ui/loading";
import { Button } from "@/app/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import { Label } from "@/app/components/ui/label";
import { Input } from "@/app/components/ui/input";
import { Textarea } from "@/app/components/ui/textarea";
import { Checkbox } from "@/app/components/ui/checkbox";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/app/components/ui/card";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/app/components/ui/alert-dialog";
import { NotAllowed } from "@/app/components/not-allowed";
import { Footer } from "@/app/components/footer";
import { toast } from "sonner";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { CalendarBreadcrumb } from "@/app/components/calendar-breadcrumb";

export default function CalendarEventsPage() {
    const { data: session, status } = useSession();
    const router = useRouter();
    const { id, calendarId } = useParams();

    const [isAdmin, setIsAdmin] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [calendar, setCalendar] = useState<Calendar | null>(null);
    const [events, setEvents] = useState<Event[]>([]);
    const [holidays, setHolidays] = useState<HolidayEvent[]>([]);
    const [isFixedDay, setIsFixedDay] = useState(true);

    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);

    const [isHolidayDialogOpen, setIsHolidayDialogOpen] = useState(false);
    const [isHolidayEditDialogOpen, setIsHolidayEditDialogOpen] = useState(false);
    const [isHolidayDeleteDialogOpen, setIsHolidayDeleteDialogOpen] = useState(false);
    const [editingHoliday, setEditingHoliday] = useState<HolidayEvent | null>(null);
    const [deletingHoliday, setDeletingHoliday] = useState<HolidayEvent | null>(null);

    const [newHoliday, setNewHoliday] = useState({
        title: "",
        description: "",
        month: 1,
        day: 1,
        dayNotFixed: "",
        color: "#FF0000",
        allDay: true,
        isRecurring: true
    });

    const [formData, setFormData] = useState({
        title: "",
        description: "",
        startDate: "",
        startTime: "",
        endDate: "",
        endTime: "",
        allDay: false,
        location: "",
    });

    const [editingEvent, setEditingEvent] = useState<Event | null>(null);

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    const fetchHolidays = useCallback(async () => {
        try {
            const res = await fetch(`/api/holidays?calendarId=${calendarId}`);

            if (!res.ok) {
                throw new Error("Erro ao buscar datas comemorativas");
            }

            const data = await res.json();
            setHolidays(data);
        } catch (error) {
            console.error("Erro ao buscar datas comemorativas:", error);
            toast.error("Erro ao carregar datas comemorativas");
        }
    }, [calendarId]);

    const handleAddHoliday = async () => {
        try {
            if (!newHoliday.title) {
                toast.error("O título é obrigatório");
                return;
            }

            const res = await fetch("/api/holidays", {
                method: "POST",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    title: newHoliday.title,
                    description: newHoliday.description,
                    month: newHoliday.month,
                    day: isFixedDay ? newHoliday.day : undefined,
                    dayNotFixed: !isFixedDay ? newHoliday.dayNotFixed : undefined,
                    color: newHoliday.color,
                    allDay: newHoliday.allDay,
                    isRecurring: newHoliday.isRecurring,
                    calendarId
                })
            });

            if (!res.ok) {
                throw new Error("Erro ao adicionar data comemorativa");
            }

            toast.success("Data comemorativa adicionada com sucesso!");
            setIsHolidayDialogOpen(false);
            setNewHoliday({
                title: "",
                description: "",
                month: 1,
                day: 1,
                dayNotFixed: "",
                color: "#FF0000",
                allDay: true,
                isRecurring: true
            });

            fetchHolidays();
        } catch (error) {
            console.error("Erro ao adicionar data comemorativa:", error);
            toast.error("Erro ao adicionar data comemorativa");
        }
    };

    const handleEditHoliday = async () => {
        try {
            if (!editingHoliday || !editingHoliday.id) {
                toast.error("Erro ao identificar a data comemorativa");
                return;
            }

            if (!editingHoliday.title) {
                toast.error("O título é obrigatório");
                return;
            }

            const res = await fetch(`/api/holidays/${editingHoliday.id}`, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json"
                },
                body: JSON.stringify({
                    title: editingHoliday.title,
                    description: editingHoliday.description,
                    month: editingHoliday.month,
                    day: isFixedDay ? editingHoliday.day : undefined,
                    dayNotFixed: !isFixedDay ? editingHoliday.dayNotFixed : undefined,
                    color: editingHoliday.color,
                    allDay: editingHoliday.allDay,
                    isRecurring: editingHoliday.isRecurring
                })
            });

            if (!res.ok) {
                throw new Error("Erro ao editar data comemorativa");
            }

            toast.success("Data comemorativa atualizada com sucesso!");
            setIsHolidayEditDialogOpen(false);
            setEditingHoliday(null);

            fetchHolidays();
        } catch (error) {
            console.error("Erro ao editar data comemorativa:", error);
            toast.error("Erro ao atualizar data comemorativa");
        }
    };

    const handleDeleteHoliday = async () => {
        try {
            if (!deletingHoliday || !deletingHoliday.id) {
                toast.error("Erro ao identificar a data comemorativa");
                return;
            }

            const res = await fetch(`/api/holidays/${deletingHoliday.id}`, {
                method: "DELETE"
            });

            if (!res.ok) {
                throw new Error("Erro ao excluir data comemorativa");
            }

            toast.success("Data comemorativa excluída com sucesso!");
            setIsHolidayDeleteDialogOpen(false);
            setDeletingHoliday(null);

            fetchHolidays();
        } catch (error) {
            console.error("Erro ao excluir data comemorativa:", error);
            toast.error("Erro ao excluir data comemorativa");
        }
    };

    const getMonthName = (month: number) => {
        const date = new Date(2023, month - 1, 1);
        return format(date, "MMMM", { locale: ptBR });
    };

    const getDaysInMonth = (month: number) => {
        const year = new Date().getFullYear();
        return new Date(year, month, 0).getDate();
    };

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        setIsAdmin(["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"].includes(user?.role || ""));
                    }
                }
                setIsLoading(false);
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
                setIsLoading(false);
            }
        };

        if (session?.user?.email) {
            fetchUserRole();
        }
    }, [session]);

    useEffect(() => {
        const fetchCalendarData = async () => {
            if (calendarId) {
                try {
                    const response = await fetch(`/api/calendars/${calendarId}`);
                    if (response.ok) {
                        const data = await response.json();
                        setCalendar(data);
                        setEvents(data.events || []);

                        fetchHolidays();
                    }
                } catch (error) {
                    console.error("Erro ao buscar dados do calendário:", error);
                }
                setIsLoading(false);
            }
        };

        fetchCalendarData();
    }, [calendarId, fetchHolidays]);

    const formatDate = (dateString: string | Date) => {
        const date = new Date(dateString);
        return date.toLocaleDateString('pt-BR');
    };

    const formatTime = (dateString: string | Date) => {
        const date = new Date(dateString);
        return date.toLocaleTimeString('pt-BR', { hour: '2-digit', minute: '2-digit' });
    };

    const formatDateForInput = (dateString: string | Date) => {
        const date = new Date(dateString);
        return date.toISOString().split('T')[0];
    };

    const formatTimeForInput = (dateString: string | Date) => {
        const date = new Date(dateString);
        return date.toTimeString().substring(0, 5);
    };

    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
        const { name, value } = e.target;
        setFormData((prev) => ({
            ...prev,
            [name]: value,
        }));
    };

    const handleCheckboxChange = (checked: boolean) => {
        setFormData((prev) => ({
            ...prev,
            allDay: checked,
        }));
    };

    const resetForm = () => {
        const today = new Date().toISOString().split('T')[0];
        const now = new Date().toTimeString().substring(0, 5);

        setFormData({
            title: "",
            description: "",
            startDate: today,
            startTime: now,
            endDate: today,
            endTime: now,
            allDay: false,
            location: "",
        });
        setEditingEvent(null);
    };

    const handleCloseDialog = () => {
        setIsDialogOpen(false);
        resetForm();
    };

    const handleEditEvent = (event: Event) => {
        setEditingEvent(event);

        const startDate = formatDateForInput(event.startDate);
        const startTime = event.allDay ? "00:00" : formatTimeForInput(event.startDate);

        let endDate = startDate;
        let endTime = startTime;

        if (event.endDate) {
            endDate = formatDateForInput(event.endDate);
            endTime = event.allDay ? "23:59" : formatTimeForInput(event.endDate);
        }

        setFormData({
            title: event.title,
            description: event.description || "",
            startDate,
            startTime,
            endDate,
            endTime,
            allDay: event.allDay,
            location: event.location || "",
        });

        setIsDialogOpen(true);
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!formData.title || !formData.startDate) {
            toast.error("Título e data de início são obrigatórios");
            return;
        }

        setIsSubmitting(true);

        try {
            const startDateTime = new Date(`${formData.startDate}T${formData.allDay ? "00:00:00" : formData.startTime}:00`);

            let endDateTime = null;
            if (formData.endDate) {
                endDateTime = new Date(`${formData.endDate}T${formData.allDay ? "23:59:59" : formData.endTime}:00`);
            }

            const eventData = {
                title: formData.title,
                description: formData.description,
                startDate: startDateTime.toISOString(),
                endDate: endDateTime ? endDateTime.toISOString() : undefined,
                allDay: formData.allDay,
                location: formData.location,
                calendarId: calendarId as string,
            };

            if (editingEvent) {
                const response = await fetch(`/api/events/${editingEvent.id}`, {
                    method: "PATCH",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(eventData),
                });

                if (!response.ok) {
                    throw new Error("Erro ao atualizar evento");
                }

                const updatedEvent = await response.json();

                setEvents(events.map((evt) =>
                    evt.id === updatedEvent.id ? updatedEvent : evt
                ));

                toast.success("Evento atualizado com sucesso");
            } else {
                const response = await fetch("/api/events", {
                    method: "POST",
                    headers: {
                        "Content-Type": "application/json",
                    },
                    body: JSON.stringify(eventData),
                });

                if (!response.ok) {
                    throw new Error("Erro ao criar evento");
                }

                const newEvent = await response.json();
                setEvents([...events, newEvent]);

                toast.success("Evento criado com sucesso");
            }

            handleCloseDialog();
        } catch (error) {
            console.error("Erro:", error);
            toast.error(error instanceof Error ? error.message : "Ocorreu um erro");
        } finally {
            setIsSubmitting(false);
        }
    };

    const handleDeleteEvent = async (eventId: string) => {
        try {
            const response = await fetch(`/api/events/${eventId}`, {
                method: "DELETE",
            });

            if (!response.ok) {
                throw new Error("Erro ao excluir evento");
            }

            setEvents(events.filter((evt) => evt.id !== eventId));

            toast.success("Evento excluído com sucesso");
        } catch (error) {
            console.error("Erro:", error);
            toast.error(error instanceof Error ? error.message : "Ocorreu um erro");
        }
    };

    const groupEventsByMonth = () => {
        const grouped: Record<string, Event[]> = {};

        events.forEach(event => {
            const date = new Date(event.startDate);
            const key = `${date.getFullYear()}-${date.getMonth() + 1}`;

            if (!grouped[key]) {
                grouped[key] = [];
            }

            grouped[key].push(event);
        });

        return Object.keys(grouped)
            .sort((a, b) => b.localeCompare(a))
            .map(key => {
                const [year, month] = key.split('-').map(Number);
                const monthName = new Date(year, month - 1, 1).toLocaleString('pt-BR', { month: 'long' });

                return {
                    title: `${monthName.charAt(0) + monthName.slice(1)} ${year}`,
                    events: grouped[key].sort((a, b) =>
                        new Date(a.startDate).getTime() - new Date(b.startDate).getTime()
                    )
                };
            });
    };

    const eventGroups = groupEventsByMonth();

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isLoading ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <>
                        <CalendarBreadcrumb 
                            clientId={id as string}
                            clientName={calendar?.client?.name || 'Cliente'}
                            calendarName={calendar?.name || 'Calendário'}
                        />

                        <div className="flex justify-between items-center mb-8">
                            <div className="flex items-center gap-2">
                                <Button
                                    variant="outline"
                                    size="icon"
                                    onClick={() => router.push(`/clients/${id}/calendars`)}
                                >
                                    <MoveLeft />
                                </Button>
                            </div>
                            <div className="flex items-center gap-2 group">
                                <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                    {calendar?.name}
                                </h1>
                                <div
                                    className="p-2 rounded-full"
                                    style={{
                                        backgroundColor: calendar?.color
                                            ? `${calendar.color}20`
                                            : '#4285F420'
                                    }}
                                >
                                    <CalendarIcon
                                        size={24}
                                        color={calendar?.color || "#4285F4"}
                                    />
                                </div>
                            </div>
                        </div>

                        <div className="mb-6 flex justify-between gap-2 flex-wrap">
                            <Button
                                onClick={() => setIsHolidayDialogOpen(true)}
                            >
                                <Gift size={16} />
                                Nova data comemorativa
                            </Button>

                            <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
                                <DialogTrigger asChild>
                                    <Button
                                        disabled
                                    >
                                        <PlusCircle size={16} />
                                        Novo evento
                                    </Button>
                                </DialogTrigger>
                                <DialogContent className="max-h-[90vh] overflow-y-auto">
                                    <DialogHeader>
                                        <DialogTitle>
                                            {editingEvent ? "Editar evento" : "Novo evento"}
                                        </DialogTitle>
                                    </DialogHeader>
                                    <form onSubmit={handleSubmit} className="space-y-4">
                                        <div>
                                            <Label htmlFor="title">Título</Label>
                                            <Input
                                                id="title"
                                                name="title"
                                                value={formData.title}
                                                onChange={handleInputChange}
                                                placeholder="Título do evento"
                                                required
                                            />
                                        </div>
                                        <div>
                                            <Label htmlFor="description">Descrição</Label>
                                            <Textarea
                                                id="description"
                                                name="description"
                                                value={formData.description}
                                                onChange={handleInputChange}
                                                placeholder="Descrição (opcional)"
                                                rows={3}
                                            />
                                        </div>
                                        <div className="flex items-center space-x-2 mb-4">
                                            <Checkbox
                                                id="allDay"
                                                checked={formData.allDay}
                                                onCheckedChange={handleCheckboxChange}
                                            />
                                            <Label htmlFor="allDay">Evento de dia inteiro</Label>
                                        </div>
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="startDate">Data de início</Label>
                                                <Input
                                                    id="startDate"
                                                    name="startDate"
                                                    type="date"
                                                    value={formData.startDate}
                                                    onChange={handleInputChange}
                                                    required
                                                />
                                            </div>
                                            {!formData.allDay && (
                                                <div>
                                                    <Label htmlFor="startTime">Hora de início</Label>
                                                    <Input
                                                        id="startTime"
                                                        name="startTime"
                                                        type="time"
                                                        value={formData.startTime}
                                                        onChange={handleInputChange}
                                                        required
                                                    />
                                                </div>
                                            )}
                                        </div>
                                        <div className="grid grid-cols-2 gap-4">
                                            <div>
                                                <Label htmlFor="endDate">Data de término (opcional)</Label>
                                                <Input
                                                    id="endDate"
                                                    name="endDate"
                                                    type="date"
                                                    value={formData.endDate}
                                                    onChange={handleInputChange}
                                                />
                                            </div>
                                            {!formData.allDay && (
                                                <div>
                                                    <Label htmlFor="endTime">Hora de término</Label>
                                                    <Input
                                                        id="endTime"
                                                        name="endTime"
                                                        type="time"
                                                        value={formData.endTime}
                                                        onChange={handleInputChange}
                                                    />
                                                </div>
                                            )}
                                        </div>
                                        <div>
                                            <Label htmlFor="location">Localização</Label>
                                            <Input
                                                id="location"
                                                name="location"
                                                value={formData.location}
                                                onChange={handleInputChange}
                                                placeholder="Localização (opcional)"
                                            />
                                        </div>
                                        <div className="flex justify-end gap-2 pt-4">
                                            <Button
                                                type="button"
                                                variant="outline"
                                                onClick={handleCloseDialog}
                                            >
                                                Cancelar
                                            </Button>
                                            <Button type="submit" disabled={isSubmitting}>
                                                Salvar
                                            </Button>
                                        </div>
                                    </form>
                                </DialogContent>
                            </Dialog>
                        </div>

                        <Tabs defaultValue="holidays" className="mb-6">
                            <TabsList className="grid w-full grid-cols-2">
                                <TabsTrigger value="holidays">Datas comemorativas</TabsTrigger>
                                <TabsTrigger value="events">Eventos</TabsTrigger>
                            </TabsList>

                            <TabsContent value="holidays" className="pt-4">
                                {holidays.length === 0 ? (
                                    <div className="text-center p-8 border rounded-lg bg-muted/20">
                                        <Gift className="mx-auto text-muted-foreground" />
                                        <h3 className="font-medium">Nenhuma data comemorativa encontrada</h3>
                                        <p className="text-muted-foreground mb-4">
                                            Adicione datas comemorativas recorrentes para o calendário.
                                        </p>
                                        <Button
                                            onClick={() => setIsHolidayDialogOpen(true)}
                                        >
                                            <PlusCircle size={16} />
                                            Adicionar data comemorativa
                                        </Button>
                                    </div>
                                ) : (
                                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                                        {holidays.map((holiday) => (
                                            <Card key={holiday.id}>
                                                <CardHeader className="pb-2">
                                                    <div className="flex justify-between items-start">
                                                        <CardTitle className="text-lg">
                                                            {holiday.title}
                                                        </CardTitle>
                                                        <div className="flex space-x-1">                                                                <Button
                                                            variant="outline"
                                                            size="icon"
                                                            onClick={() => {
                                                                setEditingHoliday(holiday);
                                                                setIsFixedDay(!!holiday.day && !holiday.dayNotFixed);
                                                                setIsHolidayEditDialogOpen(true);
                                                            }}
                                                        >
                                                            <Edit className="h-4 w-4" />
                                                        </Button>
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                onClick={() => {
                                                                    setDeletingHoliday(holiday);
                                                                    setIsHolidayDeleteDialogOpen(true);
                                                                }}
                                                            >
                                                                <Trash2 className="h-4 w-4" />
                                                            </Button>
                                                        </div>
                                                    </div>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="flex items-center mb-2">
                                                        <CalendarIcon className="h-4 w-4 mr-2" style={{ color: holiday.color || "#FF0000" }} />
                                                        <span>
                                                            {getMonthName(holiday.month)} {holiday.dayNotFixed || holiday.day}
                                                        </span>
                                                    </div>
                                                    {holiday.description && (
                                                        <p className="text-sm text-gray-600 mb-2">{holiday.description}</p>
                                                    )}
                                                    <div className="flex items-center text-xs text-gray-500">
                                                        <span className="mr-3">
                                                            {holiday.isRecurring ? "Recorrente anual" : "Não recorrente"}
                                                        </span>
                                                        <span>
                                                            {holiday.allDay ? "Dia todo" : "Evento parcial"}
                                                        </span>
                                                    </div>
                                                </CardContent>
                                            </Card>
                                        ))}
                                    </div>
                                )}
                            </TabsContent>

                            <TabsContent value="events" className="pt-4">
                                {events.length === 0 ? (
                                    <div className="text-center p-8 border rounded-lg bg-muted/20">
                                        <p className="text-muted-foreground">
                                            Essa função será implementada em breve.
                                        </p>
                                    </div>
                                ) : (
                                    <div className="space-y-8">
                                        {eventGroups.map((group, index) => (
                                            <div key={index}>
                                                <h2 className="text-xl font-semibold mb-4 capitalize">{group.title}</h2>
                                                <div className="space-y-4">
                                                    {group.events.map((event) => (
                                                        <Card key={event.id} className="overflow-hidden">
                                                            <CardHeader className="pb-2">
                                                                <CardTitle className="flex justify-between items-center">
                                                                    <span className="truncate">{event.title}</span>
                                                                    <div className="flex gap-1">
                                                                        <Button
                                                                            variant="outline"
                                                                            size="icon"
                                                                            onClick={() => handleEditEvent(event)}
                                                                        >
                                                                            <Edit className="h-4 w-4" />
                                                                        </Button>
                                                                        <AlertDialog>
                                                                            <AlertDialogTrigger asChild>
                                                                                <Button variant="outline" size="icon">
                                                                                    <Trash2 className="h-4 w-4" />
                                                                                </Button>
                                                                            </AlertDialogTrigger>
                                                                            <AlertDialogContent>
                                                                                <AlertDialogHeader>
                                                                                    <AlertDialogTitle>Excluir evento</AlertDialogTitle>
                                                                                    <AlertDialogDescription>
                                                                                        Tem certeza que deseja excluir este evento? Esta ação não pode ser desfeita.
                                                                                    </AlertDialogDescription>
                                                                                </AlertDialogHeader>
                                                                                <AlertDialogFooter>
                                                                                    <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                                                                    <AlertDialogAction
                                                                                        onClick={() => handleDeleteEvent(event.id)}
                                                                                    >
                                                                                        Excluir
                                                                                    </AlertDialogAction>
                                                                                </AlertDialogFooter>
                                                                            </AlertDialogContent>
                                                                        </AlertDialog>
                                                                    </div>
                                                                </CardTitle>
                                                            </CardHeader>
                                                            <CardContent className="space-y-3">
                                                                {event.description && (
                                                                    <p className="text-sm text-muted-foreground">
                                                                        {event.description}
                                                                    </p>
                                                                )}
                                                                <div className="flex items-center gap-2 text-sm">
                                                                    <CalendarClock className="h-4 w-4 text-muted-foreground" />
                                                                    <span>
                                                                        {event.allDay ? (
                                                                            <>Dia inteiro - {formatDate(event.startDate)}</>
                                                                        ) : (
                                                                            <>
                                                                                {formatDate(event.startDate)} às {formatTime(event.startDate)}
                                                                                {event.endDate && (
                                                                                    <>
                                                                                        {' '} até {' '}
                                                                                        {formatDate(event.endDate) === formatDate(event.startDate)
                                                                                            ? formatTime(event.endDate)
                                                                                            : `${formatDate(event.endDate)} às ${formatTime(event.endDate)}`}
                                                                                    </>
                                                                                )}
                                                                            </>
                                                                        )}
                                                                    </span>
                                                                </div>
                                                                {event.location && (
                                                                    <div className="flex items-center gap-2 text-sm">
                                                                        <MapPinned className="h-4 w-4 text-muted-foreground" />
                                                                        <span>{event.location}</span>
                                                                    </div>
                                                                )}
                                                            </CardContent>
                                                        </Card>
                                                    ))}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                )}
                            </TabsContent>
                        </Tabs>
                    </>
                ) : (
                    <NotAllowed page="clients" />
                )}
            </div>

            <Dialog open={isHolidayDialogOpen} onOpenChange={setIsHolidayDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Adicionar data comemorativa</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                        <div className="space-y-2">
                            <Label htmlFor="title">Título</Label>
                            <Input
                                id="title"
                                value={newHoliday.title}
                                onChange={(e) => setNewHoliday({ ...newHoliday, title: e.target.value })}
                                placeholder="Ex: Natal, Aniversário da Empresa, etc"
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="description">Descrição (opcional)</Label>
                            <Textarea
                                id="description"
                                value={newHoliday.description}
                                onChange={(e) => setNewHoliday({ ...newHoliday, description: e.target.value })}
                                placeholder="Detalhes adicionais sobre a data"
                            />
                        </div>

                        <div className="space-y-6">
                            <div className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-3">
                                        <Label htmlFor="month" className="text-sm font-medium">
                                            Mês
                                        </Label>
                                        <div className="h-5"></div>
                                        <Select
                                            value={newHoliday.month.toString()}
                                            onValueChange={(value) => setNewHoliday({ ...newHoliday, month: parseInt(value) })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione o mês" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                                                    <SelectItem key={month} value={month.toString()}>
                                                        {getMonthName(month)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-3">
                                        <Label className="text-sm font-medium">
                                            Configuração do dia
                                        </Label>
                                        <div className="flex items-center space-x-2">
                                            <Checkbox
                                                id="fixedDay"
                                                checked={isFixedDay}
                                                onCheckedChange={(checked) => setIsFixedDay(checked as boolean)}
                                            />
                                            <Label htmlFor="fixedDay" className="text-sm font-medium">
                                                Dia fixo
                                            </Label>
                                        </div>

                                        <div className="space-y-2">
                                            {isFixedDay ? (
                                                <Select
                                                    value={newHoliday.day?.toString() || ""}
                                                    onValueChange={(value) => setNewHoliday({
                                                        ...newHoliday,
                                                        day: parseInt(value),
                                                        dayNotFixed: ""
                                                    })}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Selecione o dia" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Array.from({ length: getDaysInMonth(newHoliday.month) }, (_, i) => i + 1).map((day) => (
                                                            <SelectItem key={day} value={day.toString()}>
                                                                {day}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            ) : (
                                                <Input
                                                    id="dayNotFixed"
                                                    value={newHoliday.dayNotFixed || ""}
                                                    onChange={(e) => setNewHoliday({
                                                        ...newHoliday,
                                                        dayNotFixed: e.target.value,
                                                        day: 1
                                                    })}
                                                    type="text"
                                                    placeholder="Ex: Terceiro domingo de abril"
                                                    className="w-full"
                                                />
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="color">Cor</Label>
                            <div className="flex items-center">
                                <Input
                                    id="color"
                                    type="color"
                                    value={newHoliday.color}
                                    onChange={(e) => setNewHoliday({ ...newHoliday, color: e.target.value })}
                                    className="w-12 h-10 p-1"
                                />
                                <span className="ml-2 text-sm text-gray-500">
                                    Selecione uma cor para a data comemorativa
                                </span>
                            </div>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="allDay"
                                checked={newHoliday.allDay}
                                onCheckedChange={(checked) =>
                                    setNewHoliday({ ...newHoliday, allDay: checked as boolean })
                                }
                            />
                            <Label htmlFor="allDay">Dia todo</Label>
                        </div>

                        <div className="flex items-center space-x-2">
                            <Checkbox
                                id="isRecurring"
                                checked={newHoliday.isRecurring}
                                onCheckedChange={(checked) =>
                                    setNewHoliday({ ...newHoliday, isRecurring: checked as boolean })
                                }
                            />
                            <Label htmlFor="isRecurring">Data recorrente (anual)</Label>
                        </div>

                        <div className="pt-4 flex justify-end space-x-2">
                            <Button variant="outline" onClick={() => setIsHolidayDialogOpen(false)}>
                                Cancelar
                            </Button>
                            <Button onClick={handleAddHoliday}>
                                Adicionar
                            </Button>
                        </div>
                    </div>
                </DialogContent>
            </Dialog>

            <Dialog open={isHolidayEditDialogOpen} onOpenChange={setIsHolidayEditDialogOpen}>
                <DialogContent>
                    <DialogHeader>
                        <DialogTitle>Editar data comemorativa</DialogTitle>
                    </DialogHeader>
                    {editingHoliday && (
                        <div className="space-y-6">
                            <div className="space-y-4">
                                <div className="space-y-2">
                                    <Label htmlFor="edit-title">Título</Label>
                                    <Input
                                        id="edit-title"
                                        value={editingHoliday.title}
                                        onChange={(e) => setEditingHoliday({ ...editingHoliday, title: e.target.value })}
                                    />
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="edit-description">Descrição (opcional)</Label>
                                    <Textarea
                                        id="edit-description"
                                        value={editingHoliday.description || ""}
                                        onChange={(e) => setEditingHoliday({ ...editingHoliday, description: e.target.value })}
                                    />
                                </div>

                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <div className="space-y-3">
                                        <Label htmlFor="edit-month" className="text-sm font-medium">
                                            Mês
                                        </Label>
                                        <div className="h-5"></div>
                                        <Select
                                            value={editingHoliday.month.toString()}
                                            onValueChange={(value) => setEditingHoliday({ ...editingHoliday, month: parseInt(value) })}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione o mês" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                                                    <SelectItem key={month} value={month.toString()}>
                                                        {getMonthName(month)}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                    </div>

                                    <div className="space-y-3">
                                        <Label className="text-sm font-medium">
                                            Configuração do dia
                                        </Label>
                                        <div className="flex items-center space-x-2">
                                            <Checkbox
                                                id="editFixedDay"
                                                checked={isFixedDay}
                                                onCheckedChange={(checked) => setIsFixedDay(checked as boolean)}
                                            />
                                            <Label htmlFor="editFixedDay" className="text-sm font-medium">
                                                Dia fixo
                                            </Label>
                                        </div>

                                        <div className="space-y-2">
                                            {isFixedDay ? (
                                                <Select
                                                    value={(editingHoliday.day || 1).toString()}
                                                    onValueChange={(value) => setEditingHoliday({
                                                        ...editingHoliday,
                                                        day: parseInt(value),
                                                        dayNotFixed: ""
                                                    })}
                                                >
                                                    <SelectTrigger>
                                                        <SelectValue placeholder="Selecione o dia" />
                                                    </SelectTrigger>
                                                    <SelectContent>
                                                        {Array.from({ length: getDaysInMonth(editingHoliday.month) }, (_, i) => i + 1).map((day) => (
                                                            <SelectItem key={day} value={day.toString()}>
                                                                {day}
                                                            </SelectItem>
                                                        ))}
                                                    </SelectContent>
                                                </Select>
                                            ) : (
                                                <Input
                                                    id="edit-dayNotFixed"
                                                    value={editingHoliday.dayNotFixed || ""}
                                                    onChange={(e) => setEditingHoliday({
                                                        ...editingHoliday,
                                                        dayNotFixed: e.target.value,
                                                        day: 1
                                                    })}
                                                    type="text"
                                                    placeholder="Ex: Terceiro domingo de abril"
                                                    className="w-full"
                                                />
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div className="space-y-2">
                                <Label htmlFor="edit-color">Cor</Label>
                                <div className="flex items-center">
                                    <Input
                                        id="edit-color"
                                        type="color"
                                        value={editingHoliday.color || "#FF0000"}
                                        onChange={(e) => setEditingHoliday({ ...editingHoliday, color: e.target.value })}
                                        className="w-12 h-10 p-1"
                                    />
                                    <span className="ml-2 text-sm text-gray-500">
                                        Selecione uma cor para a data comemorativa
                                    </span>
                                </div>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="edit-allDay"
                                    checked={editingHoliday.allDay}
                                    onCheckedChange={(checked) =>
                                        setEditingHoliday({ ...editingHoliday, allDay: checked as boolean })
                                    }
                                />
                                <Label htmlFor="edit-allDay">Dia todo</Label>
                            </div>

                            <div className="flex items-center space-x-2">
                                <Checkbox
                                    id="edit-isRecurring"
                                    checked={editingHoliday.isRecurring}
                                    onCheckedChange={(checked) =>
                                        setEditingHoliday({ ...editingHoliday, isRecurring: checked as boolean })
                                    }
                                />
                                <Label htmlFor="edit-isRecurring">Data recorrente (anual)</Label>
                            </div>

                            <div className="pt-4 flex justify-end space-x-2">
                                <Button variant="outline" onClick={() => setIsHolidayEditDialogOpen(false)}>
                                    Cancelar
                                </Button>
                                <Button onClick={handleEditHoliday}>
                                    Salvar
                                </Button>
                            </div>
                        </div>
                    )}
                </DialogContent>
            </Dialog>

            <AlertDialog open={isHolidayDeleteDialogOpen} onOpenChange={setIsHolidayDeleteDialogOpen}>
                <AlertDialogContent>
                    <AlertDialogHeader>
                        <AlertDialogTitle>Confirmar exclusão</AlertDialogTitle>
                        <AlertDialogDescription>
                            Tem certeza que deseja excluir a data comemorativa &quot;{deletingHoliday?.title}&quot;?
                            Esta ação não pode ser desfeita.
                        </AlertDialogDescription>
                    </AlertDialogHeader>
                    <AlertDialogFooter>
                        <AlertDialogCancel>Cancelar</AlertDialogCancel>
                        <AlertDialogAction onClick={handleDeleteHoliday}>
                            Excluir
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            <Footer />
        </div>
    );
}
