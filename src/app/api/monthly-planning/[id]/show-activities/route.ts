import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";

export async function PATCH(request: Request) {
    try {
        const url = new URL(request.url);
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.indexOf('monthly-planning') + 1];

        if (!id) {
            return NextResponse.json(
                { error: "ID do planejamento é obrigatório" },
                { status: 400 }
            );
        }

        const { showActivitiesInModuleThree } = await request.json();

        if (showActivitiesInModuleThree === undefined) {
            return NextResponse.json(
                { error: "O parâmetro showActivitiesInModuleThree é obrigatório" },
                { status: 400 }
            );
        }

        const planning = await prisma.monthlyPlanning.findUnique({
            where: { id },
            include: { 
                client: true,
                activities: {
                    include: {
                        contents: true
                    }
                }
            }
        });

        if (!planning) {
            return NextResponse.json(
                { error: "Planejamento não encontrado" },
                { status: 404 }
            );
        }

        if (showActivitiesInModuleThree) {
            const feedContentIds = planning.activities.flatMap(activity => 
                activity.contents
                    .filter(content => 
                        content.destination === "Feed" || 
                        content.destination === "Story/Feed"
                    )
                    .map(content => content.id)
            );

            if (feedContentIds.length > 0) {
                await prisma.content.updateMany({
                    where: {
                        id: { in: feedContentIds }
                    },
                    data: {
                        status: "estruturação de feed"
                    }
                });
            }
        }

        const updatedPlanning = await prisma.monthlyPlanning.update({
            where: { id },
            data: { showActivitiesInModuleThree }
        });

        return NextResponse.json(updatedPlanning, { status: 200 });
    } catch (error) {
        console.error("Erro ao atualizar exibição de atividades:", error);

        if (error instanceof Error) {
            return NextResponse.json(
                { error: error.message },
                { status: 400 }
            );
        }

        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}
