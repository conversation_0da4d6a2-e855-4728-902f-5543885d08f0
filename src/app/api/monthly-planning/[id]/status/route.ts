import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";

interface UserContentGroup {
    user: {
        id: string;
        name?: string | null;
        email: string;
        image?: string | null;
    };
    clientName: string;
    count: number;
}

interface UserContentGroups {
    [userId: string]: UserContentGroup;
}

export async function PATCH(request: Request) {
    try {
        const url = new URL(request.url);
        const pathParts = url.pathname.split('/');
        const id = pathParts[pathParts.indexOf('monthly-planning') + 1];

        if (!id) {
            return NextResponse.json(
                { error: "ID do planejamento é obrigatório" },
                { status: 400 }
            );
        }

        const { status } = await request.json();

        if (!status) {
            return NextResponse.json(
                { error: "O status é obrigatório" },
                { status: 400 }
            );
        }

        const validStatusValues = ["pend. aprovação", "aprovado", "não aprovado"];
        if (!validStatusValues.includes(status)) {
            return NextResponse.json(
                { error: "Status inválido. Use: 'pend. aprovação', 'aprovado' ou 'não aprovado'" },
                { status: 400 }
            );
        }

        const planning = await prisma.monthlyPlanning.findUnique({
            where: { id },
            include: { client: true }
        });

        if (!planning) {
            return NextResponse.json(
                { error: "Planejamento não encontrado" },
                { status: 404 }
            );
        }

        const updatedPlanning = await prisma.monthlyPlanning.update({
            where: { id },
            data: { status }
        });

        if (status === "aprovado" && planning.status !== "aprovado") {
            const contents = await prisma.content.findMany({
                where: {
                    weeklyActivity: {
                        monthlyPlanningId: id
                    },
                    OR: [
                        { assignedToId: { not: null } },
                        { steps: { some: { assignedToId: { not: null } } } }
                    ]
                },
                include: {
                    assignedTo: true,
                    weeklyActivity: {
                        include: {
                            monthlyPlanning: {
                                include: {
                                    client: true
                                }
                            }
                        }
                    },
                    steps: {
                        include: {
                            assignedTo: true
                        }
                    }
                }
            });

            const userContentGroups: UserContentGroups = contents.reduce((groups: UserContentGroups, content) => {
                const userId = content.assignedTo?.email;
                
                if (userId && content.assignedTo) {
                    if (!groups[userId]) {
                        groups[userId] = {
                            user: content.assignedTo,
                            clientName: content.weeklyActivity.monthlyPlanning.client.name,
                            count: 0
                        };
                    }
                    
                    groups[userId].count++;
                }

                content.steps?.forEach(step => {
                    if (step.assignedToId && step.assignedTo) {
                        const stepUserId = step.assignedTo.email;
                        
                        if (!groups[stepUserId]) {
                            groups[stepUserId] = {
                                user: step.assignedTo,
                                clientName: content.weeklyActivity.monthlyPlanning.client.name,
                                count: 0
                            };
                        }
                        
                        groups[stepUserId].count++;
                    }
                });
                
                return groups;
            }, {});

            for (const userId in userContentGroups) {
                const group = userContentGroups[userId];
                
                await prisma.notification.create({
                    data: {
                        content: `O planejamento de ${group.clientName} foi aprovado. Você tem ${group.count} demanda${group.count > 1 ? 's' : ''} atribuída${group.count > 1 ? 's' : ''}.`,
                        type: "planning_approved",
                        entityId: id,
                        entityType: "monthly_planning",
                        userId: userId,
                        importance: "high",
                        reference: "/my-demands"
                    }
                });
            }
        }

        return NextResponse.json(updatedPlanning, { status: 200 });
    } catch (error) {
        console.error("Erro ao atualizar status do planejamento:", error);

        if (error instanceof Error) {
            return NextResponse.json(
                { error: error.message },
                { status: 400 }
            );
        }

        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}