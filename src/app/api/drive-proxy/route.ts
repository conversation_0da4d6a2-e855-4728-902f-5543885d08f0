import { NextRequest, NextResponse } from 'next/server';
import { promises as fs } from 'fs';
import path from 'path';

const imageCache = new Map<string, { buffer: ArrayBuffer, contentType: string, timestamp: number }>();
const CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

setInterval(() => {
    const now = Date.now();
    for (const [key, value] of imageCache.entries()) {
        if (now - value.timestamp > CACHE_EXPIRATION) {
            imageCache.delete(key);
        }
    }
}, 60 * 60 * 1000);

export async function GET(request: NextRequest) {
    const url = new URL(request.url);
    const fileId = url.searchParams.get('id');
    const noCache = url.searchParams.get('no-cache') === 'true';

    console.log('📡 Drive-proxy solicitação recebida:', {
        fileId,
        noCache,
        userAgent: request.headers.get('user-agent')
    });

    if (!fileId) {
        console.error('❌ ID do arquivo não fornecido');
        return NextResponse.json({ error: 'ID do arquivo não fornecido' }, { status: 400 });
    }

    try {
        if (!noCache && imageCache.has(fileId)) {
            const cachedImage = imageCache.get(fileId)!;
            console.log('💾 Imagem encontrada no cache:', fileId);

            return new NextResponse(cachedImage.buffer, {
                headers: {
                    'Content-Type': cachedImage.contentType,
                    'Cache-Control': 'public, max-age=86400',
                    'X-Cache': 'HIT'
                },
            });
        }

        // Estratégia 1: URL de thumbnail padrão
        const thumbnailUrl = `https://drive.google.com/thumbnail?id=${fileId}&sz=w800`;
        console.log('🔗 Tentando buscar imagem (estratégia 1):', thumbnailUrl);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        try {
            const response = await fetch(thumbnailUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                    'Referer': 'https://drive.google.com/',
                },
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            console.log('📥 Resposta do Google Drive (estratégia 1):', {
                status: response.status,
                statusText: response.statusText,
                contentType: response.headers.get('Content-Type'),
                contentLength: response.headers.get('Content-Length')
            });

            if (response.ok) {
                const contentType = response.headers.get('Content-Type') || '';
                const contentLength = parseInt(response.headers.get('Content-Length') || '0');
                
                // Verificar se é realmente uma imagem
                if (contentType.startsWith('image/') && contentLength > 0) {
                    const buffer = await response.arrayBuffer();

                    console.log('✅ Imagem carregada com sucesso (estratégia 1):', {
                        fileId,
                        contentType,
                        bufferSize: buffer.byteLength
                    });

                    imageCache.set(fileId, {
                        buffer,
                        contentType,
                        timestamp: Date.now()
                    });

                    return new NextResponse(buffer, {
                        headers: {
                            'Content-Type': contentType,
                            'Cache-Control': 'public, max-age=86400',
                            'X-Cache': 'MISS'
                        },
                    });
                } else {
                    console.log('⚠️ Estratégia 1 retornou HTML em vez de imagem:', {
                        contentType,
                        contentLength
                    });
                }
            }

            // Estratégia 2: URL de thumbnail com tamanho diferente
            console.log('🔗 Tentando estratégia 2 com tamanho menor...');
            const smallThumbnailUrl = `https://drive.google.com/thumbnail?id=${fileId}&sz=w400`;
            
            const controller2 = new AbortController();
            const timeoutId2 = setTimeout(() => controller2.abort(), 10000);

            const response2 = await fetch(smallThumbnailUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                    'Referer': 'https://drive.google.com/',
                },
                signal: controller2.signal
            });

            clearTimeout(timeoutId2);

            if (response2.ok) {
                const contentType2 = response2.headers.get('Content-Type') || '';
                const contentLength2 = parseInt(response2.headers.get('Content-Length') || '0');
                
                if (contentType2.startsWith('image/') && contentLength2 > 0) {
                    const buffer2 = await response2.arrayBuffer();

                    console.log('✅ Imagem carregada com sucesso (estratégia 2):', {
                        fileId,
                        contentType: contentType2,
                        bufferSize: buffer2.byteLength
                    });

                    imageCache.set(fileId, {
                        buffer: buffer2,
                        contentType: contentType2,
                        timestamp: Date.now()
                    });

                    return new NextResponse(buffer2, {
                        headers: {
                            'Content-Type': contentType2,
                            'Cache-Control': 'public, max-age=86400',
                            'X-Cache': 'MISS'
                        },
                    });
                } else {
                    console.log('⚠️ Estratégia 2 retornou HTML em vez de imagem:', {
                        contentType: contentType2,
                        contentLength: contentLength2
                    });
                }
            }

            // Estratégia 3: URL de exportação direto
            console.log('🔗 Tentando estratégia 3 com exportação...');
            const exportUrl = `https://drive.google.com/uc?export=view&id=${fileId}`;
            
            const controller3 = new AbortController();
            const timeoutId3 = setTimeout(() => controller3.abort(), 10000);

            const response3 = await fetch(exportUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                    'Referer': 'https://drive.google.com/',
                },
                signal: controller3.signal
            });

            clearTimeout(timeoutId3);

            if (response3.ok && response3.headers.get('Content-Type')?.startsWith('image/')) {
                const contentType3 = response3.headers.get('Content-Type') || '';
                const contentLength3 = parseInt(response3.headers.get('Content-Length') || '0');
                
                if (contentLength3 > 0) {
                    const buffer3 = await response3.arrayBuffer();

                    console.log('✅ Imagem carregada com sucesso (estratégia 3):', {
                        fileId,
                        contentType: contentType3,
                        bufferSize: buffer3.byteLength
                    });

                    imageCache.set(fileId, {
                        buffer: buffer3,
                        contentType: contentType3,
                        timestamp: Date.now()
                    });

                    return new NextResponse(buffer3, {
                        headers: {
                            'Content-Type': contentType3,
                            'Cache-Control': 'public, max-age=86400',
                            'X-Cache': 'MISS'
                        },
                    });
                } else {
                    console.log('⚠️ Estratégia 3 retornou imagem vazia:', {
                        contentType: contentType3,
                        contentLength: contentLength3
                    });
                }
            }

            // Estratégia 4: URL de download direto (última tentativa)
            console.log('🔗 Tentando estratégia 4 com download direto...');
            const downloadUrl = `https://drive.google.com/uc?export=download&id=${fileId}`;
            
            const controller4 = new AbortController();
            const timeoutId4 = setTimeout(() => controller4.abort(), 10000);

            const response4 = await fetch(downloadUrl, {
                headers: {
                    'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/121.0.0.0 Safari/537.36',
                    'Referer': 'https://drive.google.com/',
                },
                signal: controller4.signal
            });

            clearTimeout(timeoutId4);

            if (response4.ok) {
                const contentType4 = response4.headers.get('Content-Type') || '';
                const contentLength4 = parseInt(response4.headers.get('Content-Length') || '0');
                
                // Aceitar também application/octet-stream que pode ser uma imagem
                if ((contentType4.startsWith('image/') || contentType4.includes('octet-stream')) && contentLength4 > 0) {
                    const buffer4 = await response4.arrayBuffer();

                    console.log('✅ Imagem carregada com sucesso (estratégia 4):', {
                        fileId,
                        contentType: contentType4,
                        bufferSize: buffer4.byteLength
                    });

                    imageCache.set(fileId, {
                        buffer: buffer4,
                        contentType: contentType4.startsWith('image/') ? contentType4 : 'image/jpeg',
                        timestamp: Date.now()
                    });

                    return new NextResponse(buffer4, {
                        headers: {
                            'Content-Type': contentType4.startsWith('image/') ? contentType4 : 'image/jpeg',
                            'Cache-Control': 'public, max-age=86400',
                            'X-Cache': 'MISS'
                        },
                    });
                }
            }

            throw new Error(`Todas as estratégias falharam. Última resposta: ${response4.status}`);

        } catch (error) {
            clearTimeout(timeoutId);

            const fetchError = error as Error;
            console.error('❌ Erro ao buscar imagem:', {
                fileId,
                error: fetchError.message,
                isTimeout: fetchError.name === 'AbortError'
            });

            if (fetchError && fetchError.name === 'AbortError') {
                throw new Error('Timeout ao buscar imagem');
            }

            throw error;
        }
    } catch (error) {
        console.error('❌ Erro geral no drive-proxy:', {
            fileId,
            error: error instanceof Error ? error.message : 'Erro desconhecido'
        });

        try {
            const placeholderPath = path.join(process.cwd(), 'public', 'images', 'placeholder.png');
            const placeholderBuffer = await fs.readFile(placeholderPath);
            console.log('🖼️ Retornando placeholder para:', fileId);

            return new NextResponse(placeholderBuffer, {
                headers: {
                    'Content-Type': 'image/png',
                    'Cache-Control': 'public, max-age=3600',
                    'X-Error': 'true'
                },
            });
        } catch (placeholderError) {
            console.error('❌ Erro ao carregar placeholder:', placeholderError);
            return NextResponse.json({ error: 'Falha ao obter a imagem do Google Drive' }, { status: 500 });
        }
    }
}