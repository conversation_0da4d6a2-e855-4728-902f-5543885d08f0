import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function PATCH(request: Request) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Não autenticado" },
                { status: 401 }
            );
        }

        const pathParts = new URL(request.url).pathname.split('/');
        const id = pathParts[pathParts.indexOf('contents') + 1];

        if (!id) {
            return NextResponse.json(
                { error: "ID do conteúdo é obrigatório" },
                { status: 400 }
            );
        }

        let { urlStructuringFeed, urlsWithTypes, urlFolder } = await request.json();
        const _urlsWithTypes = urlsWithTypes;
        const _urlFolder = urlFolder;
        urlsWithTypes = _urlsWithTypes;
        urlFolder = _urlFolder;

        if (typeof urlStructuringFeed === 'string') {
            urlStructuringFeed = urlStructuringFeed.trim() ? [urlStructuringFeed.trim()] : [];
        }
        if (!Array.isArray(urlStructuringFeed)) {
            return NextResponse.json(
                { error: "urlStructuringFeed deve ser um array de strings" },
                { status: 400 }
            );
        }
        for (const url of urlStructuringFeed) {
            if (typeof url !== 'string' || !url.trim()) {
                return NextResponse.json(
                    { error: "Todas as URLs devem ser strings não vazias" },
                    { status: 400 }
                );
            }
        }

        let urlTypes: string[] = [];
        let urlMediaTypes: string[] = [];
        let urlThumbnails: string[] = [];
        
        if (urlsWithTypes && Array.isArray(urlsWithTypes)) {
            urlTypes = urlsWithTypes.map(item => item.type || 'feed');
            urlMediaTypes = urlsWithTypes.map(item => item.mediaType || 'foto');
            urlThumbnails = urlsWithTypes.map(item => item.thumbnailUrl || '');
            
            while (urlTypes.length < urlStructuringFeed.length) {
                urlTypes.push('feed');
            }
            
            while (urlMediaTypes.length < urlStructuringFeed.length) {
                urlMediaTypes.push('foto');
            }
            
            while (urlThumbnails.length < urlStructuringFeed.length) {
                urlThumbnails.push('');
            }
        } else {
            urlTypes = new Array(urlStructuringFeed.length).fill('feed');
            urlMediaTypes = new Array(urlStructuringFeed.length).fill('foto');
            urlThumbnails = new Array(urlStructuringFeed.length).fill('');
        }

        const content = await prisma.content.findUnique({
            where: { id },
            include: {
                weeklyActivity: {
                    include: {
                        monthlyPlanning: {
                            include: {
                                client: true
                            }
                        }
                    }
                },
                assignedTo: true
            }
        });

        if (!content) {
            return NextResponse.json(
                { error: "Conteúdo não encontrado" },
                { status: 404 }
            );
        }

        const isNewUrl = (!content.urlStructuringFeed || content.urlStructuringFeed.length === 0) && urlStructuringFeed.length > 0;

        const updatedContent = await prisma.content.update({
            where: { id },
            data: {
                urlStructuringFeed,
                urlTypes,
                urlMediaTypes,
                urlThumbnails,
                urlFolder
            },
        });

        if (isNewUrl) {
            const clientName = content.weeklyActivity.monthlyPlanning.client.name;
            const contentType = content.contentType;
            const userName = session.user.name || "Um usuário";

            await prisma.notification.create({
                data: {
                    content: `${userName} adicionou uma URL à demanda ${content.id.slice(0, 8).toUpperCase()} de ${clientName} (${contentType})`,
                    type: "url_added",
                    entityId: id,
                    entityType: "content",
                    reference: `/admin/demands`,
                    userId: null,
                    importance: "normal"
                }
            });
        }

        return NextResponse.json(updatedContent, { status: 200 });
    } catch (error) {
        console.error("Erro ao atualizar URL da estruturação:", error);

        if (error instanceof Error) {
            return NextResponse.json(
                { error: error.message },
                { status: 400 }
            );
        }

        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}