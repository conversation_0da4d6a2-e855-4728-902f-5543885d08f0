import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { Prisma } from "@prisma/client";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function PATCH(request: Request) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Não autenticado" },
                { status: 401 }
            );
        }

        const pathParts = new URL(request.url).pathname.split('/');
        const id = pathParts[pathParts.indexOf('contents') + 1];

        if (!id) {
            return NextResponse.json(
                { error: "ID do conteúdo é obrigatório" },
                { status: 400 }
            );
        }

    const { urlStructuringFeed, urlsWithTypes, urlFolder } = await request.json();

        // Normalize urlStructuringFeed to support multiple shapes:
        // - string
        // - string[]
        // - Array of { url: string, review?: string }
        // We'll produce two parallel arrays to store in the DB:
        // - urlStructuringFeed: string[] (only urls)
        // - urlStructuringFeedReviews: string[] (review text or empty string)

        const normalizedUrls: string[] = [];
        const normalizedReviews: string[] = [];

        if (typeof urlStructuringFeed === 'string') {
            if (urlStructuringFeed.trim()) {
                normalizedUrls.push(urlStructuringFeed.trim());
                normalizedReviews.push('');
            }
        } else if (Array.isArray(urlStructuringFeed)) {
            for (const item of urlStructuringFeed) {
                if (typeof item === 'string') {
                    const u = item.trim();
                    if (u) {
                        normalizedUrls.push(u);
                        normalizedReviews.push('');
                    }
                } else if (item && typeof item === 'object' && typeof item.url === 'string') {
                    const u = String(item.url).trim();
                    if (u) {
                        normalizedUrls.push(u);
                        normalizedReviews.push(item.review ? String(item.review) : '');
                    }
                } else {
                    return NextResponse.json(
                        { error: 'Itens de urlStructuringFeed devem ser string ou {url, review?}.' },
                        { status: 400 }
                    );
                }
            }
        } else if (urlStructuringFeed == null) {
            // keep empty arrays
        } else {
            return NextResponse.json(
                { error: 'urlStructuringFeed inválido' },
                { status: 400 }
            );
        }

    let urlTypes: string[] = [];
    let urlMediaTypes: string[] = [];
    let urlThumbnails: string[] = [];
        
        if (urlsWithTypes && Array.isArray(urlsWithTypes)) {
            urlTypes = urlsWithTypes.map(item => item.type || 'feed');
            urlMediaTypes = urlsWithTypes.map(item => item.mediaType || 'foto');
            urlThumbnails = urlsWithTypes.map(item => item.thumbnailUrl || '');
            
            while (urlTypes.length < urlStructuringFeed.length) {
                urlTypes.push('feed');
            }
            
            while (urlMediaTypes.length < urlStructuringFeed.length) {
                urlMediaTypes.push('foto');
            }
            
            while (urlThumbnails.length < normalizedUrls.length) {
                urlThumbnails.push('');
            }
        } else {
            urlTypes = new Array(normalizedUrls.length).fill('feed');
            urlMediaTypes = new Array(normalizedUrls.length).fill('foto');
            urlThumbnails = new Array(normalizedUrls.length).fill('');
        }

        const content = await prisma.content.findUnique({
            where: { id },
            include: {
                weeklyActivity: {
                    include: {
                        monthlyPlanning: {
                            include: {
                                client: true
                            }
                        }
                    }
                },
                assignedTo: true
            }
        });

        if (!content) {
            return NextResponse.json(
                { error: "Conteúdo não encontrado" },
                { status: 404 }
            );
        }

        const isNewUrl = (!content.urlStructuringFeed || content.urlStructuringFeed.length === 0) && normalizedUrls.length > 0;

        const dataForUpdate = {
            urlStructuringFeed: normalizedUrls,
            urlTypes,
            urlMediaTypes,
            urlThumbnails,
            urlFolder,
            urlStructuringFeedReviews: normalizedReviews
        } as unknown as Prisma.ContentUpdateInput;

        const updatedContent = await prisma.content.update({
            where: { id },
            data: dataForUpdate,
        });

        if (isNewUrl) {
            const clientName = content.weeklyActivity.monthlyPlanning.client.name;
            const contentType = content.contentType;
            const userName = session.user.name || "Um usuário";

            await prisma.notification.create({
                data: {
                    content: `${userName} adicionou uma arquivo à demanda ${content.id.slice(0, 8).toUpperCase()} de ${clientName} (${contentType})`,
                    type: "url_added",
                    entityId: id,
                    entityType: "content",
                    reference: `/admin/demands`,
                    userId: null,
                    importance: "normal"
                }
            });
        }

        return NextResponse.json(updatedContent, { status: 200 });
    } catch (error) {
        console.error("Erro ao atualizar URL da estruturação:", error);

        if (error instanceof Error) {
            return NextResponse.json(
                { error: error.message },
                { status: 400 }
            );
        }

        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}