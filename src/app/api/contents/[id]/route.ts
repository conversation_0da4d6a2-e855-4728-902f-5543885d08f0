import prisma from "@/app/lib/prisma";
import { StepType } from "@prisma/client";
import { NextResponse } from "next/server";

interface ContentUpdateData {
    activityDate?: Date;
    contentType?: string;
    channel?: string | null;
    details?: string | null;
    destination?: string;
    copywriting?: string | null;
    caption?: string | null;
    reference?: string | null;
    priority?: string | null;
    position?: number | null;
    status?: string | null;
    weeklyActivityId?: string;
    urlStructuringFeed?: string[];
    assignedToId?: string | null;
    carouselImagesCount?: number | null;
    review?: string | null;
}

interface ContentStep {
    type: string;
    assignedToId: string;
}

export async function GET(request: Request) {
    try {
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();

        if (!id) {
            return NextResponse.json({ message: "ID não fornecido" }, { status: 400 });
        }

        const content = await prisma.content.findUnique({
            where: { id },
            include: {
                assignedTo: true
            }
        });

        if (!content) {
            return NextResponse.json({ message: "Conteúdo não encontrado" }, { status: 404 });
        }

        return new NextResponse(JSON.stringify(content), {
            status: 200,
            headers: {
                'Content-Type': 'application/json',
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });
    } catch (error) {
        return NextResponse.json(
            { message: "Erro interno do servidor", error },
            { status: 500 }
        );
    }
}

export async function PUT(request: Request) {
    try {
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();

        const data = await request.json();
        
        const {
            activityDate,
            contentType,
            channel,
            destination,
            details,
            copywriting,
            caption,
            reference,
            priority,
            position,
            status,
            weeklyActivityId,
            urlStructuringFeed,
            assignedToId,
            carouselImagesCount,
            review,
            steps
        } = data;

        const existingContent = await prisma.content.findUnique({
            where: { id }
        });

        if (!existingContent) {
            return NextResponse.json(
                { message: "Conteúdo não encontrado" },
                { status: 404 }
            );
        }

        const updateData: ContentUpdateData = {};


        if (data.hasOwnProperty('activityDate')) {
            updateData.activityDate = new Date(activityDate);
        }
        if (data.hasOwnProperty('contentType')) {
            updateData.contentType = contentType;
        }
        if (data.hasOwnProperty('channel')) {
            updateData.channel = channel;
        }
        if (data.hasOwnProperty('destination')) {
            updateData.destination = destination;
        }
        if (data.hasOwnProperty('details')) {
            updateData.details = details;
        }

        if (data.hasOwnProperty('copywriting')) {
            updateData.copywriting = copywriting;
        }

        if (data.hasOwnProperty('caption')) {
            updateData.caption = caption;
        }

        if (data.hasOwnProperty('reference')) {
            updateData.reference = reference;
        }

        if (data.hasOwnProperty('priority')) {
            updateData.priority = priority;
        }

        if (data.hasOwnProperty('position')) {
            updateData.position = position;
        }

        if (data.hasOwnProperty('status')) {
            updateData.status = status;
        }

        if (data.hasOwnProperty('weeklyActivityId')) {
            updateData.weeklyActivityId = weeklyActivityId;
        }

        if (data.hasOwnProperty('urlStructuringFeed')) {
            updateData.urlStructuringFeed = urlStructuringFeed;
        }

        if (data.hasOwnProperty('carouselImagesCount')) {
            updateData.carouselImagesCount = carouselImagesCount;
        }

        if (data.hasOwnProperty('assignedToId')) {
            updateData.assignedToId = assignedToId;
            if (assignedToId && !data.hasOwnProperty('status')) {
                updateData.status = "repassado";
            }
        }
        
        if (data.hasOwnProperty('review')) {
            updateData.review = review;
        }

        try {
            const updatedContent = await prisma.content.update({
                where: { id },
                data: updateData,
                include: {
                    assignedTo: true,
                    weeklyActivity: {
                        include: {
                            monthlyPlanning: {
                                include: {
                                    client: true
                                }
                            }
                        }
                    },
                    steps: {
                        include: {
                            assignedTo: true
                        }
                    }
                }
            });

            if (data.hasOwnProperty('review') && 
                review && 
                review.trim() !== '' && 
                updatedContent.assignedToId) {
                
                const contentWithRelations = await prisma.content.findUnique({
                    where: { id: updatedContent.id },
                    include: {
                        assignedTo: true,
                        weeklyActivity: {
                            include: {
                                monthlyPlanning: {
                                    include: {
                                        client: true
                                    }
                                }
                            }
                        }
                    }
                });
                
                if (contentWithRelations?.assignedTo?.email) {
                    const clientName = contentWithRelations.weeklyActivity?.monthlyPlanning?.client?.name || 'Cliente não especificado';
                    
                    await prisma.notification.create({
                        data: {
                            content: `Alteração solicitada no conteúdo: ${updatedContent.details || 'Sem descrição'} (${clientName})`,
                            type: "review_content",
                            entityId: updatedContent.id,
                            entityType: "content",
                            userId: contentWithRelations.assignedTo.email,
                            importance: "high",
                        },
                    });
                }
            }

            if (steps && Array.isArray(steps)) {
                await prisma.contentStep.deleteMany({
                    where: { contentId: id }
                });

                if (steps.length > 0) {
                    await Promise.all(
                        steps.map((step: ContentStep) => {
                            if (id) {
                                return prisma.contentStep.create({
                                    data: {
                                        contentId: id,
                                        type: step.type as StepType,
                                        assignedToId: step.assignedToId
                                    }
                                });
                            }
                            return Promise.resolve();
                        })
                    );
                }
            }


            return new NextResponse(JSON.stringify(updatedContent), {
                status: 200,
                headers: {
                    'Content-Type': 'application/json',
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });
        } catch (dbError) {
            return NextResponse.json(
                { message: "Erro ao atualizar o conteúdo no banco de dados", error: dbError instanceof Error ? dbError.message : String(dbError) },
                { status: 500 }
            );
        }
    } catch (error) {
        return NextResponse.json(
            { message: "Erro interno do servidor", error },
            { status: 500 }
        );
    }
}

export async function DELETE(request: Request) {
    try {
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();

        const content = await prisma.content.findUnique({
            where: { id },
        });

        if (!content) {
            return NextResponse.json(
                { message: "Conteúdo não encontrado" },
                { status: 404 }
            );
        }

        await prisma.content.delete({
            where: { id },
        });

        return NextResponse.json({ success: true });
    } catch (error) {
        return NextResponse.json(
            { message: "Erro interno do servidor", error },
            { status: 500 }
        );
    }
}

export async function PATCH(request: Request) {
    try {
        const url = new URL(request.url);
        const id = url.pathname.split('/').pop();
        const body = await request.json();

        if ('status' in body && Object.keys(body).length === 1) {
            const { status } = body;

            const validStatuses = [
                "pendente",
                "em andamento",
                "estruturação de feed",
                "feed estruturado",
                "repassado",
                "em revisão",
                "alteração",
                "pend. captação",
                "captado",
                "anúncio concluído",
                "concluído"
            ];

            if (!validStatuses.includes(status)) {
                return NextResponse.json(
                    {
                        message: "Status inválido",
                        validOptions: validStatuses
                    },
                    { status: 400 }
                );
            }

            try {
                const updatedContent = await prisma.content.update({
                    where: { id },
                    data: { status },
                    include: {
                        assignedTo: true,
                        weeklyActivity: {
                            include: {
                                monthlyPlanning: {
                                    include: {
                                        client: true
                                    }
                                }
                            }
                        }
                    }
                });

                return NextResponse.json(updatedContent);
            } catch (prismaError) {
                console.error("Erro ao atualizar status do conteúdo:", prismaError);
                return NextResponse.json(
                    { message: "Conteúdo não encontrado" },
                    { status: 404 }
                );
            }
        } else {
            return NextResponse.json({ message: "Operação não suportada" }, { status: 400 });
        }
    } catch (error) {
        return NextResponse.json(
            { message: "Erro interno do servidor", error },
            { status: 500 }
        );
    }
}