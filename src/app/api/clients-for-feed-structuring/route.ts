import prisma from '@/app/lib/prisma';
import { NextResponse } from 'next/server';

export async function GET(req: Request) {
    try {
        const url = new URL(req.url);
        const month = url.searchParams.get('month');
        const year = url.searchParams.get('year');

        type PlanningWhere = { status: string; showActivitiesInModuleThree: boolean; month?: number; year?: number };

        const planningWhere: PlanningWhere = {
            status: 'aprovado',
            showActivitiesInModuleThree: true
        };

        if (month) planningWhere.month = parseInt(month);
        if (year) planningWhere.year = parseInt(year);

        const clients = await prisma.client.findMany({
            where: {
                archived: false,
                monthlyPlannings: {
                    some: planningWhere
                }
            },
            select: {
                id: true,
                name: true,
                monthlyPlannings: {
                    where: planningWhere,
                    select: {
                        id: true,
                        month: true,
                        year: true
                    }
                }
            },
            orderBy: { name: 'asc' }
        });

        const mapped = clients.map((c) => ({
            clientId: c.id,
            clientName: c.name,
            planningsCount: c.monthlyPlannings.length,
            plannings: c.monthlyPlannings
        }));

        return NextResponse.json(mapped);
    } catch (error) {
        console.error('Erro ao buscar clientes para estruturação de feed:', error);
        return NextResponse.json({ message: 'Erro interno do servidor' }, { status: 500 });
    }
}
