import { NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      return NextResponse.json(
        { message: "Não autenticado" },
        { status: 401 }
      );
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
      select: { id: true }
    });

    if (!user) {
      return NextResponse.json(
        { message: "Usuário não encontrado" },
        { status: 404 }
      );
    }

    const contentDemands = await prisma.content.findMany({
      where: {
        assignedToId: user.id
      },
      include: {
        weeklyActivity: {
          include: {
            monthlyPlanning: {
              include: {
                client: {
                  select: {
                    id: true,
                    name: true,
                    instagramUsername: true
                  }
                }
              }
            }
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        steps: {
          include: {
            assignedTo: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      },
      orderBy: {
        activityDate: 'desc'
      }
    });

    const stepContentDemands = await prisma.content.findMany({
      where: {
        steps: {
          some: {
            assignedToId: user.id
          }
        }
      },
      include: {
        weeklyActivity: {
          include: {
            monthlyPlanning: {
              include: {
                client: {
                  select: {
                    id: true,
                    name: true,
                    instagramUsername: true
                  }
                }
              }
            }
          }
        },
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        },
        steps: {
          include: {
            assignedTo: {
              select: {
                id: true,
                name: true,
                email: true,
                image: true
              }
            }
          }
        }
      },
      orderBy: {
        activityDate: 'desc'
      }
    });

    const formattedContentDemands = contentDemands.map(content => ({
      ...content,
      type: 'content',
      archived: content.archived
    }));

    const formattedStepContentDemands = stepContentDemands.map(content => ({
      ...content,
      type: 'content',
      archived: content.archived
    }));

    const generalDemands = await prisma.generalDemand.findMany({
      where: {
        assignedToId: user.id
      },
      select: {
        id: true,
        title: true,
        description: true,
        dueDate: true,
        status: true,
        priority: true,
        position: true,
        clientId: true,
        looseClientId: true,
        archived: true,
        review: true,
        urlStructuringFeed: true,
        urlTypes: true,
        client: true,
        looseClient: true,
        assignedTo: {
          select: {
            id: true,
            name: true,
            email: true,
            image: true
          }
        }
      },
      orderBy: [
        {
          position: 'asc'
        },
        {
          dueDate: 'asc'
        }
      ]
    });

    const formattedGeneralDemands = generalDemands.map(demand => {
      const clientInfo = demand.client
        ? {
          id: demand.client.id,
          name: demand.client.name,
          instagramUsername: demand.client.instagramUsername
        }
        : demand.looseClient
          ? {
            id: demand.looseClient.id,
            name: `${demand.looseClient.name} ⦁ não fixo`,
            instagramUsername: null
          }
          : {
            id: 'unknown',
            name: 'Cliente não especificado',
            instagramUsername: null
          };

      return {
        id: demand.id,
        type: 'general',
        activityDate: demand.dueDate,
        title: demand.title,
        description: demand.description,
        status: demand.status,
        priority: demand.priority,
        position: demand.position || 9999,
        client: clientInfo,
        assignedTo: demand.assignedTo,
        weeklyActivity: null,
        isLooseClient: !!demand.looseClient,
        archived: demand.archived,
        review: demand.review,
        urlStructuringFeed: demand.urlStructuringFeed,
        urlTypes: demand.urlTypes
      };
    });

    const allDemands = [...formattedContentDemands, ...formattedStepContentDemands, ...formattedGeneralDemands];

    const uniqueDemands = allDemands.filter((demand, index, array) =>
      array.findIndex(d => d.id === demand.id) === index
    );

    const sortedDemands = uniqueDemands.sort((a, b) => {
      const posA = a.position || 9999;
      const posB = b.position || 9999;

      if (posA !== posB) {
        return posA - posB;
      }

      const today = new Date();
      const currentMonth = today.getMonth();
      const currentYear = today.getFullYear();

      const dateA = a.activityDate ? new Date(a.activityDate) : new Date(0);
      const dateB = b.activityDate ? new Date(b.activityDate) : new Date(0);

      const yearA = dateA.getFullYear();
      const yearB = dateB.getFullYear();
      const monthA = dateA.getMonth();
      const monthB = dateB.getMonth();
      const dayA = dateA.getDate();
      const dayB = dateB.getDate();

      const isDateACurrentMonth = monthA === currentMonth && yearA === currentYear;
      const isDateBCurrentMonth = monthB === currentMonth && yearB === currentYear;

      if (isDateACurrentMonth && !isDateBCurrentMonth) {
        return -1;
      }
      if (!isDateACurrentMonth && isDateBCurrentMonth) {
        return 1;
      }

      if (isDateACurrentMonth && isDateBCurrentMonth) {
        return dayA - dayB;
      }

      if (yearA !== yearB) {
        return yearB - yearA;
      }

      if (monthA !== monthB) {
        return monthB - monthA;
      }

      return dayA - dayB;
    });

    return NextResponse.json(sortedDemands);
  } catch (error) {
    console.error("Error fetching user demands:", error);
    return NextResponse.json(
      { message: "Erro interno do servidor", error },
      { status: 500 }
    );
  }
}
