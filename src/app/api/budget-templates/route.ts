import { NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';
import { Prisma } from '@prisma/client';

type TemplateFieldInput = {
  key: string;
  label?: string;
  type?: string;
  required?: boolean;
  options?: string[] | null;
  defaultValue?: string | null;
  sortOrder?: number;
};

const db = prisma;

export async function GET(req: Request) {
  try {
    const url = new URL(req.url);
    const id = url.searchParams.get('id');

    if (id) {
      const tmpl = await db.budgetTemplate.findUnique({
        where: { id },
        include: { services: { orderBy: { sortOrder: 'asc' } } }
      });
      if (!tmpl) return NextResponse.json({ error: 'Template não encontrado' }, { status: 404 });
      return NextResponse.json(tmpl);
    }

    const templates = await db.budgetTemplate.findMany({
      where: { active: true },
      orderBy: { name: 'asc' },
      select: { id: true, name: true, description: true, active: true }
    });
    return NextResponse.json(templates);
  } catch (err) {
    console.error('GET /api/budget-templates error', err);
    return NextResponse.json({ error: 'Erro ao listar modelos de orçamento' }, { status: 500 });
  }
}

export async function POST(req: Request) {
  try {
    const body = await req.json();
  const { name, description, active = true, fields } = body;

    if (!name || typeof name !== 'string') {
      return NextResponse.json({ error: 'Nome inválido' }, { status: 400 });
    }

  const created = await db.budgetTemplate.create({
      data: {
        name,
        description: description ?? null,
        active: !!active,
        services: fields && Array.isArray(fields) ? {
      create: (fields.map((f: TemplateFieldInput) => ({
            key: f.key,
            label: f.label ?? f.key,
            type: f.type,
            required: !!f.required,
            options: f.options ?? null,
            defaultValue: f.defaultValue ?? null,
            sortOrder: typeof f.sortOrder === 'number' ? f.sortOrder : 0
          })) as unknown) as Prisma.TemplateServicesCreateWithoutBudgetTemplateInput[]
        } : undefined
      },
      include: { services: true }
    });

    return NextResponse.json(created, { status: 201 });
  } catch (err) {
    console.error('POST /api/budget-templates error', err);
    return NextResponse.json({ error: 'Erro ao criar modelo de orçamento' }, { status: 500 });
  }
}

export async function PATCH(req: Request) {
  try {
    const body = await req.json();
  const { id, name, description, active, fields } = body;

    if (!id || typeof id !== 'string') {
      return NextResponse.json({ error: 'Id do template é obrigatório' }, { status: 400 });
    }

  const exists = await db.budgetTemplate.findUnique({ where: { id } });
    if (!exists) return NextResponse.json({ error: 'Template não encontrado' }, { status: 404 });

    await db.budgetTemplate.update({
      where: { id },
      data: {
        ...(name ? { name } : {}),
        ...(typeof description !== 'undefined' ? { description: description ?? null } : {}),
        ...(typeof active !== 'undefined' ? { active: !!active } : {})
      },
      include: { services: true }
    });

    if (Array.isArray(fields)) {
  await db.templateServices.deleteMany({ where: { budgetTemplateId: id } });
      if (fields.length > 0) {
  await db.templateServices.createMany({
          data: (fields.map((f: TemplateFieldInput, idx: number) => ({
            budgetTemplateId: id,
            key: f.key,
            label: f.label ?? f.key,
            type: f.type,
            required: !!f.required,
            options: f.options ?? null,
            defaultValue: f.defaultValue ?? null,
            sortOrder: typeof f.sortOrder === 'number' ? f.sortOrder : idx
          })) as unknown) as Prisma.TemplateServicesCreateManyInput[]
        });
      }
    }

  const reloaded = await db.budgetTemplate.findUnique({ where: { id }, include: { services: true } });
    return NextResponse.json(reloaded);
  } catch (err) {
    console.error('PATCH /api/budget-templates error', err);
    return NextResponse.json({ error: 'Erro ao atualizar modelo de orçamento' }, { status: 500 });
  }
}

export async function DELETE(req: Request) {
  try {
    const url = new URL(req.url);
    const id = url.searchParams.get('id');
    if (!id) return NextResponse.json({ error: 'Id do template é obrigatório' }, { status: 400 });

  await db.budgetTemplate.update({ where: { id }, data: { active: false } });
    return NextResponse.json({ ok: true });
  } catch (err) {
    console.error('DELETE /api/budget-templates error', err);
    return NextResponse.json({ error: 'Erro ao remover modelo de orçamento' }, { status: 500 });
  }
}
