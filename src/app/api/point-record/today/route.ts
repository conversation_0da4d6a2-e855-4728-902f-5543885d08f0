import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function GET() {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user) {
            return NextResponse.json({ error: "Usuário não encontrado" }, { status: 404 });
        }

        const today = new Date();
        const todayDate = new Date(today.getFullYear(), today.getMonth(), today.getDate());

        const allRecords = await prisma.pointRecord.findMany({
            where: {
                userId: user.id,
                date: todayDate
            },
            orderBy: { clockIn: 'asc' }
        });

        const openRecord = allRecords.find(record => record.clockIn && !record.clockOut);
        
        const completedRecords = allRecords.filter(record => record.clockIn && record.clockOut);

        if (openRecord) {
            return NextResponse.json({
                id: openRecord.id,
                clockIn: openRecord.clockIn?.toISOString(),
                clockOut: null,
                date: openRecord.date.toISOString().split('T')[0],
                totalHours: openRecord.totalHours,
                hasOpenEntry: true,
                previousRecords: completedRecords.map(record => ({
                    id: record.id,
                    clockIn: record.clockIn?.toISOString(),
                    clockOut: record.clockOut?.toISOString(),
                    totalHours: record.totalHours
                }))
            });
        }

        return NextResponse.json({ 
            hasOpenEntry: false,
            previousRecords: completedRecords.map(record => ({
                id: record.id,
                clockIn: record.clockIn?.toISOString(),
                clockOut: record.clockOut?.toISOString(),
                totalHours: record.totalHours
            }))
        });

    } catch (error) {
        console.error("Erro ao buscar registro de hoje:", error);
        return NextResponse.json({ error: "Erro interno do servidor" }, { status: 500 });
    }
}