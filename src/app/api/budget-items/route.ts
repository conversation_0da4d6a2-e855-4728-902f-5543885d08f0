import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';

export async function GET(req: NextRequest) {
    try {
        const url = new URL(req.url);
        const templateId = url.searchParams.get('templateId') || undefined;
        const items = await prisma.budgetItem.findMany({
            where: templateId ? { budgetTemplateId: templateId } : {},
            orderBy: { sortOrder: 'asc' },
            select: { id: true, code: true, name: true, description: true }
        });
        return NextResponse.json(items);
    } catch (err) {
        console.error('GET /api/budget-items error', err);
        return NextResponse.json({ error: 'Erro ao listar itens de orçamento' }, { status: 500 });
    }
}
