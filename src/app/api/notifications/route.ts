import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { Prisma } from "@prisma/client";

export async function GET(request: Request) {
    try {
        const { searchParams } = new URL(request.url);
        const unreadOnly = searchParams.get('unread') === 'true';

        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json([], { status: 200 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email || "" },
            select: { role: true },
        });

        const isAdmin = user?.role === "ADMIN" || user?.role === "DEVELOPER" || user?.role === "GENERAL_ASSISTANT";

        const query: Prisma.NotificationFindManyArgs = {
            where: {
                OR: [
                    { userId: session.user.email || undefined },
                    {
                        userId: null,
                        ...(!isAdmin ? {
                            NOT: {
                                type: {
                                    in: ["new_feedback", "url_added"]
                                }
                            }
                        } : {})
                    }
                ],
                NOT: {
                    type: "status_change"
                },
                ...(unreadOnly ? { isRead: false } : {})
            },
            orderBy: {
                createdAt: 'desc'
            },
            take: unreadOnly ? 20 : 100
        };

        const notifications = await prisma.notification.findMany(query);

        type VirtualNotification = {
            id: string;
            content: string;
            type: string;
            entityId: string;
            entityType: string;
            reference: string;
            isRead: boolean;
            createdAt: Date;
            importance?: string;
        };

        const virtuals: VirtualNotification[] = [];
        if (isAdmin) {
            const getUTCYMD = (d: Date) => ({ y: d.getUTCFullYear(), m: d.getUTCMonth(), d: d.getUTCDate() });
            const toEpochDay = (y: number, m: number, d: number) => Math.floor(Date.UTC(y, m, d) / 86400000);

            const now = new Date();
            const t = getUTCYMD(now);
            const todayEpoch = toEpochDay(t.y, t.m, t.d);

            const users = await prisma.user.findMany({
                where: { archived: false },
                select: { id: true, name: true, birthDate: true, hireDate: true }
            });

            const calcNextOccurrenceUTC = (raw?: Date | null) => {
                if (!raw) return null;
                const { m, d } = getUTCYMD(new Date(raw));
                let y = t.y;
                if (toEpochDay(y, m, d) < todayEpoch) y = y + 1;
                return new Date(Date.UTC(y, m, d, 12, 0, 0));
            };

            for (const u of users) {
                const nextBirthday = calcNextOccurrenceUTC(u.birthDate as Date | null);
                const nextHire = calcNextOccurrenceUTC(u.hireDate as Date | null);

                if (nextBirthday) {
                    const nb = getUTCYMD(nextBirthday);
                    const days = toEpochDay(nb.y, nb.m, nb.d) - todayEpoch;
                    if (days === 0 || days === 7 || days === 15) {
                        virtuals.push({
                            id: `virtual-birthday-${u.id}-${nextBirthday.toISOString().slice(0,10)}`,
                            content: `${u.name || 'Colaborador'} faz aniversário em ${days === 0 ? 'hoje' : `${days} dia(s)`}.`,
                            type: 'upcoming_birthday',
                            entityId: u.id,
                            entityType: 'user',
                            reference: '/admin/users',
                            isRead: false,
                            createdAt: new Date(Date.UTC(nb.y, nb.m, nb.d, 12, 0, 0)),
                            importance: days === 0 ? 'high' : 'normal'
                        });
                    }
                }

                if (nextHire) {
                    const nh = getUTCYMD(nextHire);
                    const days = toEpochDay(nh.y, nh.m, nh.d) - todayEpoch;
                    if (days === 0 || days === 7 || days === 15) {
                        virtuals.push({
                            id: `virtual-hire-${u.id}-${nextHire.toISOString().slice(0,10)}`,
                            content: `${u.name || 'Colaborador'} completa aniversário de contratação em ${days === 0 ? 'hoje' : `${days} dia(s)`}.`,
                            type: 'upcoming_hire',
                            entityId: u.id,
                            entityType: 'user',
                            reference: '/admin/users',
                            isRead: false,
                            createdAt: new Date(Date.UTC(nh.y, nh.m, nh.d, 12, 0, 0)),
                            importance: days === 0 ? 'high' : 'normal'
                        });
                    }
                }
            }
        }

        const all = [...notifications, ...virtuals].sort((a, b) => new Date(a.createdAt).getTime() < new Date(b.createdAt).getTime() ? 1 : -1);
        return NextResponse.json(all, { status: 200 });
    } catch (error) {
        console.error("Erro ao buscar notificações:", error);
        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}

export async function PATCH(request: Request) {
    try {
        const { id, isRead } = await request.json();

        if (!id) {
            return NextResponse.json(
                { error: "ID da notificação é obrigatório" },
                { status: 400 }
            );
        }

        const notification = await prisma.notification.update({
            where: { id },
            data: { isRead: isRead ?? true }
        });

        return NextResponse.json(notification, { status: 200 });
    } catch (error) {
        console.error("Erro ao atualizar notificação:", error);
        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}