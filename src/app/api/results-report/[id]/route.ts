import { NextResponse } from 'next/server';
import prisma from '@/app/lib/prisma';

export async function PUT(req: Request) {
    try {
        const url = new URL(req.url);
        const id = url.pathname.split('/').pop();
        if (!id) {
            return NextResponse.json({ error: 'ID do relatório é obrigatório' }, { status: 400 });
        }
        
        const body = await req.json();
        const { results } = body;

        const existingReport = await prisma.resultsReport.findUnique({
            where: { id: id },
            include: { results: true }
        });

        if (!existingReport) {
            return NextResponse.json({ error: 'Relatório não encontrado' }, { status: 404 });
        }

        const resultId = existingReport.results[0]?.id;

        if (!resultId) {
            return NextResponse.json({ error: 'Resultado não encontrado' }, { status: 404 });
        }

        await prisma.result.update({
            where: { id: resultId },
            data: results[0]
        });

        const updatedReport = await prisma.resultsReport.update({
            where: { id: id },
            data: { updatedAt: new Date() },
            include: { results: true }
        });

        return NextResponse.json(updatedReport);
    } catch (error) {
        console.error('Erro ao atualizar relatório:', error);
        return NextResponse.json({ error: 'Erro ao atualizar relatório' }, { status: 500 });
    }
}