import { NextRequest, NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { Role, AccessLevel, WorkArrangement } from "@prisma/client";

type UserUpdateData = {
    name?: string;
    fullName?: string;
    address?: string;
    birthDate?: Date | null;
    hireDate?: Date | null;
    CPF?: string;
    RG?: string;
    workCard?: string;
    children?: number;
    role?: Role;
    accessLevel?: AccessLevel;
    workArrangement?: WorkArrangement;
    salary?: number | null;
};

export async function GET() {
    try {
        const users = await prisma.user.findMany({
            select: {
                id: true,
                name: true,
                fullName: true,
                image: true,
                email: true,
                address: true,
                birthDate: true,
                hireDate: true,
                CPF: true,
                RG: true,
                workCard: true,
                children: true,
                role: true,
                accessLevel: true,
                workArrangement: true,
                salary: true,
                archived: true
            },
        });

        return NextResponse.json(users);
    } catch (error) {
        console.error("Error fetching users:", error);
        return NextResponse.json(
            { error: "Internal server error" },
            { status: 500 }
        );
    }
}

export async function PATCH(req: NextRequest) {
    try {
        const data = await req.json();
        const { id } = data;

        if (!id) {
            return NextResponse.json(
                { error: "ID do usuário é obrigatório" },
                { status: 400 }
            );
        }

        const fieldTransforms: Record<string, (value: unknown) => unknown> = {
            name: (v) => v,
            fullName: (v) => v,
            address: (v) => v,
            birthDate: (v) => v ? new Date(v as string) : null,
            hireDate: (v) => v ? new Date(v as string) : null,
            CPF: (v) => v,
            RG: (v) => v,
            workCard: (v) => v,
            children: (v) => v ? parseInt(v as string) : 0,
            role: (v) => v as Role,
            accessLevel: (v) => v as AccessLevel,
            workArrangement: (v) => v as WorkArrangement,
            salary: (v) => v ? parseFloat(v as string) : null
        };

        const updateData: UserUpdateData = {};
        
        Object.entries(data).forEach(([key, value]) => {
            if (key !== 'id' && value !== undefined && fieldTransforms[key]) {
                (updateData as Record<string, unknown>)[key] = fieldTransforms[key](value);
            }
        });

        if (Object.keys(updateData).length === 0) {
            return NextResponse.json(
                { error: "Nenhum dado válido fornecido para atualização" },
                { status: 400 }
            );
        }

        const updatedUser = await prisma.user.update({
            where: { id },
            data: updateData,
        });

        return NextResponse.json(updatedUser, { status: 200 });
    } catch (error) {
        console.error("Erro ao atualizar usuário:", error);
        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}
