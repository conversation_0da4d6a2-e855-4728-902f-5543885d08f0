import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

const ALLOWED_ROLES = ['ADMIN', 'DEVELOPER'];

export async function POST(req: Request) {
    try {
        const session = await getServerSession(authOptions);
        if (!session) {
            return NextResponse.json({ message: 'Sem sessão' }, { status: 401 });
        }

        const requestingUser = await prisma.user.findUnique({ where: { email: session.user?.email ?? undefined } });
        if (!requestingUser) {
            return NextResponse.json({ message: 'Usu<PERSON>rio não encontrado' }, { status: 404 });
        }

        if (!ALLOWED_ROLES.includes(requestingUser.role)) {
            return NextResponse.json({ message: 'Acesso negado' }, { status: 403 });
        }

        const body = await req.json();
        const { userId, clientId } = body as { userId?: string; clientId?: string };

        if (!userId || !clientId) {
            return NextResponse.json({ message: 'userId and clientId são obrigatórios' }, { status: 400 });
        }

        const user = await prisma.user.findUnique({ where: { id: userId } });
        if (!user) {
            return NextResponse.json({ message: 'Usuário não encontrado' }, { status: 404 });
        }

        const client = await prisma.client.findUnique({ where: { id: clientId } });
        if (!client) {
            return NextResponse.json({ message: 'Cliente não encontrado' }, { status: 404 });
        }

        // Ensure client is not already assigned to another user
        if (client.userId && client.userId !== userId) {
            return NextResponse.json({ message: 'Cliente já está atribuído a outro usuário' }, { status: 409 });
        }

        const updatedClient = await prisma.client.update({
            where: { id: clientId },
            data: { userId },
        });

        return NextResponse.json({ success: true, client: updatedClient }, { status: 200 });
    } catch (error) {
        console.error('Erro ao atribuir cliente:', error);
        return NextResponse.json({ message: 'Erro interno' }, { status: 500 });
    }
}
