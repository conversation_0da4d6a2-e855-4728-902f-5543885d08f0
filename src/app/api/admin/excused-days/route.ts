import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import prisma from "@/app/lib/prisma";

export async function GET() {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const user = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!user || !["ADMIN", "DEVELOPER"].includes(user.role)) {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const excusedDays = await prisma.excusedDay.findMany({
            include: {
                user: {
                    select: {
                        name: true,
                        email: true
                    }
                }
            },
            orderBy: {
                date: 'desc'
            }
        });

        return NextResponse.json(excusedDays);
    } catch (error) {
        console.log(error);
        return NextResponse.json({ error: "Erro interno" }, { status: 500 });
    }
}

export async function POST(request: NextRequest) {
    try {
        const session = await getServerSession(authOptions);
        
        if (!session?.user?.email) {
            return NextResponse.json({ error: "Não autorizado" }, { status: 401 });
        }

        const adminUser = await prisma.user.findUnique({
            where: { email: session.user.email }
        });

        if (!adminUser || !["ADMIN", "DEVELOPER"].includes(adminUser.role)) {
            return NextResponse.json({ error: "Acesso negado" }, { status: 403 });
        }

        const { userId, date, reason } = await request.json();

        const excusedDay = await prisma.excusedDay.create({
            data: {
                userId,
                date: new Date(date + 'T12:00:00'),
                reason,
                createdBy: adminUser.id
            }
        });

        return NextResponse.json(excusedDay);
    } catch (error) {
        console.error("Erro ao criar dia abonado:", error);
        return NextResponse.json({ error: "Erro interno" }, { status: 500 });
    }
}