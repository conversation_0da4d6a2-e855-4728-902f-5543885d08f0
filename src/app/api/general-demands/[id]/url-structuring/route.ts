import { NextResponse } from "next/server";
import prisma from "@/app/lib/prisma";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";

export async function PATCH(request: Request) {
    try {
        const session = await getServerSession(authOptions);

        if (!session?.user) {
            return NextResponse.json(
                { error: "Não autenticado" },
                { status: 401 }
            );
        }

        const pathParts = new URL(request.url).pathname.split('/');
        const generalDemandsIndex = pathParts.indexOf('general-demands');
        const id = generalDemandsIndex !== -1 && generalDemandsIndex + 1 < pathParts.length
            ? pathParts[generalDemandsIndex + 1]
            : '';

        if (!id) {
            return NextResponse.json(
                { error: "ID da demanda é obrigatório" },
                { status: 400 }
            );
        }

        const { urlStructuringFeed, urlsWithTypes } = await request.json();

        if (!Array.isArray(urlStructuringFeed)) {
            return NextResponse.json(
                { error: "urlStructuringFeed deve ser um array de strings" },
                { status: 400 }
            );
        }

        for (const url of urlStructuringFeed) {
            if (typeof url !== 'string' || !url.trim()) {
                return NextResponse.json(
                    { error: "Todas as URLs devem ser strings não vazias" },
                    { status: 400 }
                );
            }
        }

        let urlTypes: string[] = [];
        let urlMediaTypes: string[] = [];
        let urlThumbnails: string[] = [];
        
        if (urlsWithTypes && Array.isArray(urlsWithTypes)) {
            urlTypes = urlsWithTypes.map(item => item.type || 'feed');
            urlMediaTypes = urlsWithTypes.map(item => item.mediaType || 'foto');
            urlThumbnails = urlsWithTypes.map(item => item.thumbnailUrl || '');

            while (urlTypes.length < urlStructuringFeed.length) {
                urlTypes.push('feed');
            }
            
            while (urlMediaTypes.length < urlStructuringFeed.length) {
                urlMediaTypes.push('foto');
            }
            
            while (urlThumbnails.length < urlStructuringFeed.length) {
                urlThumbnails.push('');
            }
        } else {
            urlTypes = new Array(urlStructuringFeed.length).fill('feed');
            urlMediaTypes = new Array(urlStructuringFeed.length).fill('foto');
            urlThumbnails = new Array(urlStructuringFeed.length).fill('');
        }

        const generalDemand = await prisma.generalDemand.findUnique({
            where: { id },
            include: {
                client: true,
                looseClient: true,
                assignedTo: true
            }
        });

        if (!generalDemand) {
            return NextResponse.json(
                { error: "Demanda geral não encontrada" },
                { status: 404 }
            );
        }

        const isNewUrl = (!generalDemand.urlStructuringFeed || generalDemand.urlStructuringFeed.length === 0) && urlStructuringFeed.length > 0;

        const updatedGeneralDemand = await prisma.generalDemand.update({
            where: { id },
            data: {
                urlStructuringFeed,
                urlTypes,
                urlMediaTypes,
                urlThumbnails
            },
        });

        if (isNewUrl) {
            const clientName = generalDemand.client?.name ||
                generalDemand.looseClient?.name ||
                "Cliente não especificado";
            const userName = session.user.name || "Um usuário";

            await prisma.notification.create({
                data: {
                    content: `${userName} adicionou uma URL à demanda ${generalDemand.id.slice(0, 8).toUpperCase()} de ${clientName} (${generalDemand.title})`,
                    type: "url_added",
                    entityId: id,
                    entityType: "general_demand",
                    reference: `/admin/demands`,
                    userId: null,
                    importance: "normal"
                }
            });
        }

        return NextResponse.json({
            ...updatedGeneralDemand,
            urlStructuringFeed
        }, { status: 200 });
    } catch (error) {
        console.error("Erro ao atualizar URL da estruturação para demanda geral:", error);

        if (error instanceof Error) {
            return NextResponse.json(
                { error: error.message },
                { status: 400 }
            );
        }

        return NextResponse.json(
            { error: "Erro interno do servidor" },
            { status: 500 }
        );
    }
}
