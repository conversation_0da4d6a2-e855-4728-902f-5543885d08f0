import prisma from "@/app/lib/prisma";
import { NextResponse } from "next/server";

export async function PATCH(request: Request) {
  try {
    const url = new URL(request.url);

    const segments = url.pathname.split('/');
    const generalDemandsIndex = segments.indexOf('general-demands');
    const id = generalDemandsIndex !== -1 && generalDemandsIndex + 1 < segments.length
      ? segments[generalDemandsIndex + 1]
      : '';

    if (!id) {
      return NextResponse.json({ error: "ID é obrigatório" }, { status: 400 });
    }

    const { status } = await request.json();

    if (!status) {
      return NextResponse.json({ error: "Status é obrigatório" }, { status: 400 });
    }

    const validStatuses = [
      "pendente",
      "em revisão",
      "concluído"
    ];

    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        {
          error: "Status inválido",
          validOptions: validStatuses
        },
        { status: 400 }
      );
    }

    const generalDemand = await prisma.generalDemand.findUnique({
      where: { id },
    });

    if (!generalDemand) {
      return NextResponse.json(
        { error: "Demanda geral não encontrada" },
        { status: 404 }
      );
    }

    const updatedGeneralDemand = await prisma.generalDemand.update({
      where: { id },
      data: { status },
    });

    return NextResponse.json(updatedGeneralDemand);
  } catch (error) {
    console.error("Erro ao atualizar status da demanda geral:", error);
    return NextResponse.json(
      { error: "Erro ao processar a solicitação" },
      { status: 500 }
    );
  }
}
