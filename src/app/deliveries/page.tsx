"use client";

import React, { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { Header } from "@/app/components/header";
import { Footer } from "@/app/components/footer";
import Loading from "@/app/components/ui/loading";
import { NotAllowed } from "@/app/components/not-allowed";
import { Grid3x3 } from "lucide-react";
import { Breadcrumb, BreadcrumbList, BreadcrumbItem, BreadcrumbLink, BreadcrumbPage, BreadcrumbSeparator } from "@/app/components/ui/breadcrumb";
import { Badge } from "@/app/components/ui/badge";
import { DeliveriesImages } from "@/app/components/deliveries-images";

type Client = {
    id: string;
    name?: string | null;
    instagramUsername?: string | null;
};

export default function DeliveriesClientPage() {
    const { data: session, status } = useSession();
    const [client, setClient] = useState<Client | null>(null);
    const [isFetchingClient, setIsFetchingClient] = useState(true);
    const router = useRouter();

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/login");
        }
    }, [status, router]);

    useEffect(() => {
        const fetchClientForUser = async () => {
            if (!session?.user?.email) {
                setIsFetchingClient(false);
                return;
            }

            try {
                setIsFetchingClient(true);
                const res = await fetch(`/api/users/${session.user.email}`);
                if (!res.ok) {
                    throw new Error(`Erro ao buscar usuário: ${res.status}`);
                }
                const user = await res.json();
                setClient(user?.client ?? null);
            } catch (err) {
                console.error("Erro ao buscar cliente do usuário:", err);
                setClient(null);
            } finally {
                setIsFetchingClient(false);
            }
        };

        fetchClientForUser();
    }, [session]);

    if (status === "loading" || isFetchingClient) {
        return (
            <div className="min-h-screen flex flex-col">
                <Header />
                <div className="p-4 xs:px-8 xs:pt-4 xs:pb-8 flex-grow">
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                </div>
                <Footer />
            </div>
        );
    }

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:px-8 xs:pt-4 xs:pb-8 flex-grow">
                <div className="mb-4 flex flex-col xs:flex-row gap-4 justify-between w-full">
                    <Breadcrumb>
                        <BreadcrumbList>
                            <BreadcrumbItem>
                                <BreadcrumbLink href="/dashboard">Dashboard</BreadcrumbLink>
                                <BreadcrumbSeparator />
                            </BreadcrumbItem>
                            <BreadcrumbItem>
                                <BreadcrumbPage>Redes sociais</BreadcrumbPage>
                            </BreadcrumbItem>
                        </BreadcrumbList>
                    </Breadcrumb>
                    <div className="flex flex-col items-start gap-2">
                        <div className="flex items-center gap-2">
                            <h1 className="text-xl uppercase font-geistMono font-semibold tracking-tight">Redes sociais</h1>
                            <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                <Grid3x3 size={20} color="#db5743" />
                            </div>
                        </div>
                        <div className="flex items-center gap-2">
                            <span className="w-2 h-2 bg-primary3"></span>
                            <h2 className="text-sm">
                                {client?.name || '-'}
                            </h2>
                        </div>
                    </div>
                </div>
                {session?.user ? (
                    client ? (
                        <div className="border-t pt-4 border-zinc-200 dark:border-zinc-800">
                            <DeliveriesImages clientId={client.id} />
                        </div>
                    ) : (
                        <div className="min-h-[60vh] flex flex-col items-center justify-center">
                            <h3 className="text-lg font-semibold mb-2">Nenhum cliente vinculado</h3>
                            <p className="text-sm text-muted-foreground mb-4">Seu usuário não possui um cliente atribuído. Entre em contato com um administrador.</p>
                            <Badge variant="secondary">Sem cliente vinculado</Badge>
                        </div>
                    )
                ) : (
                    <NotAllowed page="/dashboard" />
                )}
            </div>
            <Footer />
        </div>
    );
}
