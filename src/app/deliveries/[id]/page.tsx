"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { useRouter, useParams } from "next/navigation";
import { Header } from "@/app/components/header";
import { Footer } from "@/app/components/footer";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { MoveLeft, Package, AppWindowMac, BadgeInfo } from "lucide-react";
import Loading from "@/app/components/ui/loading";
import { NotAllowed } from "@/app/components/not-allowed";
import Link from "next/link";
import Image from "next/image";
import { DeliveriesContent } from "@/app/components/deliveries-content";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import { Separator } from "@/app/components/ui/separator";
import { Badge } from "@/app/components/ui/badge";
import { ClientNavigationModal } from "@/app/components/client-navigation-modal";

interface Client {
    id: string;
    name: string;
    instagramUsername?: string;
    [key: string]: unknown;
}

export default function DeliveriesPage() {
    const { data: session, status } = useSession();
    const [client, setClient] = useState<Client | null>(null);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [hasPermission, setHasPermission] = useState(false);
    const [clientNavigationOpen, setClientNavigationOpen] = useState(false);
    const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
    const [viewedClients, setViewedClients] = useState<{ id: string; name: string }[]>([]);
    const router = useRouter();
    const params = useParams();
    const id = params?.id as string;

    const handleOpenClientNavigation = (clientId: string) => {
        setSelectedClientId(clientId);
        setClientNavigationOpen(true);
    };

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/login");
        }
    }, [status, router]);

    useEffect(() => {
        const recentClients = JSON.parse(localStorage.getItem('viewedClients') || '[]');
        setViewedClients(recentClients);
    }, []);

    useEffect(() => {
        const fetchUserPermissions = async () => {
            if (session?.user?.email) {
                try {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        const allowedRoles = ["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"];
                        setHasPermission(allowedRoles.includes(user?.role));
                    }
                } catch (error) {
                    console.error("Erro ao verificar permissões:", error);
                    setHasPermission(false);
                }
            }
            setIsFetchingRole(false);
        };

        if (session?.user) {
            fetchUserPermissions();
        } else {
            setIsFetchingRole(false);
        }
    }, [session]);

    useEffect(() => {
        if (id && session?.user && hasPermission) {
            fetch(`/api/clients/${id}`)
                .then(res => res.json())
                .then(data => {
                    setClient(data);
                })
                .catch(err => {
                    console.error("Erro ao carregar dados do cliente:", err);
                });
        }
    }, [id, session, hasPermission]);

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:px-8 xs:pt-4 xs:pb-8 flex-grow">
                {isFetchingRole || !client ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : session?.user && hasPermission ? (
                    <>
                        <div className="flex justify-between items-center">
                            <div className="flex items-start xs:items-center justify-between w-full gap-2">
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => router.push('/clients')}
                                    >
                                        <MoveLeft className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => handleOpenClientNavigation(client.id)}
                                    >
                                        <AppWindowMac className="h-4 w-4" />
                                    </Button>
                                </div>
                                <ClientNavigationModal
                                    open={clientNavigationOpen}
                                    onOpenChange={setClientNavigationOpen}
                                    clientId={selectedClientId}
                                    clientName={viewedClients.find(client => client.id === selectedClientId)?.name}
                                />
                                <div className="flex items-center gap-2 group">
                                    <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                        Entregas
                                    </h1>
                                    <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                        <Package size={24} color="#db5743" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h2 className="mt-4 text-lg font-semibold">{client.name}</h2>
                            {client.instagramUsername && (
                                <Link
                                    href={`https://www.instagram.com/${client.instagramUsername}`}
                                    target="_blank"
                                    className="text-sm text-blue-500 hover:underline underline-offset-4 cursor-pointer inline-flex items-center gap-1"
                                >
                                    <Image src="/instagram.svg" width={14} height={14} alt="Instagram" />
                                    {client.instagramUsername}
                                </Link>
                            )}
                            <div className="border-t pt-1 border-zinc-200 dark:border-zinc-800 mt-2">
                                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1 mb-2">
                                    <h3 className="flex items-center gap-1 text-sm font-semibold">
                                        <Package size={16} />
                                        Entregas
                                    </h3>
                                    <div className="flex items-center gap-1">
                                        <Badge variant="secondary">
                                            etapa 7
                                        </Badge>
                                        <Badge variant="secondary">
                                            entrega de conteúdo
                                        </Badge>
                                        <Dialog>
                                            <DialogTrigger asChild>
                                                <Badge variant="secondary" className="cursor-pointer">
                                                    <BadgeInfo className="h-4 w-4" />
                                                </Badge>
                                            </DialogTrigger>
                                            <DialogContent
                                                className="h-[85vh] p-0 overflow-y-auto"
                                                style={{ maxWidth: "550px !important", width: "550px !important" }}
                                            >
                                                <div className="p-6">
                                                    <DialogHeader className="mb-4">
                                                        <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                                            Saiba mais
                                                        </DialogTitle>
                                                        <DialogDescription className="font-medium text-gray-700 dark:text-gray-200">
                                                            Etapa 7 — Entrega de Conteúdo
                                                        </DialogDescription>
                                                    </DialogHeader>
                                                    <Separator className="my-2" />
                                                    <div className="space-y-6 text-sm text-zinc-700 dark:text-zinc-300">
                                                        <div>
                                                            <h4 className="font-semibold mb-2">Objetivo:</h4>
                                                            <p>Preparar todo o conteúdo do mês com antecedência, garantindo a qualidade e o alinhamento das publicações.</p>
                                                        </div>

                                                        <div className="space-y-2">
                                                            <h4 className="font-semibold">Etapas principais:</h4>
                                                            <ul className="list-disc pl-4 space-y-2">
                                                                <li>Desenvolver cada peça com base no escopo aprovado, incluindo design gráfico e edição de vídeos.</li>
                                                                <li>Criar legendas alinhadas com a estratégia de comunicação, utilizando gatilhos emocionais e CTAs.</li>
                                                                <li>Apresentar as peças e legendas para revisão, estabelecendo fluxo de feedback e correção.</li>
                                                                <li>Programar as postagens nos canais adequados com antecedência para cumprimento do calendário.</li>
                                                            </ul>
                                                        </div>
                                                    </div>
                                                </div>
                                            </DialogContent>
                                        </Dialog>
                                    </div>
                                </div>
                                <DeliveriesContent clientId={id} />
                            </div>
                        </div>
                    </>
                ) : (
                    <NotAllowed page="/dashboard" />
                )}
            </div>
            <Footer />
        </div>
    );
}
