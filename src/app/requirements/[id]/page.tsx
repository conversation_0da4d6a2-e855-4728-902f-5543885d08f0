"use client"

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import Image from 'next/image';
import { Avatar, AvatarImage, AvatarFallback } from '@/app/components/ui/avatar';
import Link from 'next/link';
import { format, startOfWeek } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CheckSquare, Filter, Info, ListTodo, MoveLeft, RefreshCw, Search, Square, User, AppWindowMac } from 'lucide-react';
import { toast } from "sonner";
import { Header } from '@/app/components/header';
import { Footer } from '@/app/components/footer';
import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/app/components/ui/dialog';
import { Input } from '@/app/components/ui/input';
import { Badge } from '@/app/components/ui/badge';
import { Tabs, TabsList, TabsTrigger } from '@/app/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { Checkbox } from '@/app/components/ui/checkbox';
import Loading from '@/app/components/ui/loading';
import { NotAllowed } from '@/app/components/not-allowed';
import { AboutRequirements } from '@/app/components/about-requirements';
import { AssignContent } from '@/app/components/assign-content';
import { AssignBulkContent } from '@/app/components/assign-bulk-content';
import { ActivityInfo } from '@/app/components/activity-info';
import { UpdateContentStatus } from '@/app/components/update-content-status';
import { EditContentDetails } from '@/app/components/edit-content-details';
import { ClientNavigationModal } from '@/app/components/client-navigation-modal';

interface Client {
  id: string;
  name: string;
  instagramUsername?: string;
}

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

interface Content {
  id: string;
  activityDate: string;
  contentType: string;
  channel?: string;
  details?: string;
  destination: string;
  caption?: string;
  status?: string;
  copywriting?: string;
  reference?: string;
  urlStructuringFeed?: string;
  assignedToId?: string | null;
  assignedTo?: User | null;
  createdAt: string;
  updatedAt: string;
}

export default function RequirementsPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const params = useParams();
  const clientId = params.id as string;

  const [client, setClient] = useState<Client | null>(null);
  const [contents, setContents] = useState<Content[]>([]);
  const [filteredContents, setFilteredContents] = useState<Content[]>([]);
  const [contentsByWeek, setContentsByWeek] = useState<{ [key: string]: Content[] }>({});
  const [localAssignees, setLocalAssignees] = useState<{ [key: string]: { id: string | null, user?: User | null } }>({});
  const [localContentDetails, setLocalContentDetails] = useState<{ [key: string]: { reference?: string, copywriting?: string, caption?: string } }>({});
  const [searchQuery, setSearchQuery] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [isAdmin, setIsAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [selectedContentIds, setSelectedContentIds] = useState<string[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);
  const [clientNavigationOpen, setClientNavigationOpen] = useState(false);
  const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
  const [viewedClients, setViewedClients] = useState<{ id: string; name: string }[]>([]);

  const monthNames = useMemo(() => [
    "Janeiro", "Fevereiro", "Março", "Abril",
    "Maio", "Junho", "Julho", "Agosto",
    "Setembro", "Outubro", "Novembro", "Dezembro",
  ], []);

  const [selectedMonth, setSelectedMonth] = useState<string | null>(null);

  const monthName = useCallback((month: number) => monthNames[month - 1], [monthNames]);

  const fetchData = useCallback(async () => {
    setIsLoading(true);
    try {
      const clientResponse = await fetch(`/api/clients/${clientId}`);
      if (!clientResponse.ok) {
        throw new Error('Failed to fetch client data');
      }
      const clientData = await clientResponse.json();
      setClient(clientData);

      const timestamp = new Date().getTime();
      const planningsResponse = await fetch(`/api/clients/${clientId}?include=monthlyPlannings.activities.contents&t=${timestamp}`);
      if (!planningsResponse.ok) {
        throw new Error('Failed to fetch planning data');
      }
      const planningsData = await planningsResponse.json();

      const allContents: Content[] = [];
      if (planningsData.monthlyPlannings && planningsData.monthlyPlannings.length > 0) {
        planningsData.monthlyPlannings.forEach((planning: { status?: string, activities?: { contents?: Content[] }[] }) => {
          if (planning.status === "aprovado" && planning.activities && planning.activities.length > 0) {
            planning.activities.forEach((activity: { contents?: Content[] }) => {
              if (activity.contents && activity.contents.length > 0) {
                const filteredContents = activity.contents.filter(content => {
                  const destinationLower = content.destination?.toLowerCase() || '';

                  const isFeedDestination = (
                    destinationLower === 'feed' ||
                    (destinationLower.includes('story') && destinationLower.includes('feed'))
                  );

                  if (isFeedDestination) {
                    return content.status === 'feed estruturado';
                  }

                  return true;
                });

                allContents.push(...filteredContents);
              }
            });
          }
        });
      }

      const sortedContents = [...allContents].sort((a, b) => {
        const dateA = new Date(a.activityDate);
        const dateB = new Date(b.activityDate);

        if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
          return 0;
        }

        const monthA = dateA.getMonth();
        const monthB = dateB.getMonth();

        if (monthA !== monthB) {
          return monthA - monthB;
        }

        return dateA.getDate() - dateB.getDate();
      });

      setContents(sortedContents);
      setFilteredContents(sortedContents);
    } catch (error) {
      console.error('Error fetching data:', error);
      toast.error('Erro ao carregar dados');
    } finally {
      setIsLoading(false);
    }
  }, [clientId]);

  const handleOpenClientNavigation = (clientId: string) => {
    setSelectedClientId(clientId);
    setClientNavigationOpen(true);
  };

  useEffect(() => {
    const recentClients = JSON.parse(localStorage.getItem('viewedClients') || '[]');
    setViewedClients(recentClients);
  }, []);

  useEffect(() => {
    if (status === "authenticated") {
      fetchData();

      const fetchUserRole = async () => {
        try {
          if (session?.user?.email) {
            const response = await fetch(`/api/users/${session.user.email}`);
            if (response.ok) {
              const user = await response.json();
              const adminRoles = ["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"];
              setIsAdmin(user?.role ? adminRoles.includes(user.role) : false);
            }
          }
        } catch (error) {
          console.error("Error fetching user role:", error);
        }
      };

      fetchUserRole();
    } else if (status === 'unauthenticated') {
      router.push('/');
    }
  }, [status, session, router, fetchData]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchData().finally(() => setIsRefreshing(false));
  };

  const handleBulkAssignSuccess = () => {
    handleRefresh();
    setSelectedContentIds([]);
    setSelectionMode(false);
  };

  const handleLocalAssigneeUpdate = (contentId: string, userId: string | null, user?: User | null) => {
    setLocalAssignees(prev => ({
      ...prev,
      [contentId]: { id: userId, user }
    }));
  };

  const handleLocalContentDetailsUpdate = (contentId: string, reference: string | null, copywriting: string | null, caption: string | null) => {
    setLocalContentDetails(prev => ({
      ...prev,
      [contentId]: {
        reference: reference || undefined,
        copywriting: copywriting || undefined,
        caption: caption || undefined
      }
    }));
  };

  const toggleSelectionMode = () => {
    setSelectionMode(prev => !prev);
    if (selectionMode) {
      setSelectedContentIds([]);
    }
  };

  const toggleContentSelection = (contentId: string) => {
    setSelectedContentIds(prev => {
      if (prev.includes(contentId)) {
        return prev.filter(id => id !== contentId);
      } else {
        return [...prev, contentId];
      }
    });
  };

  const selectAllContents = () => {
    if (selectedContentIds.length === filteredContents.length) {
      setSelectedContentIds([]);
    } else {
      setSelectedContentIds(filteredContents.map(content => content.id));
    }
  };

  const handleBulkAssigneeUpdate = (contentIds: string[], userId: string | null, user?: User | null) => {
    const updates: { [key: string]: { id: string | null, user?: User | null } } = {};

    contentIds.forEach(contentId => {
      updates[contentId] = { id: userId, user };
    });

    setLocalAssignees(prev => ({
      ...prev,
      ...updates
    }));
  };

  useEffect(() => {
    let result = [...contents];

    if (searchQuery.trim() !== '') {
      const query = searchQuery.toLowerCase();
      result = result.filter(content =>
        content.contentType?.toLowerCase().includes(query) ||
        content.details?.toLowerCase().includes(query) ||
        content.destination?.toLowerCase().includes(query) ||
        content.caption?.toLowerCase().includes(query)
      );
    }

    if (statusFilter !== 'all') {
      result = result.filter(content => content.status === statusFilter);
    }

    if (selectedMonth) {
      result = result.filter(content => {
        const contentDate = new Date(content.activityDate);
        const contentMonth = contentDate.getMonth() + 1;
        return monthName(contentMonth).toLowerCase() === selectedMonth;
      });
    }

    setFilteredContents(result);

    const groupedByWeek: { [key: string]: Content[] } = {};

    const orderedResult = [...result].sort((a, b) => {
      const dateA = new Date(a.activityDate);
      const dateB = new Date(b.activityDate);

      const monthA = dateA.getMonth();
      const monthB = dateB.getMonth();

      if (monthA !== monthB) {
        return monthA - monthB;
      }

      return dateA.getDate() - dateB.getDate();
    });

    orderedResult.forEach(content => {
      try {
        const contentDate = new Date(content.activityDate);
        const weekStart = startOfWeek(contentDate, { locale: ptBR });
        const weekKey = format(weekStart, 'yyyy-MM-dd');

        if (!groupedByWeek[weekKey]) {
          groupedByWeek[weekKey] = [];
        }

        groupedByWeek[weekKey].push(content);
      } catch (error) {
        console.error('Error grouping content by week:', error);
      }
    });

    setContentsByWeek(groupedByWeek);
  }, [contents, searchQuery, statusFilter, selectedMonth, monthName]);

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
    } catch {
      return 'Data inválida';
    }
  };

  const formatWeekRange = (weekKey: string) => {
    try {
      const weekContents = contentsByWeek[weekKey];

      if (!weekContents || weekContents.length === 0) {
        return 'Semana sem conteúdos';
      }

      const sortedContents = [...weekContents];

      const firstItem = sortedContents[0];
      const lastItem = sortedContents[sortedContents.length - 1];

      const firstDate = new Date(firstItem.activityDate);
      const lastDate = new Date(lastItem.activityDate);

      const sameMonth = firstDate.getMonth() === lastDate.getMonth();

      if (sameMonth) {
        return `${format(firstDate, 'dd', { locale: ptBR })} - ${format(lastDate, 'dd', { locale: ptBR })} de ${format(firstDate, 'MMMM', { locale: ptBR })}`;
      } else {
        return `${format(firstDate, 'dd MMM', { locale: ptBR })} - ${format(lastDate, 'dd MMM', { locale: ptBR })}`;
      }
    } catch (error) {
      console.error('Erro ao formatar intervalo de semana:', error);
      return 'Semana inválida';
    }
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <div className="p-4 xs:p-8 flex-grow">
        {isLoading ? (
          <div className="min-h-[70vh] flex justify-center items-center">
            <Loading />
          </div>
        ) : isAdmin ? (
          <>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-2">
                <Button variant="outline" size="icon" onClick={() => router.push("/clients")}>
                  <MoveLeft />
                </Button>
                <Button
                  variant="outline"
                  size="icon"
                  onClick={() => handleOpenClientNavigation(client?.id || '')}
                >
                  <AppWindowMac className="h-4 w-4" />
                </Button>
              </div>
              <ClientNavigationModal
                open={clientNavigationOpen}
                onOpenChange={setClientNavigationOpen}
                clientId={selectedClientId}
                clientName={viewedClients.find(client => client.id === selectedClientId)?.name}
              />
              <div className="flex items-center gap-3 group">
                <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                  Demandas
                </h1>
                <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                  <ListTodo size={24} color="#db5743" />
                </div>
              </div>
            </div>
            <h2 className="mt-4 text-lg font-semibold">{client?.name}</h2>
            {client?.instagramUsername && (
              <Link
                href={`https://www.instagram.com/${client.instagramUsername}`}
                target="_blank"
                className="text-sm text-blue-500 hover:underline underline-offset-4 cursor-pointer inline-flex items-center gap-1"
              >
                <Image src="/instagram.svg" width={14} height={14} alt="Instagram" />
                {client.instagramUsername}
              </Link>
            )}
            <div className="border-t pt-1 border-zinc-200 dark:border-zinc-800 mt-2 mb-4">
              <div className="flex flex-col xs:flex-row items-start xs:items-center gap-1 mb-2">
                <h3 className="flex items-center gap-1 text-sm font-semibold">
                  <ListTodo size={16} />
                  Aprovação de conteúdo
                </h3>
                <div className="flex items-center gap-1">
                  <Badge variant="secondary">
                    etapa 4
                  </Badge>
                  <Badge variant="secondary">
                    tarefas
                  </Badge>
                  <Dialog>
                    <DialogTrigger asChild>
                      <Button variant="link" size="icon">
                        <Info />
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-[550px] h-[85vh] p-0 overflow-y-auto">
                      <div className="p-6">
                        <DialogHeader>
                          <DialogTitle className="font-semibold text-gray-900 dark:text-gray-400">Saiba mais</DialogTitle>
                          <DialogDescription className="font-medium text-gray-700 dark:text-gray-200">
                            Etapa 4 — Demandas
                            <span className="block mt-1 text-sm font-normal">Gerenciamento de Requisitos de Conteúdo</span>
                          </DialogDescription>
                        </DialogHeader>
                        <AboutRequirements />
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              </div>
              <div className="flex flex-col sm:flex-row justify-between gap-4 sm:items-center">
                <div className="flex flex-wrap items-center gap-2">
                  <Select onValueChange={(value) => setSelectedMonth(value === "all" ? null : value)}>
                    <SelectTrigger className="w-full sm:w-[180px]">
                      <SelectValue placeholder="Filtrar por mês" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os meses</SelectItem>
                      {monthNames.map((name, index) => (
                        <SelectItem key={index} value={name.toLowerCase()}>
                          {name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>

                  <Button
                    variant={selectionMode ? "default" : "outline"}
                    className="gap-1 whitespace-nowrap flex-grow sm:flex-grow-0"
                    onClick={toggleSelectionMode}
                  >
                    {selectionMode ? <CheckSquare className="h-4 w-4" /> : <Square className="h-4 w-4" />}
                    {selectionMode ? "Cancelar seleção" : "Selecionar"}
                  </Button>
                </div>

                <div className="flex flex-wrap items-center gap-2">
                  {selectionMode && (
                    <>
                      <Button
                        variant="outline"
                        className="gap-1 whitespace-nowrap"
                        onClick={selectAllContents}
                      >
                        {selectedContentIds.length === filteredContents.length ? "Desmarcar todos" : "Selecionar todos"}
                      </Button>
                      <AssignBulkContent
                        contentIds={selectedContentIds}
                        onLocalUpdate={handleBulkAssigneeUpdate}
                        onSuccess={handleBulkAssignSuccess}
                      />
                      {selectedContentIds.length === 0 && (
                        <span className="hidden sm:inline text-xs text-muted-foreground">
                          Selecione demandas para atribuir
                        </span>
                      )}
                    </>
                  )}
                  <Button
                    variant="outline"
                    className="gap-1 whitespace-nowrap ml-auto sm:ml-0"
                    onClick={handleRefresh}
                    disabled={isRefreshing}
                  >
                    <RefreshCw className={`h-4 w-4 ${isRefreshing ? 'animate-spin' : ''}`} />
                    Atualizar
                  </Button>
                </div>
                {selectionMode && selectedContentIds.length === 0 && (
                  <span className="sm:hidden text-xs text-muted-foreground text-center">
                    Selecione demandas para atribuir
                  </span>
                )}
              </div>
            </div>

            <div className="flex flex-col xs:flex-row gap-2 justify-between mb-4">
              <div className="relative w-full lg:w-[550px]">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400" size={18} />
                <Input
                  type="text"
                  placeholder="Pesquisar por tipo, detalhes ou destino"
                  className="pl-10 w-full placeholder:text-sm"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                />
              </div>

              <div className="flex gap-2 items-center w-full">
                <Filter size={18} className="text-gray-500 dark:text-gray-400 shrink-0" />

                <div className="hidden lg:block w-full overflow-x-auto">
                  <Tabs defaultValue="all" className="w-full" onValueChange={setStatusFilter}>
                    <TabsList className="flex flex-nowrap w-full">
                      <TabsTrigger value="all" className="flex-1 min-w-[100px]">Todos</TabsTrigger>
                      <TabsTrigger value="pendente" className="flex-1 min-w-[100px]">Pendentes</TabsTrigger>
                      <TabsTrigger value="em andamento" className="flex-1 min-w-[100px]">Andamento</TabsTrigger>
                      <TabsTrigger value="pend. captação" className="flex-1 min-w-[100px]">Captação</TabsTrigger>
                      <TabsTrigger value="captado" className="flex-1 min-w-[100px]">Captados</TabsTrigger>
                      <TabsTrigger value="feed estruturado" className="flex-1 min-w-[100px]">Estruturado</TabsTrigger>
                      <TabsTrigger value="concluído" className="flex-1 min-w-[100px]">Concluídos</TabsTrigger>
                    </TabsList>
                  </Tabs>
                </div>

                <div className="lg:hidden w-full lg:w-[250px]">
                  <Select defaultValue="all" onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-full">
                      <SelectValue placeholder="Filtrar por status" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Todos os status</SelectItem>
                      <SelectItem value="pendente">Pendentes</SelectItem>
                      <SelectItem value="em andamento">Em andamento</SelectItem>
                      <SelectItem value="pend. captação">Pend. captação</SelectItem>
                      <SelectItem value="captado">Captados</SelectItem>
                      <SelectItem value="feed estruturado">Feed estruturado</SelectItem>
                      <SelectItem value="concluído">Concluídos</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>

            {filteredContents.length === 0 ? (
              <div className="text-center py-10">
                <ListTodo className="mx-auto h-12 w-12 text-muted-foreground" />
                <h3 className="mt-2 text-lg font-semibold">Nenhuma demanda encontrada</h3>
                <p className="text-muted-foreground">
                  {selectedMonth
                    ? `Nenhuma demanda encontrada para o mês de ${selectedMonth.charAt(0).toUpperCase() + selectedMonth.slice(1)}`
                    : searchQuery || statusFilter !== 'all'
                      ? 'Tente ajustar os filtros de pesquisa'
                      : 'Este cliente ainda não possui demandas'}
                </p>
              </div>
            ) : (
              <div className="space-y-8">
                {Object.keys(contentsByWeek)
                  .sort((a, b) => {
                    const dateA = new Date(a);
                    const dateB = new Date(b);

                    const monthA = dateA.getMonth();
                    const monthB = dateB.getMonth();

                    if (monthA !== monthB) {
                      return monthA - monthB;
                    }

                    return dateA.getDate() - dateB.getDate();
                  })
                  .map((weekKey) => (
                    <div key={weekKey} className="space-y-4">
                      <div className="sticky top-0 z-10 bg-background py-2 border-b border-zinc-200 dark:border-zinc-800 flex items-center gap-2">
                        <div className="bg-primary/10 dark:bg-primary/20 p-1 rounded-md">
                          <ListTodo size={16} className="text-primary" />
                        </div>
                        <h3 className="text-sm font-medium">
                          Semana: <span className="text-primary">{formatWeekRange(weekKey)}</span>
                        </h3>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {contentsByWeek[weekKey].map((content) => (
                          <Card
                            key={content.id}
                            className={`dark:bg-zinc-800/50 ${selectedContentIds.includes(content.id) ? 'ring-2 ring-primary' : ''} ${selectionMode ? 'cursor-pointer' : ''}`}
                            onClick={() => selectionMode && toggleContentSelection(content.id)}
                          >
                            <CardHeader className="pb-2">
                              <div className="flex justify-between items-start">
                                <div className="flex items-center gap-2">
                                  {selectionMode && (
                                    <Checkbox
                                      checked={selectedContentIds.includes(content.id)}
                                      onCheckedChange={() => toggleContentSelection(content.id)}
                                      className="data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground"
                                    />
                                  )}
                                  <CardTitle className="text-base">{content.contentType}</CardTitle>
                                </div>
                                <UpdateContentStatus
                                  contentId={content.id}
                                  currentStatus={content.status || 'pendente'}
                                />
                              </div>
                              <CardDescription>
                                {content.destination} • {formatDate(content.activityDate)}
                              </CardDescription>
                            </CardHeader>
                            <CardContent className="pb-2">
                              {content.details && (
                                <p className="text-sm mb-2">{content.details}</p>
                              )}
                            </CardContent>
                            <CardFooter className="pt-0 text-xs text-muted-foreground flex justify-between gap-2 items-center">
                              <span>Atualizado em: {formatDate(content.updatedAt)}</span>
                              <div className="flex items-center gap-2">
                                {(() => {
                                  const localAssignee = localAssignees[content.id];
                                  const hasLocalUser = localAssignee?.user && localAssignee.id !== null;

                                  const userToShow = hasLocalUser ? localAssignee.user : content.assignedTo;

                                  const shouldShowUser =
                                    (userToShow && localAssignee === undefined) ||
                                    (userToShow && localAssignee?.id !== null);

                                  if (shouldShowUser && userToShow) {
                                    return (
                                      <div className="flex items-center gap-1">
                                        <Avatar className="h-6 w-6 border-2 border-zinc-200">
                                          <AvatarImage src={userToShow.image || undefined} alt={userToShow.name || userToShow.email} />
                                          <AvatarFallback className="text-xs bg-blue-50 text-blue-800 dark:bg-blue-950 dark:text-blue-300">
                                            {userToShow.name?.[0] || userToShow.email[0]}
                                          </AvatarFallback>
                                        </Avatar>
                                        <span className="text-xs text-muted-foreground truncate max-w-[80px]">
                                          {userToShow?.name?.split(' ')[0] || userToShow.email.split('@')[0]}
                                        </span>
                                      </div>
                                    );
                                  }
                                  return null;
                                })()}
                                <div className='flex gap-1 items-center'>
                                  <ActivityInfo
                                    activity={content}
                                    localReference={localContentDetails[content.id]?.reference}
                                    localCopywriting={localContentDetails[content.id]?.copywriting}
                                    localCaption={localContentDetails[content.id]?.caption}
                                  />
                                  <AssignContent
                                    contentId={content.id}
                                    currentAssigneeId={content.assignedToId}
                                    onLocalUpdate={(userId, user) => handleLocalAssigneeUpdate(content.id, userId, user)}
                                  />
                                  <EditContentDetails
                                    contentId={content.id}
                                    currentReference={localContentDetails[content.id]?.reference || content.reference}
                                    currentCopy={localContentDetails[content.id]?.copywriting || content.copywriting}
                                    currentCaption={localContentDetails[content.id]?.caption || content.caption}
                                    onLocalUpdate={(reference, copywriting, caption) => handleLocalContentDetailsUpdate(content.id, reference, copywriting, caption)}
                                  />
                                </div>
                              </div>
                            </CardFooter>
                          </Card>
                        ))}
                      </div>
                    </div>
                  ))}
              </div>
            )}
          </>
        ) : (
          <NotAllowed page='/' />
        )}
      </div>
      <Footer />
    </div>
  );
}
