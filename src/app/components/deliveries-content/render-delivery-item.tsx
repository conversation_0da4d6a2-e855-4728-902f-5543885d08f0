import Image from 'next/image';

interface DeliveryContent {
    id: string;
    contentType?: string;
    details?: string;
    urlStructuringFeed?: string | string[];
    urlTypes?: string[];
    urlMediaTypes?: string[];
    urlThumbnails?: string[];
    activityDate: string | Date;
    week: number;
    month?: number;
    year?: number;
    status?: string;
    planningId?: string;
    caption?: string;
    currentUrlIndex?: number;
}

const isVideoContent = (contentType?: string): boolean => {
    if (!contentType) return false;
    const videoTypes = ['vídeo', 'video', 'reel', 'reels', 'animação'];
    return videoTypes.some(type => contentType.toLowerCase().includes(type.toLowerCase()));
};

const isVideoUrl = (urlIndex: number, urlMediaTypes?: string[]): boolean => {
    if (!urlMediaTypes || !urlMediaTypes[urlIndex]) return false;
    return urlMediaTypes[urlIndex] === 'video';
};

const PlayIcon = ({ isPdfMode = false }: { isPdfMode?: boolean }) => {
    const size = isPdfMode ? 60 : 70;

    return (
        <div
            style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                zIndex: 10,
                pointerEvents: 'none',
                width: `${size}px`,
                height: `${size}px`,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
            }}
        >
            {isPdfMode ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                    src="/play.png"
                    alt="Play"
                    width={50}
                    height={50}
                    style={{
                        filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5))',
                        display: 'block',
                    }}
                />
            ) : (
                <Image
                    src="/play.png"
                    alt="Play"
                    width={50}
                    height={50}
                    style={{
                        filter: 'drop-shadow(0 2px 4px rgba(0, 0, 0, 0.5))',
                    }}
                />
            )}
        </div>
    );
};

export const extractGoogleDriveId = (url: string): string | null => {
    if (!url) return null;

    // Limpar a URL de espaços e caracteres especiais
    const cleanUrl = url.trim();

    // Lista de padrões para extrair ID do Google Drive
    const patterns = [
        // Padrão padrão: https://drive.google.com/file/d/ID/view
        /\/file\/d\/([a-zA-Z0-9_-]+)/,
        // Padrão para URLs do tipo: /d/ID
        /\/d\/([a-zA-Z0-9_-]+)/,
        // Padrão para URLs do tipo: ?id=ID
        /[?&]id=([a-zA-Z0-9_-]+)/,
        // Padrão para URLs do tipo: https://drive.google.com/open?id=ID
        /open\?id=([a-zA-Z0-9_-]+)/,
        // Padrão para URLs de thumbnail direto
        /thumbnail\?id=([a-zA-Z0-9_-]+)/,
        // Padrão para export view
        /uc\?.*id=([a-zA-Z0-9_-]+)/,
        // Padrão para compartilhamento direto
        /\/([a-zA-Z0-9_-]{25,})/
    ];

    for (let i = 0; i < patterns.length; i++) {
        const match = cleanUrl.match(patterns[i]);
        if (match && match[1] && match[1].length >= 25) {
            return match[1];
        }
    }

    return null;
};

export const renderDeliveryItem = (
    content: DeliveryContent,
    isPdfMode = false,
    compactView = false
) => {
    const getAllUrls = (urls: string | string[] | undefined): string[] => {
        if (!urls) return [];
        return Array.isArray(urls) ? urls.filter(url => url && typeof url === 'string' && url.trim() !== '') : [urls];
    };

    const allUrls = getAllUrls(content.urlStructuringFeed);

    // Se currentUrlIndex está definido, renderizar apenas essa URL específica do carrossel
    if (content.currentUrlIndex !== undefined) {
        const currentUrl = allUrls[content.currentUrlIndex];
        const currentDriveId = currentUrl ? extractGoogleDriveId(currentUrl) : null;

        // Verificar se é vídeo usando o índice específico
        const isVideoFromMediaType = isVideoUrl(content.currentUrlIndex, content.urlMediaTypes);
        const isVideoFromContentType = isVideoContent(content.contentType);
        const isVideo = isVideoFromMediaType || isVideoFromContentType;

        if (!currentUrl || !currentDriveId) {
            return (
                <div className={`text-amber-800 border border-dashed p-1 rounded text-xs text-center ${compactView ? 'h-[140px]' : 'h-[180px]'} flex items-center justify-center`}>
                    * URL pendente ou não é necessária
                </div>
            );
        }

        const thumbnailUrl = isVideo && content.urlThumbnails?.[content.currentUrlIndex];
        const thumbnailDriveId = thumbnailUrl ? extractGoogleDriveId(thumbnailUrl) : null;
        const displayDriveId = isVideo && thumbnailDriveId ? thumbnailDriveId : currentDriveId;

        return (
            <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                {isPdfMode && isVideo && thumbnailUrl ? (
                    <a href={currentUrl} target="_blank" rel="noopener noreferrer" style={{ display: 'block', width: '100%', height: '100%' }}>
                        <Image
                            src={`/api/drive-proxy?id=${displayDriveId}&quality=high&size=large`}
                            alt="Capa do vídeo"
                            width={1000}
                            height={1500}
                            className="object-contain max-w-full max-h-full"
                            priority
                            loading="eager"
                            unoptimized={true}
                            data-pdf-image="true"
                            style={{
                                width: 'auto',
                                height: 'auto',
                                maxWidth: '100%',
                                maxHeight: '100%',
                                objectPosition: 'center'
                            }}
                        />
                        <PlayIcon isPdfMode={true} />
                    </a>
                ) : (
                    <Image
                        src={`/api/drive-proxy?id=${displayDriveId}&quality=high&size=large`}
                        alt="Conteúdo"
                        width={1000}
                        height={1500}
                        className="object-contain max-w-full max-h-full"
                        priority
                        loading="eager"
                        unoptimized={true}
                        data-pdf-image="true"
                        style={{
                            width: 'auto',
                            height: 'auto',
                            maxWidth: '100%',
                            maxHeight: '100%',
                            objectPosition: 'center'
                        }}
                    />
                )}
                {isPdfMode && isVideo && !thumbnailUrl && <PlayIcon isPdfMode={true} />}
            </div>
        );
    }

    const firstUrl = allUrls[0];
    const driveId = firstUrl ? extractGoogleDriveId(firstUrl) : null;

    if (isPdfMode) {
        if (allUrls.length > 1) {
            const limitedUrls = allUrls.slice(0, 12);

            return (
                <div className="pdf-multiple-images" style={{
                    display: 'flex',
                    flexWrap: 'wrap',
                    gap: '6px',
                    justifyContent: 'flex-start',
                    maxWidth: '100%'
                }}>
                    {limitedUrls.map((url, index) => {
                        const urlDriveId = extractGoogleDriveId(url);
                        // Verificar se é vídeo usando urlMediaTypes OU contentType como fallback
                        const isVideoFromMediaType = isVideoUrl(index, content.urlMediaTypes);
                        const isVideoFromContentType = isVideoContent(content.contentType);
                        const isVideo = isVideoFromMediaType || isVideoFromContentType;

                        if (!urlDriveId) {
                            return (
                                <div key={index} style={{
                                    width: '120px',
                                    aspectRatio: '3 / 4',
                                    display: 'flex',
                                    alignItems: 'center',
                                    justifyContent: 'center',
                                    fontSize: '10px',
                                    flexShrink: 0,
                                    borderRadius: '8px',
                                    backgroundColor: '#f3f4f6'
                                }}>
                                    Erro: URL inválida para o criativo {index + 1}
                                </div>
                            );
                        }

                        // Para vídeos, usar thumbnail se disponível
                        const thumbnailUrl = isVideo && content.urlThumbnails?.[index];
                        const thumbnailDriveId = thumbnailUrl ? extractGoogleDriveId(thumbnailUrl) : null;
                        const displayDriveId = isVideo && thumbnailDriveId ? thumbnailDriveId : urlDriveId;

                        // Usar a URL original do vídeo
                        const videoUrl = allUrls[index];

                        const imageElement = (
                            <div key={index} style={{
                                width: '200px',
                                aspectRatio: '3 / 4',
                                overflow: 'hidden',
                                position: 'relative',
                                flexShrink: 0,
                            }}>
                                <Image
                                    src={`/api/drive-proxy?id=${displayDriveId}&quality=high&size=large`}
                                    alt={`Visualização da entrega - Criativo ${index + 1}`}
                                    layout="fill"
                                    objectFit="contain"
                                    priority
                                    loading="eager"
                                    unoptimized={true}
                                    data-pdf-image="true"
                                />
                                {isPdfMode && isVideo && <PlayIcon isPdfMode={true} />}
                            </div>
                        );

                        return isPdfMode && isVideo && thumbnailUrl ? (
                            <a key={index} href={videoUrl} target="_blank" rel="noopener noreferrer">
                                {imageElement}
                            </a>
                        ) : imageElement;

                    })}
                </div>
            );
        }



        return (
            <>
                {content.urlStructuringFeed && driveId ? (
                    <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                        {(() => {
                            // Para vídeos, usar thumbnail se disponível
                            const isVideoFromMediaType = isVideoUrl(0, content.urlMediaTypes);
                            const isVideoFromContentType = isVideoContent(content.contentType);
                            const isVideo = isVideoFromMediaType || isVideoFromContentType;
                            const thumbnailUrl = isVideo && content.urlThumbnails?.[0];
                            const thumbnailDriveId = thumbnailUrl ? extractGoogleDriveId(thumbnailUrl) : null;
                            const displayDriveId = isVideo && thumbnailDriveId ? thumbnailDriveId : driveId;


                            // Usar a URL original do vídeo
                            const videoUrl = firstUrl;

                            const imageElement = (
                                <Image
                                    src={`/api/drive-proxy?id=${displayDriveId}&quality=high&size=large`}
                                    alt={isVideo && thumbnailUrl ? "Capa do vídeo" : "Visualização da entrega"}
                                    width={1000}
                                    height={1500}
                                    className="object-contain max-w-full max-h-full"
                                    priority
                                    loading="eager"
                                    unoptimized={true}
                                    data-pdf-image="true"
                                    style={{
                                        width: 'auto',
                                        height: 'auto',
                                        maxWidth: '100%',
                                        maxHeight: '100%',
                                        objectPosition: 'center'
                                    }}
                                    onError={(e) => {
                                        console.error('Erro ao carregar imagem PDF:', {
                                            driveId,
                                            originalUrl: content.urlStructuringFeed,
                                            proxyUrl: `/api/drive-proxy?id=${displayDriveId}&quality=high&size=large`
                                        });
                                        const imgElement = e.currentTarget as HTMLImageElement;

                                        if (!imgElement.src.includes('placeholder.png')) {
                                            imgElement.src = '/images/placeholder.png';
                                            return;
                                        }

                                        imgElement.onerror = null;
                                        imgElement.style.display = 'none';

                                        const errorDiv = document.createElement('div');
                                        errorDiv.className = 'flex items-center justify-center h-full bg-red-50 text-red-500 text-sm p-4';
                                        errorDiv.textContent = 'Erro ao carregar imagem';
                                        if (imgElement.parentNode) {
                                            imgElement.parentNode.appendChild(errorDiv);
                                        }
                                    }}
                                />
                            );

                            return isPdfMode && isVideo && thumbnailUrl ? (
                                <a href={videoUrl} target="_blank" rel="noopener noreferrer" style={{ display: 'block', width: '100%', height: '100%' }}>
                                    {imageElement}
                                    <PlayIcon isPdfMode={true} />
                                </a>
                            ) : (
                                <>
                                    {imageElement}
                                    {isPdfMode && isVideo && <PlayIcon isPdfMode={true} />}
                                </>
                            );
                        })()}
                    </div>
                ) : (
                    <div className="h-[700px] flex items-center justify-center bg-gray-50 text-gray-500 text-sm p-4">
                        Nenhuma URL cadastrada
                    </div>
                )}
            </>
        );
    }

    const isVideoFromMediaType = isVideoUrl(0, content.urlMediaTypes);
    const isVideoFromContentType = isVideoContent(content.contentType);
    const isVideo = isVideoFromMediaType || isVideoFromContentType;
    const thumbnailUrl = isVideo && content.urlThumbnails?.[0];
    const thumbnailDriveId = thumbnailUrl ? extractGoogleDriveId(thumbnailUrl) : null;
    const displayDriveId = isVideo && thumbnailDriveId ? thumbnailDriveId : driveId;

    return (
        <>
            {content.urlStructuringFeed && driveId ? (
                <div className={`flex flex-col ${compactView ? 'h-[350px]' : 'h-[400px]'}`}>
                    <div className={`flex-1 bg-gray-100 rounded relative ${compactView ? 'h-[350px]' : 'h-[400px]'} flex items-center justify-center overflow-hidden`}>
                        <Image
                            src={`/api/drive-proxy?id=${displayDriveId}&quality=high&size=large`}
                            alt={isVideo && thumbnailUrl ? "Capa do vídeo" : "Visualização da entrega"}
                            width={500}
                            height={625}
                            className="max-w-full max-h-full object-contain"
                            loading="lazy"
                            unoptimized={true}
                            style={{
                                width: 'auto',
                                height: 'auto',
                                maxWidth: '100%',
                                maxHeight: '100%',
                                objectPosition: 'center'
                            }}
                            onError={(e) => {
                                console.error('Erro ao carregar imagem normal:', {
                                    driveId,
                                    displayDriveId,
                                    originalUrl: content.urlStructuringFeed,
                                    proxyUrl: `/api/drive-proxy?id=${displayDriveId}&quality=high&size=large`
                                });
                                const imgElement = e.currentTarget as HTMLImageElement;

                                if (!imgElement.src.includes('placeholder.png')) {
                                    imgElement.src = '/images/placeholder.png';
                                    return;
                                }

                                if (!imgElement.src.includes('placeholder.png')) {
                                    imgElement.src = '/images/placeholder.png';
                                    return;
                                }

                                imgElement.onerror = null;
                                imgElement.style.display = 'none';

                                const errorDiv = document.createElement('div');
                                errorDiv.className = 'flex items-center justify-center h-full bg-red-50 text-red-500 text-sm p-4 rounded';
                                errorDiv.textContent = 'Erro ao carregar imagem! Provavelmente a URL não é pública, verifique no Google Drive.';
                                if (imgElement.parentNode) {
                                    imgElement.parentNode.appendChild(errorDiv);
                                }
                            }}
                        />
                    </div>
                </div>
            ) : (
                <div className="h-[400px] flex items-center justify-center bg-gray-100 dark:bg-zinc-800 text-gray-500 dark:text-zinc-400 text-sm p-4 rounded">
                    Nenhuma URL cadastrada
                </div>
            )}
        </>
    );
};
