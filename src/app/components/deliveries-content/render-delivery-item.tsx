import Image from 'next/image';

interface DeliveryContent {
    id: string;
    contentType?: string;
    details?: string;
    urlStructuringFeed?: string | string[];
    urlTypes?: string[];
    urlMediaTypes?: string[];
    urlThumbnails?: string[];
    activityDate: string | Date;
    week: number;
    month?: number;
    year?: number;
    status?: string;
    planningId?: string;
    caption?: string;
    currentUrlIndex?: number;
}

export const extractGoogleDriveId = (url: string): string | null => {
    if (!url) return null;

    // Limpar a URL de espaços e caracteres especiais
    const cleanUrl = url.trim();

    // Lista de padrões para extrair ID do Google Drive
    const patterns = [
        // Padrão padrão: https://drive.google.com/file/d/ID/view
        /\/file\/d\/([a-zA-Z0-9_-]+)/,
        // Padrão para URLs do tipo: /d/ID
        /\/d\/([a-zA-Z0-9_-]+)/,
        // Padrão para URLs do tipo: ?id=ID
        /[?&]id=([a-zA-Z0-9_-]+)/,
        // Padrão para URLs do tipo: https://drive.google.com/open?id=ID
        /open\?id=([a-zA-Z0-9_-]+)/,
        // Padrão para URLs de thumbnail direto
        /thumbnail\?id=([a-zA-Z0-9_-]+)/,
        // Padrão para export view
        /uc\?.*id=([a-zA-Z0-9_-]+)/,
        // Padrão para compartilhamento direto
        /\/([a-zA-Z0-9_-]{25,})/
    ];

    for (let i = 0; i < patterns.length; i++) {
        const match = cleanUrl.match(patterns[i]);
        if (match && match[1] && match[1].length >= 25) {
            return match[1];
        }
    }

    return null;
};

export const renderDeliveryItem = (
    content: DeliveryContent,
    compactView = false
) => {
    const getAllUrls = (urls: string | string[] | undefined): string[] => {
        if (!urls) return [];
        return Array.isArray(urls) ? urls.filter(url => url && typeof url === 'string' && url.trim() !== '') : [urls];
    };

    const allUrls = getAllUrls(content.urlStructuringFeed);

    // Se currentUrlIndex está definido, renderizar apenas essa URL específica do carrossel
    if (content.currentUrlIndex !== undefined) {
        const currentUrl = allUrls[content.currentUrlIndex];
        const currentDriveId = currentUrl ? extractGoogleDriveId(currentUrl) : null;

        if (!currentUrl || !currentDriveId) {
            return (
                <div className={`text-amber-800 border border-dashed p-1 rounded text-xs text-center ${compactView ? 'h-[140px]' : 'h-[180px]'} flex items-center justify-center`}>
                    * URL pendente ou não é necessária
                </div>
            );
        }

        return (
            <div style={{ position: 'relative', width: '100%', height: '100%' }}>
                {currentUrl && (
                    <a href={currentUrl} target="_blank" rel="noopener noreferrer" style={{ display: 'block', width: '100%', height: '100%' }}>
                        <Image
                            src={`/api/drive-proxy?id=${currentDriveId}&quality=high&size=large`}
                            alt="Visualização da entrega"
                            width={1000}
                            height={1500}
                            className="object-contain max-w-full max-h-full"
                            loading="eager"
                            unoptimized={true}
                            style={{
                                width: 'auto',
                                height: 'auto',
                                maxWidth: '100%',
                                maxHeight: '100%',
                                objectPosition: 'center'
                            }}
                        />
                    </a>
                )}
            </div>
        );
    }

    const firstUrl = allUrls[0];
    const driveId = firstUrl ? extractGoogleDriveId(firstUrl) : null;

    return (
        <>
            {content.urlStructuringFeed && driveId ? (
                <div className={`flex flex-col ${compactView ? 'h-[350px]' : 'h-[400px]'}`}>
                    <div className={`flex-1 bg-gray-100 rounded relative ${compactView ? 'h-[350px]' : 'h-[400px]'} flex items-center justify-center overflow-hidden`}>
                        <Image
                            src={`/api/drive-proxy?id=${driveId}&quality=high&size=large`}
                            alt="Visualização da entrega"
                            width={500}
                            height={625}
                            className="max-w-full max-h-full object-contain"
                            loading="lazy"
                            unoptimized={true}
                            style={{
                                width: 'auto',
                                height: 'auto',
                                maxWidth: '100%',
                                maxHeight: '100%',
                                objectPosition: 'center'
                            }}
                            onError={(e) => {
                                console.error('Erro ao carregar imagem normal:', {
                                    driveId,
                                    originalUrl: content.urlStructuringFeed,
                                    proxyUrl: `/api/drive-proxy?id=${driveId}&quality=high&size=large`
                                });
                                const imgElement = e.currentTarget as HTMLImageElement;

                                if (!imgElement.src.includes('placeholder.png')) {
                                    imgElement.src = '/images/placeholder.png';
                                    return;
                                }

                                if (!imgElement.src.includes('placeholder.png')) {
                                    imgElement.src = '/images/placeholder.png';
                                    return;
                                }

                                imgElement.onerror = null;
                                imgElement.style.display = 'none';

                                const errorDiv = document.createElement('div');
                                errorDiv.className = 'flex items-center justify-center h-full bg-red-50 text-red-500 text-sm p-4 rounded';
                                errorDiv.textContent = 'Erro ao carregar imagem! Provavelmente a URL não é pública, verifique no Google Drive.';
                                if (imgElement.parentNode) {
                                    imgElement.parentNode.appendChild(errorDiv);
                                }
                            }}
                        />
                    </div>
                </div>
            ) : (
                <div className="h-[400px] flex items-center justify-center bg-gray-100 dark:bg-zinc-800 text-gray-500 dark:text-zinc-400 text-sm p-4 rounded">
                    Nenhuma URL cadastrada
                </div>
            )}
        </>
    );
};
