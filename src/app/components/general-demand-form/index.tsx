"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/app/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog";
import { Label } from "@/app/components/ui/label";
import { Input } from "@/app/components/ui/input";
import { Textarea } from "@/app/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Avatar, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar";
import { Ellipsis, Plus } from "lucide-react";
import { toast } from "sonner";
import { Card, CardContent, CardHeader } from "../ui/card";

interface Client {
  id: string;
  name: string;
}

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
  role?: string;
}

interface GeneralDemandFormProps {
  clients: Client[];
  onSuccess: () => void;
  initialData?: {
    id: string;
    title: string;
    description?: string | null;
    dueDate?: Date | null;
    status?: string;
    priority?: string;
    clientId: string;
    isLooseClient?: boolean;
    looseClientName?: string;
    assignedToId?: string | null;
    urlStructuringFeed?: string[] | null;
  };
  buttonLabel?: string;
  buttonVariant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  triggerComponent?: React.ReactNode;
}

type FormDataType = {
  title: string;
  description: string;
  dueDate: Date | null;
  status: string;
  priority: string;
  clientId: string;
  useLooseClient: boolean;
  looseClientName: string;
  assignedToId: string | null;
  urlStructuringFeed: string[];
};

export function GeneralDemandForm({
  clients,
  onSuccess,
  initialData,
  buttonLabel = "Nova demanda pontual",
  buttonVariant = "default",
  triggerComponent,
}: GeneralDemandFormProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [users, setUsers] = useState<User[]>([]);
  const [formData, setFormData] = useState<FormDataType>({
    title: initialData?.title || "",
    description: initialData?.description || "",
    dueDate: initialData?.dueDate || new Date(),
    status: initialData?.status || "pendente",
    priority: initialData?.priority || "normal",
    clientId: initialData?.clientId || "",
    useLooseClient: initialData?.isLooseClient || false,
    looseClientName: initialData?.looseClientName || "",
    assignedToId: initialData?.assignedToId || null,
    urlStructuringFeed: initialData?.urlStructuringFeed || [],
  });

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen]);

  useEffect(() => {
    if (initialData) {
      setFormData({
        title: initialData.title,
        description: initialData.description || "",
        dueDate: initialData.dueDate || new Date(),
        status: initialData.status || "pendente",
        priority: initialData.priority || "normal",
        clientId: initialData.clientId,
        useLooseClient: initialData.isLooseClient || false,
        looseClientName: initialData.looseClientName || "",
        assignedToId: initialData.assignedToId || null,
        urlStructuringFeed: initialData.urlStructuringFeed || [],
      });
    }
  }, [initialData]);

  const fetchUsers = async () => {
    try {
      const response = await fetch("/api/users");
      if (response.ok) {
        const data = await response.json();
        const filteredUsers = data.filter(
          (user: User) => user.role !== "DEVELOPER"
        );
        setUsers(filteredUsers);
      } else {
        toast.error("Erro ao carregar usuários");
      }
    } catch (error) {
      console.error("Erro ao buscar usuários:", error);
      toast.error("Erro ao carregar usuários");
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSelectChange = (name: string, value: string | null) => {
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const formatDateForInput = (date: Date | null): string => {
    if (!date) return "";

    const dateCopy = new Date(date);

    const year = dateCopy.getFullYear();
    const month = String(dateCopy.getMonth() + 1).padStart(2, '0');
    const day = String(dateCopy.getDate()).padStart(2, '0');

    return `${year}-${month}-${day}`;
  };

  const handleDateChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    if (!value) {
      setFormData(prev => ({
        ...prev,
        dueDate: null
      }));
      return;
    }

    const [year, month, day] = value.split('-').map(Number);
    const date = new Date(year, month - 1, day, 12, 0, 0);

    setFormData(prev => ({
      ...prev,
      dueDate: date
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);

    if (!formData.title.trim()) {
      toast.error("O título é obrigatório");
      setIsLoading(false);
      return;
    }

    if (!formData.useLooseClient && !formData.clientId) {
      toast.error("Selecione um cliente");
      setIsLoading(false);
      return;
    }

    if (formData.useLooseClient && !formData.looseClientName.trim()) {
      toast.error("Informe o nome do cliente não fixo");
      setIsLoading(false);
      return;
    }

    try {
      const url = initialData
        ? `/api/general-demands/${initialData.id}`
        : "/api/general-demands";
      const method = initialData ? "PATCH" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success(
          initialData
            ? "Demanda pontual atualizada com sucesso"
            : "Demanda pontual criada com sucesso"
        );
        setIsOpen(false);
        setFormData({
          title: "",
          description: "",
          dueDate: new Date(),
          status: "pendente",
          priority: "normal",
          clientId: "",
          useLooseClient: false,
          looseClientName: "",
          assignedToId: null,
          urlStructuringFeed: [],
        });
        onSuccess();
      } else {
        const error = await response.json();
        toast.error(error.error || "Erro ao processar a solicitação");
      }
    } catch (error) {
      console.error("Erro ao salvar demanda pontual:", error);
      toast.error("Erro ao processar a solicitação");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        {triggerComponent ? (
          triggerComponent
        ) : (
          <Button variant={buttonVariant} className="gap-1">
            <Plus className="h-4 w-4" />
            {buttonLabel}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            {initialData ? "Editar demanda pontual" : "Nova demanda pontual"}
          </DialogTitle>
          <DialogDescription>
            {initialData
              ? "Atualize os detalhes da demanda pontual"
              : "Preencha os detalhes para criar uma nova demanda pontual"}
          </DialogDescription>
        </DialogHeader>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Input
                type="checkbox"
                id="useLooseClient"
                checked={formData.useLooseClient}
                onChange={(e) => setFormData(prev => ({
                  ...prev,
                  useLooseClient: e.target.checked,
                  clientId: e.target.checked ? "" : prev.clientId
                }))}
                className="h-4 w-4 rounded border-gray-300 text-primary focus:ring-primary"
              />
              <Label htmlFor="useLooseClient" className="text-sm font-medium">
                Cliente não fixo
              </Label>
            </div>

            {formData.useLooseClient ? (
              <div>
                <Label htmlFor="looseClientName">Nome do Cliente</Label>
                <Input
                  id="looseClientName"
                  name="looseClientName"
                  placeholder="Nome do cliente não fixo"
                  value={formData.looseClientName}
                  onChange={handleInputChange}
                />
              </div>
            ) : (
              <div>
                <Label htmlFor="clientId">Cliente</Label>
                <Select
                  value={formData.clientId}
                  defaultValue={formData.clientId}
                  onValueChange={(value) => handleSelectChange("clientId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Selecione um cliente" />
                  </SelectTrigger>
                  <SelectContent>
                    {clients.map((client) => (
                      <SelectItem key={client.id} value={client.id}>
                        {client.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}
          </div>

          <div>
            <Label htmlFor="title">Título</Label>
            <Input
              id="title"
              name="title"
              placeholder="Título da demanda"
              value={formData.title}
              onChange={handleInputChange}
            />
          </div>

          <div>
            <Label htmlFor="description">Descrição</Label>
            <Textarea
              id="description"
              name="description"
              placeholder="Descrição detalhada da demanda"
              value={formData.description}
              onChange={handleInputChange}
            />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="dueDate">Data de vencimento</Label>
              <Input
                id="dueDate"
                name="dueDate"
                type="date"
                value={formatDateForInput(formData.dueDate)}
                onChange={handleDateChange}
                className="w-full"
              />
            </div>

            <div>
              <Label htmlFor="priority">Prioridade</Label>
              <Select
                value={formData.priority}
                defaultValue={formData.priority}
                onValueChange={(value) => handleSelectChange("priority", value)}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Selecione a prioridade" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="baixa">Baixa</SelectItem>
                  <SelectItem value="normal">Normal</SelectItem>
                  <SelectItem value="alta">Alta</SelectItem>
                  <SelectItem value="urgente">Urgente</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              value={formData.status}
              defaultValue={formData.status}
              onValueChange={(value) => handleSelectChange("status", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione o status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pendente">Pendente</SelectItem>
                <SelectItem value="estruturação de feed">Estruturação de feed</SelectItem>
                <SelectItem value="feed estruturado">Feed estruturado</SelectItem>
                <SelectItem value="repassado">Repassado</SelectItem>
                <SelectItem value="em andamento">Em andamento</SelectItem>
                <SelectItem value="em revisão">Em revisão</SelectItem>
                <SelectItem value="alteração">Alteração</SelectItem>
                <SelectItem value="captado">Captado</SelectItem>
                <SelectItem value="pend. captação">Pend. captação</SelectItem>
                <SelectItem value="anúncio concluído">Anúncio concluído</SelectItem>
                <SelectItem value="concluído">Concluído</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div>
            <Label htmlFor="assignedToId">Responsável</Label>
            <Select
              value={formData.assignedToId || "none"}
              defaultValue={formData.assignedToId || "none"}
              onValueChange={(value) => handleSelectChange("assignedToId", value === "none" ? null : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Selecione um responsável" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="none">Nenhum responsável</SelectItem>
                {users
                  .sort((a, b) => {
                    const nameA = (a.name || a.email).toLowerCase();
                    const nameB = (b.name || b.email).toLowerCase();
                    return nameA.localeCompare(nameB);
                  })
                  .map((user) => (
                    <SelectItem
                      key={user.id}
                      value={user.id}
                      className="flex items-center gap-2"
                    >
                      <div className="flex items-center gap-2">
                        <Avatar className="h-5 w-5 border-2 border-zinc-200">
                          <AvatarImage
                            src={user.image || undefined}
                            alt={user.name || user.email}
                          />
                          <AvatarFallback className="text-xs">
                            {user.name?.[0] || user.email[0]}
                          </AvatarFallback>
                        </Avatar>
                        <span>{user.name || user.email}</span>
                      </div>
                    </SelectItem>
                  ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="urlStructuringFeed">URLs</Label>
            <Card>
              <CardHeader className="text-xs">
                Em breve você poderá editar e adicionar URLs relacionadas à demanda.
              </CardHeader>
              <CardContent>
                <p className="text-xs text-muted-foreground mt-1">
                  URLs úteis para referência e acompanhamento.
                </p>
              </CardContent>
            </Card>
          </div>

          <DialogFooter>
            <Button
              type="button"
              variant="outline"
              onClick={() => setIsOpen(false)}
              className="mb-2"
            >
              Cancelar
            </Button>
            <Button type="submit" disabled={isLoading} className="mb-2">
              {isLoading ? (
                <Ellipsis />
              ) : initialData ? "Atualizar" : "Criar"}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  );
}
