"use client"

import { useState, useEffect } from 'react';
import { Ellipsis, Users } from 'lucide-react';
import { toast } from 'sonner';
import { Avatar, AvatarFallback, AvatarImage } from '@/app/components/ui/avatar';
import { Button } from '@/app/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/app/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/app/components/ui/select';
import { Badge } from '@/app/components/ui/badge';

interface UserData {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

interface AssignBulkContentProps {
  contentIds: string[];
  onSuccess?: () => void;
  onLocalUpdate?: (contentIds: string[], userId: string | null, user?: User | null) => void;
}

export function AssignBulkContent({ contentIds, onSuccess, onLocalUpdate }: AssignBulkContentProps) {
  const [users, setUsers] = useState<UserData[]>([]);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isOpen, setIsOpen] = useState(false);

  useEffect(() => {
    if (isOpen) {
      fetchUsers();
    }
  }, [isOpen]);

  const fetchUsers = async () => {
    try {
      const response = await fetch('/api/users');
      if (response.ok) {
        const data = await response.json();
        setUsers(data);
      } else {
        toast.error('Erro ao carregar usuários');
      }
    } catch (error) {
      console.error('Error fetching users:', error);
      toast.error('Erro ao carregar usuários');
    }
  };

  const handleAssign = async () => {
    if (contentIds.length === 0) {
      toast.error('Selecione pelo menos uma demanda');
      return;
    }

    setIsLoading(true);

    const selectedUser = selectedUserId ? users.find(user => user.id === selectedUserId) : null;
    const userForLocalUpdate = selectedUser ? {
      id: selectedUser.id,
      name: selectedUser.name,
      email: selectedUser.email,
      image: selectedUser.image
    } : null;

    if (onLocalUpdate) {
      onLocalUpdate(contentIds, selectedUserId, userForLocalUpdate);
    }

    try {
      const response = await fetch(`/api/contents/assign-bulk`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ contentIds, userId: selectedUserId }),
      });

      if (response.ok) {
        const result = await response.json();
        toast.success(`${result.count} demandas atribuídas com sucesso`);
        setIsOpen(false);

        if (onSuccess) {
          onSuccess();
        }
      } else {
        const error = await response.json();
        toast.error(error.message || 'Erro ao atribuir demandas');
      }
    } catch (error) {
      console.error('Error assigning contents:', error);
      toast.error('Erro ao atribuir demandas');
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="gap-2 whitespace-nowrap"
          disabled={contentIds.length === 0}
        >
          <Users className="h-4 w-4" />
          Atribuir selecionados
          {contentIds.length > 0 && (
            <Badge variant="secondary" className="ml-1">
              {contentIds.length}
            </Badge>
          )}
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Atribuir pessoa responsável</DialogTitle>
          <DialogDescription>
            {contentIds.length > 0
              ? `Selecione um membro da equipe para atribuir às ${contentIds.length} demandas selecionadas`
              : "Selecione pelo menos uma demanda para atribuir"}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Select
            value={selectedUserId || 'none'}
            onValueChange={(value) => setSelectedUserId(value === 'none' ? null : value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Selecione um responsável" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="none">Nenhum responsável</SelectItem>
              {users.map((user) => (
                <SelectItem key={user.id} value={user.id} className="flex items-center gap-2">
                  <div className="flex items-center gap-2">
                  <Avatar className="h-6 w-6 border-2 border-zinc-200">
                      <AvatarImage src={user.image || undefined} alt={user.name || user.email} />
                      <AvatarFallback className="text-xs">
                        {user.name?.[0] || user.email[0]}
                      </AvatarFallback>
                    </Avatar>
                    <span>{user.name || user.email}</span>
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex justify-end gap-2">
          <Button variant="outline" onClick={() => setIsOpen(false)} className="whitespace-nowrap">
            Cancelar
          </Button>
          <Button onClick={handleAssign} disabled={isLoading} className="whitespace-nowrap">
            {isLoading ? <Ellipsis /> : 'Atribuir'}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
