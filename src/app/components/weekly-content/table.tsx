"use client";

import React, { useEffect, useState } from 'react';
import { But<PERSON> } from "@/app/components/ui/button";
import { Badge } from "@/app/components/ui/badge";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table";
import { ActivityInfo } from "@/app/components/activity-info";
import { EditPlanActivity } from "@/app/components/edit-plan-activity";
import { RemovesPlanActivity } from "@/app/components/removes-plan-activity";
import { UpdateContentStatus } from "@/app/components/update-content-status";
import { ContentSteps } from "@/app/components/content-steps";
import { ContentAssignmentView } from "@/app/components/content-assignment-view";
import { ClipboardCopy } from "lucide-react";
import { toast } from "sonner";
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';

interface ContentType {
    clientId?: string;
    id: string;
    activityDate: string;
    contentType: string;
    channel: string;
    details: string;
    destination: string;
    caption?: string;
    status?: string;
    copywriting?: string;
    reference?: string;
    urlStructuringFeed?: string[];
    assignedTo?: {
        id: string;
        name: string;
        email: string;
        image?: string;
    } | null;
    steps?: {
        id: string;
        type: string;
        assignedTo: {
            id: string;
            name: string;
            email: string;
            image?: string;
        }
    }[];
}

interface WeeklyContentTableProps {
    activities: {
        id: string;
        description: string;
        week: number;
        contents: ContentType[];
    }[];
    refreshClientData: () => void;
    updateKey: number;
    isReadOnly: boolean;
    userRole: string;
}

export const WeeklyContentTable = ({
    activities,
    refreshClientData,
    updateKey,
    isReadOnly,
}: WeeklyContentTableProps) => {
    const [userRole, setUserRole] = useState('');
    const { data: session, status } = useSession();
    const router = useRouter();

    useEffect(() => {
        const checkUserRole = async () => {
            if (session?.user?.email) {
                try {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        setUserRole(user?.role || '');
                    }
                } catch (error) {
                    console.error("Erro ao verificar função do usuário:", error);
                }
            }
        };

        if (status === "authenticated") {
            checkUserRole();
        } else if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, session, router]);

    return (
        <Table className="w-full table-fixed">
            <TableHeader>
                <TableRow>
                    <TableHead className="p-2 text-xs w-[18%]">Data</TableHead>
                    <TableHead className="p-2 text-xs w-[17%]">Tipo</TableHead>
                    <TableHead className="p-2 text-xs w-[17%]">Destino</TableHead>
                    <TableHead className="p-2 text-xs w-[28%]">Canal</TableHead>
                    <TableHead className="p-2 text-xs text-right w-[20%]">Status</TableHead>
                </TableRow>
            </TableHeader>

            <TableBody>
                {activities.flatMap(activity =>
                    (activity.contents || []).map(content => (
                        <React.Fragment key={`${content.id}-${updateKey}`}>
                            <TableRow>
                                <TableCell className="font-medium p-2 text-xs">
                                    <div>
                                        <span>
                                            {new Date(content.activityDate).toLocaleDateString("pt-BR", {
                                                weekday: 'short',
                                                day: "2-digit",
                                                month: "2-digit",
                                            })}
                                        </span>
                                        <span className="block text-[10px] text-muted-foreground mt-0.5">
                                            #{content.id.substring(0, 8).toUpperCase()}
                                        </span>
                                    </div>
                                </TableCell>
                                <TableCell className="p-2">
                                    <Badge variant="outline" className="justify-center text-xs truncate w-full">
                                        {content.contentType}
                                    </Badge>
                                </TableCell>
                                <TableCell className="p-2">
                                    <Badge variant="outline" className="justify-center text-xs truncate w-full">
                                        {content.destination}
                                    </Badge>
                                </TableCell>
                                <TableCell className="p-2 w-[28%]">
                                    <div className="flex flex-wrap gap-1">
                                        {content.channel.split(', ').map((channel, idx) => (
                                            <Badge
                                                key={`${content.id}-channel-${idx}`}
                                                variant="outline"
                                                className="text-xs py-0.5 px-1.5 max-w-full"
                                            >
                                                <span className="truncate block" title={channel}>
                                                    {channel}
                                                </span>
                                            </Badge>
                                        ))}
                                    </div>
                                </TableCell>
                                <TableCell className="text-right p-2">
                                    <UpdateContentStatus
                                        contentId={content.id}
                                        currentStatus={content.status || 'pendente'}
                                        onSuccess={refreshClientData}
                                        disabled={isReadOnly}
                                        isAdmin={userRole === 'ADMIN' || userRole === 'DEVELOPER'}
                                    />
                                </TableCell>
                            </TableRow>

                            <TableRow>
                                <TableCell
                                    colSpan={5}
                                    className="py-2 px-2 text-xs bg-white dark:bg-zinc-900"
                                >
                                    <div className="flex justify-between items-start">
                                        <div className="flex-1">
                                            {content.details}
                                            <ContentAssignmentView
                                                assignedTo={content.assignedTo}
                                                steps={content.steps}
                                            />
                                        </div>
                                        <div className="flex gap-2 items-center">
                                            <ActivityInfo 
                                                activity={content}
                                                localReference={content.reference}
                                                localCopywriting={content.copywriting}
                                                localCaption={content.caption}
                                                localURL={content.urlStructuringFeed}
                                            />
                                            <EditPlanActivity
                                                content={{
                                                    ...content,
                                                    clientId: content.clientId || '',
                                                    weeklyActivityId: activity.id,
                                                    assignedToId: content.assignedTo?.id || null,
                                                    assignedTo: content.assignedTo || null,
                                                }}
                                                onSuccess={refreshClientData}
                                                disabled={isReadOnly}
                                            />
                                            <RemovesPlanActivity
                                                content={content}
                                                onSuccess={refreshClientData}
                                                disabled={isReadOnly}
                                            />
                                            <ContentSteps
                                                contentId={content.id}
                                                onSuccess={refreshClientData}
                                            />
                                            <Button variant="outline" size="icon" onClick={() => {
                                                navigator.clipboard.writeText(content.details);
                                                toast.success("Conteúdo copiado para a área de transferência");
                                            }}>
                                                <ClipboardCopy />
                                            </Button>
                                        </div>
                                    </div>
                                </TableCell>
                            </TableRow>
                        </React.Fragment>
                    ))
                )}
            </TableBody>
        </Table>
    );
};