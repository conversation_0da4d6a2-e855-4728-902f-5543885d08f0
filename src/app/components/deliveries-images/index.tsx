"use client";

import React, { useEffect, useState, useMemo } from "react";
import Image from "next/image";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { extractGoogleDriveId } from "@/app/components/deliveries-content/render-delivery-item";
import { <PERSON>, CardContent, CardFooter, CardHeader } from "@/app/components/ui/card";
import { Button } from "@/app/components/ui/button";
import Loading from "../ui/loading";
import { ExternalLink, Download } from "lucide-react";
import DeliveryDownloadModal from '@/app/components/delivery-download-modal';
import Link from "next/link";
import ApproveDemandButton from "../approve-demand-button";
import { AddContentReview } from "../add-content-review";

interface Content {
    details?: string;
    id: string;
    activityDate: string;
    status?: string;
    contentType?: string;
    destination?: string;
    channel?: string;
    canonicalChannel?: string;
    caption?: string;
    urlStructuringFeed?: string | string[];
    urlThumbnails?: string[];
    urlMediaTypes?: string[];
    urlTypes?: string[];
    approved?: boolean;
    review?: string;
    reviewedBy?: { id?: string; name?: string; email?: string; image?: string };
}

type RawContent = { [key: string]: unknown };

interface WeeklyActivity {
    id?: string;
    description?: string;
    week?: number;
    contents?: RawContent[];
    approved?: boolean;
    status?: string;
}

interface MonthlyPlanning {
    id?: string;
    month?: number;
    year?: number;
    status?: string;
    activities?: WeeklyActivity[];
}

interface DeliveriesImagesProps {
    clientId: string;
}

const presignedCache = new Map<string, string>();

const encodeKeySegments = (key: string) => key.split('/').map(encodeURIComponent).join('/')

const getDisplaySrc = (source?: string) => {
    if (!source) return '/images/placeholder.png';
    try {
        if (source.startsWith('data:')) return source;
        if (/^(https?:)?\/\//i.test(source)) return source;

        const publicBase = process.env.NEXT_PUBLIC_S3_BASE_URL;
        if (publicBase) {
            return `${publicBase.replace(/\/$/, '')}/${encodeKeySegments(source)}`;
        }

        const bucket = process.env.NEXT_PUBLIC_S3_BUCKET;
        if (bucket) {
            return `https://${bucket}.s3.amazonaws.com/${encodeKeySegments(source)}`;
        }

        return source;
    } catch {
        return source;
    }
}

export const DeliveriesImages = ({ clientId }: DeliveriesImagesProps) => {
    const [contents, setContents] = useState<Content[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [localPresignedMap, setLocalPresignedMap] = useState<Record<string, string>>({});
    const [selectedChannel, setSelectedChannel] = useState<string>('Todos');
    const [downloadModalOpen, setDownloadModalOpen] = useState(false);
    const [downloadItems, setDownloadItems] = useState<Array<{ key: string; label?: string; thumb?: string | null }>>([]);

    const getAllUrls = (urls: string | string[] | undefined): string[] => {
        if (!urls) return [];
        return Array.isArray(urls) ? urls.filter(Boolean) as string[] : [urls];
    };

    const canonicalizeChannel = (raw?: string | null): string | null => {
        if (!raw) return null;
        const s = String(raw).trim().toLowerCase();
        if (!s) return null;
        if (s.includes('insta') || s.includes('instagram')) return 'Instagram';
        if (s.includes('facebook') || s.includes('fb')) return 'Facebook';
        if (s.includes('youtube') || s.includes('yt')) return 'YouTube';
        if (s.includes('tiktok')) return 'TikTok';
        if (s.includes('linkedin')) return 'LinkedIn';
        if (s.includes('twitter') || s.includes('x ')) return 'X';
        return 'Outros';
    };

    useEffect(() => {
        if (!clientId) return;

        const fetchData = async () => {
            try {
                setIsLoading(true);
                const res = await fetch(`/api/clients/${clientId}?include=monthlyPlannings.activities.contents`);
                if (!res.ok) throw new Error(`Erro ${res.status}`);
                const data = await res.json();

                const all: Content[] = [];

                if (Array.isArray(data.monthlyPlannings)) {
                    (data.monthlyPlannings as MonthlyPlanning[]).forEach((planning) => {
                        if (planning.status !== 'aprovado') return;

                        const approvedActivities = (planning.activities || []).filter(a => {
                            const wk = a as WeeklyActivity;
                            return wk.approved === true && wk.contents?.filter(c => c.status !== 'concluído').length === wk.contents?.length;
                        });

                        approvedActivities.forEach((activity) => {
                            (activity.contents || []).forEach((content) => {
                                const id = content?.['id'] ? String(content['id']) : undefined;
                                const activityDate = content?.['activityDate'] ? String(content['activityDate']) : undefined;
                                if (!id || !activityDate) return;

                                {
                                    const rawReviewedBy = content['reviewedBy'] as Record<string, unknown> | undefined;
                                    all.push({
                                        id,
                                        activityDate,
                                        details: typeof content['details'] === 'string' ? String(content['details']) : undefined,
                                        destination: typeof content['destination'] === 'string' ? String(content['destination']) : undefined,
                                        channel: typeof content['channel'] === 'string' ? String(content['channel']) : (typeof content['destination'] === 'string' ? String(content['destination']) : undefined),
                                        caption: typeof content['caption'] === 'string' ? String(content['caption']) : undefined,
                                        status: typeof content['status'] === 'string' ? String(content['status']) : undefined,
                                        review: typeof content['review'] === 'string' ? String(content['review']) : undefined,
                                        reviewedBy: rawReviewedBy ? {
                                            id: rawReviewedBy['id'] ? String(rawReviewedBy['id']) : undefined,
                                            name: rawReviewedBy['name'] ? String(rawReviewedBy['name']) : undefined,
                                            email: rawReviewedBy['email'] ? String(rawReviewedBy['email']) : undefined,
                                            image: rawReviewedBy['image'] ? String(rawReviewedBy['image']) : undefined,
                                        } : undefined,
                                        approved: typeof content['approved'] === 'boolean' ? Boolean(content['approved']) : undefined,
                                        urlStructuringFeed: (Array.isArray(content['urlStructuringFeed']) || typeof content['urlStructuringFeed'] === 'string') ? (content['urlStructuringFeed'] as string | string[]) : undefined,
                                        urlThumbnails: Array.isArray(content['urlThumbnails']) ? (content['urlThumbnails'] as string[]) : undefined,
                                        urlMediaTypes: Array.isArray(content['urlMediaTypes']) ? (content['urlMediaTypes'] as string[]) : undefined,
                                        urlTypes: Array.isArray(content['urlTypes']) ? (content['urlTypes'] as string[]) : undefined,
                                        canonicalChannel: canonicalizeChannel(content['channel'] ? String(content['channel']) : (content['destination'] ? String(content['destination']) : null)) || undefined,
                                    });
                                }
                            });
                        });
                    });
                }

                const sorted = all.sort((a, b) => new Date(a.activityDate).getTime() - new Date(b.activityDate).getTime());
                setContents(sorted);
            } catch (e) {
                console.error('Erro ao carregar imagens de entregas', e);
                setContents([]);
            } finally {
                setIsLoading(false);
            }
        };

        fetchData();
    }, [clientId]);

    const allUrlsFor = (content: Content) => {
        return getAllUrls(content.urlStructuringFeed);
    };

    useEffect(() => {
        const keysToFetch: string[] = [];
        contents.forEach(c => {
            const structUrls = Array.isArray(c.urlStructuringFeed) ? c.urlStructuringFeed.filter(Boolean) as string[] : (c.urlStructuringFeed ? [String(c.urlStructuringFeed)] : []);
            const thumbs = Array.isArray(c.urlThumbnails) ? c.urlThumbnails.filter(Boolean) as string[] : [];

            // Merge structuring feed urls and thumbnails so thumbnails (used as posters) are prefetched too
            const allCandidates = structUrls.concat(thumbs);

            allCandidates.forEach(src => {
                if (!src) return;
                if (src.startsWith('data:') || /^(https?:)?\/\//i.test(src)) return;
                if (extractGoogleDriveId(src)) return;
                const key = src;
                if (!presignedCache.has(key) && !keysToFetch.includes(key)) keysToFetch.push(key);
                else if (presignedCache.has(key)) setLocalPresignedMap(prev => ({ ...prev, [key]: presignedCache.get(key)! }));
            });
        });

        if (keysToFetch.length === 0) return;

        keysToFetch.forEach(async key => {
            try {
                const res = await fetch('/api/uploads/presigned-get', {
                    method: 'POST', headers: { 'Content-Type': 'application/json' }, body: JSON.stringify({ key })
                });
                if (!res.ok) return;
                const data = await res.json().catch(() => null);
                if (data?.url) {
                    presignedCache.set(key, String(data.url));
                    setLocalPresignedMap(prev => ({ ...prev, [key]: String(data.url) }));
                }
            } catch (e) {
                console.debug('presign failed', key, e);
            }
        });
    }, [contents]);

    const channelList = useMemo(() => {
        const set = new Set<string>();
        contents.forEach(c => {
            if (c.canonicalChannel) set.add(c.canonicalChannel);
        });

        const preferredOrder = ['Instagram', 'Facebook', 'YouTube', 'TikTok', 'Kawai', 'Behance', 'Pinterest', 'Outros'];
        const ordered: string[] = [];
        preferredOrder.forEach(p => { if (set.has(p)) ordered.push(p); });
        Array.from(set).forEach(s => { if (!ordered.includes(s)) ordered.push(s); });

        return ['Todos', ...ordered];
    }, [contents]);

    const channelLogos: Record<string, string | null> = {
        Instagram: '/Instagram_Glyph_Gradient.png',
        Facebook: '/Facebook_Logo_Primary.png',
        YouTube: '/yt_icon_red_digital.png',
        TikTok: null,
        Kaiwai: null,
        Behance: null,
        Pinterest: null,
        Outros: null,
    };

    const filteredContents = useMemo(() => {
        if (!selectedChannel || selectedChannel === 'Todos') return contents;
        return contents.filter(c => c.canonicalChannel === selectedChannel);
    }, [contents, selectedChannel]);

    const selectSrc = (source?: string) => {
        if (!source) return null;
        if (source.startsWith('data:')) return source;
        const driveId = extractGoogleDriveId(source);
        if (driveId) return `/api/drive-proxy?id=${driveId}&quality=high&size=large`;
        if (/^(https?:)?\/\//i.test(source)) return source;
        const cached = localPresignedMap[source] || presignedCache.get(source);
        if (cached) return cached;

        // Only return a public display URL if a public base or bucket is configured.
        // Otherwise return null so callers can fallback to placeholder instead of a raw relative key
        const publicBase = process.env.NEXT_PUBLIC_S3_BASE_URL;
        const bucket = process.env.NEXT_PUBLIC_S3_BUCKET;
        if (publicBase || bucket) return getDisplaySrc(source);
        return null;
    };

    const handleReviewUpdate = (id: string, newReview: string) => {
        setContents(prev => prev.map(c => c.id === id ? { ...c, review: newReview } : c));
    };

    const handleStatusUpdate = (id: string, newStatus: string) => {
        setContents(prev => prev.map(c => c.id === id ? { ...c, status: newStatus } : c));
    };

    if (isLoading) {
        return (
            <div className="flex justify-center items-center h-96">
                <Loading />
            </div>
        );
    }

    if (contents.length === 0) {
        return (
            <div className="p-4 text-center text-sm text-muted-foreground">Nenhum conteúdo encontrado</div>
        );
    }
    return (
        <div>
            <div className="mb-4 flex flex-wrap gap-2">
                {channelList.map(ch => (
                    <Button
                        key={ch}
                        variant={ch === selectedChannel ? 'secondary' : 'outline'}
                        onClick={() => setSelectedChannel(ch)}
                        className="flex items-center gap-2"
                    >
                        {channelLogos[ch] ? (
                            <Image src={channelLogos[ch] as string} alt={ch} width={16} height={16} />
                        ) : null}
                        <span>{ch}</span>
                    </Button>
                ))}
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {filteredContents.map(content => {
                    const urls = allUrlsFor(content);
                    const urlMediaTypes = content.urlMediaTypes || [];
                    const urlThumbnails = content.urlThumbnails || [];

                    // Para a primeira URL, verificar se é vídeo
                    const firstIndex = 0;
                    const isVideo = (() => {
                        // prefer explicit type info on the content
                        if (Array.isArray(content.urlTypes) && content.urlTypes.length > 0) {
                            if (content.urlTypes[firstIndex] && content.urlTypes[firstIndex].toLowerCase() === 'vídeo') {
                                // still confirm by extension on this specific url
                                const videoExtRegex = /\.(mp4|webm|ogg|mov|m3u8)(\?.*)?$/i;
                                if (videoExtRegex.test(urls[firstIndex])) return true;
                            }
                        }

                        // fallback: check extension on url
                        const videoExtRegex = /\.(mp4|webm|ogg|mov|m3u8)(\?.*)?$/i;
                        try {
                            if (videoExtRegex.test(urls[firstIndex])) return true;
                        } catch {
                            // ignore
                        }

                        return false;
                    })();

                    const isFeed = urlMediaTypes[firstIndex] && urlMediaTypes[firstIndex].toLowerCase() === 'feed';
                    const thumbnail = urlThumbnails[firstIndex];

                    let displaySrc: string | undefined = undefined;
                    let posterSrc: string | undefined = undefined;
                    if (isVideo) {
                        displaySrc = urls[firstIndex];
                        // Use the corresponding thumbnail as poster if available
                        if (thumbnail) {
                            posterSrc = thumbnail;
                        } else {
                            // fallback: choose first non-video URL from urls
                            const videoExtRegex = /\.(mp4|webm|ogg|mov|m3u8)(\?.*)?$/i;
                            for (const candidate of urls) {
                                try {
                                    if (!videoExtRegex.test(candidate)) {
                                        posterSrc = candidate;
                                        break;
                                    }
                                } catch {
                                    // ignore
                                }
                            }
                        }
                    } else {
                        displaySrc = urls[firstIndex] || thumbnail;
                    }

                    const src = selectSrc(displaySrc);
                    const poster = posterSrc ? selectSrc(posterSrc) : undefined;

                    // debug: expose computed resolved src to browser console for troubleshooting posters
                    try { console.debug('[DeliveriesImages] resolved', { id: content.id, displaySrc, resolved: src, poster, isVideo, isFeed }); } catch { }
                    const formattedDate = format(new Date(content.activityDate), 'dd/MM/yyyy', { locale: ptBR });

                    return (
                        <Card key={content.id} className="overflow-hidden">
                            <div className="relative h-96 bg-gray-100 dark:bg-zinc-800 flex items-center justify-center">
                                {isVideo && src ? (
                                    <video controls className="max-h-full max-w-full rounded-sm" poster={poster || undefined}>
                                        <source src={src} />
                                        Seu navegador não suporta reprodução de vídeo.
                                    </video>
                                ) : (
                                    <Image src={src || '/images/placeholder.png'} alt={`Entrega ${content.id}`} width={800} height={600} className="object-contain max-h-full max-w-full" unoptimized={true} />
                                )}
                            </div>
                            <CardHeader>
                                <div className="flex flex-col">
                                    <div className="flex items-start justify-between gap-2">
                                        <div>
                                            <strong>{content.details || 'Conteúdo'}</strong>
                                            <div className="text-sm text-muted-foreground mt-1">{content.destination || ''}</div>
                                        </div>
                                        <div className="flex items-end gap-2 flex-col">
                                            <span className="text-sm">{formattedDate}</span>
                                            {urls.length > 1 && (
                                                <Link href="/deliveries/all-images" title="Ver todos" className="space-x-2">
                                                    <span className="text-xs text-muted-foreground">
                                                        + {urls.length - 1} outros criativos
                                                    </span>
                                                    <Button variant="outline" size="sm">
                                                        <ExternalLink size={12} /> Ver todos
                                                    </Button>
                                                </Link>
                                            )}
                                            {content.status === 'aprovado' && (
                                                <Button size="sm" onClick={() => {
                                                    const thumbs = Array.isArray(content.urlThumbnails) ? content.urlThumbnails : [];
                                                    const items = urls.map((u, i) => ({ key: String(u), label: undefined as string | undefined, thumb: (thumbs[i] ? selectSrc(thumbs[i]) : selectSrc(u)) }));
                                                    setDownloadItems(items);
                                                    setDownloadModalOpen(true);
                                                }} title="Visualizar/baixar arquivos">
                                                    <Download size={12} />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <p className="mt-2 text-sm text-muted-foreground line-clamp-3">
                                    {content.caption || 'Sem legenda'}
                                </p>
                            </CardContent>
                            <CardFooter>
                                <div className="flex gap-2 w-full">
                                    <div className="w-1/2">
                                        <AddContentReview
                                            size="sm"
                                            contentId={content.id}
                                            label={content.review ? 'Solicitação feita' : 'Solicitar ajuste geral'}
                                            disabled={(content.status === 'aprovado' || content.status === 'concluído' || content.approved)}
                                            isClient
                                            currentReview={content.review || ''}
                                            reviewedBy={content.reviewedBy}
                                            onReviewUpdate={(newReview) => handleReviewUpdate(content.id, newReview)}
                                            type="content"
                                            onStatusUpdate={(newStatus) => handleStatusUpdate(content.id, newStatus)}
                                        />
                                        {content.review && (
                                            <p className="text-xs text-muted-foreground text-center mt-1">
                                                Solicitado por {content.reviewedBy?.name || 'Usuário anônimo'}
                                            </p>
                                        )}
                                    </div>
                                    <div className="w-1/2">
                                        <ApproveDemandButton
                                            size="sm"
                                            contentId={content.id}
                                            disabled={Boolean(content.review)}
                                            currentStatus={content.status}
                                            onSuccess={() => {
                                                setContents(prev => prev.map(c => c.id === content.id ? { ...c, status: 'aprovado' } : c));
                                            }}
                                        />
                                    </div>
                                </div>
                            </CardFooter>
                        </Card>
                    );
                })}
            </div>
            <DeliveryDownloadModal open={downloadModalOpen} onOpenChange={setDownloadModalOpen} items={downloadItems} />
        </div>
    );
};

export default DeliveriesImages;
