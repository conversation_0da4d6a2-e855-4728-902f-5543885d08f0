"use client";

import { useState, useEffect } from 'react';
import { Button } from "@/app/components/ui/button";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/app/components/ui/dropdown-menu";
import { toast } from "sonner";
import { Ellipsis } from 'lucide-react';

interface UpdateContentStatusProps {
  contentId: string;
  currentStatus?: string;
  onSuccess?: () => void;
  onLocalUpdate?: (newStatus: string) => void;
  disabled?: boolean;
  isAdmin?: boolean;
}

export const UpdateContentStatus = ({
  contentId,
  currentStatus = 'pendente',
  onSuccess,
  onLocalUpdate,
  disabled = false,
  isAdmin = false
}: UpdateContentStatusProps) => {
  const [isUpdating, setIsUpdating] = useState(false);
  const [localStatus, setLocalStatus] = useState(currentStatus);

  useEffect(() => {
    setLocalStatus(currentStatus);
  }, [currentStatus]);

  const updateStatus = async (status: string) => {
    if (disabled || isUpdating) return;

    setLocalStatus(status);
    if (onLocalUpdate) {
      onLocalUpdate(status);
    }

    setIsUpdating(true);
    try {
      const response = await fetch(`/api/contents/${contentId}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Erro ao atualizar status');
      }

      toast.success('Status atualizado com sucesso');

      if (onSuccess && !onLocalUpdate) {
        onSuccess();
      }
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      toast.error(error instanceof Error ? error.message : 'Erro ao atualizar status');

      setLocalStatus(currentStatus);
      if (onLocalUpdate) {
        onLocalUpdate(currentStatus);
      }
    } finally {
      setIsUpdating(false);
    }
  };

  const getStatusClassName = (status: string) => {
    switch (status) {
      case 'em andamento':
        return "bg-yellow-100 text-yellow-800 hover:bg-yellow-200 dark:bg-yellow-950 dark:text-yellow-300 dark:hover:bg-yellow-900";
      case 'estruturação de feed':
        return "bg-gray-100 text-gray-800 hover:bg-gray-200 dark:bg-gray-950 dark:text-gray-300 dark:hover:bg-gray-900";
      case 'feed estruturado':
        return "bg-violet-100 text-violet-800 hover:bg-violet-200 dark:bg-violet-950 dark:text-violet-300 dark:hover:bg-violet-900";
      case 'repassado':
        return "bg-purple-100 text-purple-800 hover:bg-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:hover:bg-purple-900";
      case 'repassado':
        return "bg-purple-100 text-purple-800 hover:bg-purple-200 dark:bg-purple-950 dark:text-purple-300 dark:hover:bg-purple-900";
      case 'em revisão':
        return "bg-orange-100 text-orange-800 hover:bg-orange-200 dark:bg-orange-950 dark:text-orange-300 dark:hover:bg-orange-900";
      case 'alteração':
        return "bg-rose-100 text-rose-800 hover:bg-rose-200 dark:bg-rose-950 dark:text-rose-300 dark:hover:bg-rose-900";
      case 'pend. captação':
        return "bg-orange-100 text-orange-800 hover:bg-orange-200 dark:bg-orange-950 dark:text-orange-300 dark:hover:bg-orange-900";
      case 'captado':
        return "bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-950 dark:text-blue-300 dark:hover:bg-blue-900";
      case 'concluído':
        return "bg-green-100 text-green-800 hover:bg-green-200 dark:bg-green-950 dark:text-green-300 dark:hover:bg-green-900";
      case 'anúncio concluído':
        return "bg-green-100 text-lime-800 hover:bg-lime-200 dark:bg-lime-950 dark:text-lime-300 dark:hover:bg-lime-900";
      default:
        return "bg-gray-50 text-gray-800 hover:bg-gray-100 dark:bg-gray-800 dark:text-gray-300 dark:hover:bg-gray-700";
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={`p-0 text-xs truncate !focus:outline-none !focus:ring-0 rounded px-1.5 shadow-none hover:brightness-90 ${getStatusClassName(localStatus)}`}
          disabled={disabled || isUpdating}
        >
          {isUpdating ? <Ellipsis /> : localStatus === 'repassado' ? 'atribuído' : localStatus}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className='ml-2'>
        {isAdmin ? (
          <>
            <DropdownMenuItem
              onClick={() => updateStatus("pendente")}
              disabled={disabled || isUpdating || localStatus === "pendente"}
              className={localStatus === "pendente" ? "bg-gray-100 text-gray-800 dark:bg-gray-950 dark:text-gray-300" : ""}
            >
              pendente
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("em andamento")}
              disabled={disabled || isUpdating || localStatus === "em andamento"}
              className={localStatus === "em andamento" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-950 dark:text-yellow-300" : ""}
            >
              em andamento
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("estruturação de feed")}
              disabled={disabled || isUpdating || localStatus === "estruturação de feed"}
              className={localStatus === "estruturação de feed" ? "bg-gray-100 text-gray-800 dark:bg-gray-950 dark:text-gray-300" : ""}
            >
              estruturação de feed
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("feed estruturado")}
              disabled={disabled || isUpdating || localStatus === "feed estruturado"}
              className={localStatus === "feed estruturado" ? "bg-violet-100 text-violet-800 dark:bg-violet-950 dark:text-violet-300" : ""}
            >
              feed estruturado
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("repassado")}
              disabled={disabled || isUpdating || localStatus === "repassado"}
              className={localStatus === "repassado" ? "bg-purple-100 text-purple-800 dark:bg-purple-950 dark:text-purple-300" : ""}
            >
              repassado
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("em revisão")}
              disabled={disabled || isUpdating || localStatus === "em revisão"}
              className={localStatus === "em revisão" ? "bg-orange-100 text-orange-800 dark:bg-orange-950 dark:text-orange-300" : ""}
            >
              em revisão
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("alteração")}
              disabled={disabled || isUpdating || localStatus === "alteração"}
              className={localStatus === "alteração" ? "bg-rose-100 text-rose-800 dark:bg-rose-950 dark:text-rose-300" : ""}
            >
              alteração
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("pend. captação")}
              disabled={disabled || isUpdating || localStatus === "pend. captação"}
              className={localStatus === "pend. captação" ? "bg-orange-100 text-orange-800 dark:bg-orange-950 dark:text-orange-300" : ""}
            >
              pend. captação
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("captado")}
              disabled={disabled || isUpdating || localStatus === "captado"}
              className={localStatus === "captado" ? "bg-blue-100 text-blue-800 dark:bg-blue-950 dark:text-blue-300" : ""}
            >
              captado
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("anúncio concluído")}
              disabled={disabled || isUpdating || localStatus === "anúncio concluído"}
              className={localStatus === "anúncio concluído" ? "bg-lime-100 text-lime-800 dark:bg-lime-950 dark:text-lime-300" : ""}
            >
              anúncio concluído
            </DropdownMenuItem>
            <DropdownMenuItem
              onClick={() => updateStatus("concluído")}
              disabled={disabled || isUpdating || localStatus === "concluído"}
              className={localStatus === "concluído" ? "bg-green-100 text-green-800 dark:bg-green-950 dark:text-green-300" : ""}
            >
              concluído
            </DropdownMenuItem>
          </>
        ) : (
          <>
            <DropdownMenuItem
              onClick={() => updateStatus("em andamento")}
              disabled={disabled || isUpdating || localStatus === "em andamento"}
              className={localStatus === "em andamento" ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-950 dark:text-yellow-300" : ""}
            >
              em andamento
            </DropdownMenuItem>
          </>
        )}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};
