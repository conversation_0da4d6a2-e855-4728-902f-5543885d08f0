
"use client";

import { format } from "date-fns";
import { ptBR } from "date-fns/locale";

interface PdfHeaderProps {
    clientName: string;
    month?: string;
}

export const PdfHeader = ({ clientName, month }: PdfHeaderProps) => {
    const formattedMonth = month
        ? (() => {
            const [m, y] = month.split('-');
            const monthName = format(new Date(Number(y), Number(m) - 1, 1), 'MMMM', { locale: ptBR });
            return `${monthName.charAt(0).toUpperCase() + monthName.slice(1)} de ${y}`;
        })()
        : null;

    return (
        <div className="pdf-header-container" style={{ padding: '15mm', paddingTop: '8mm', paddingBottom: '5mm' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
                <div style={{
                    backgroundColor: '#e54729',
                    borderRadius: '12px',
                    padding: '8px 16px',
                    color: 'white',
                    fontSize: '11px'
                }}>
                    Etapa 07
                </div>
                <div style={{
                    border: '1px solid #e54729',
                    borderRadius: '12px',
                    padding: '8px 16px',
                    color: '#e54729',
                    fontSize: '11px'
                }}>
                    Entregas
                </div>
            </div>

            <h1 style={{
                color: '#e54729',
                fontSize: '24px',
                fontWeight: 'bold',
                marginTop: '20px'
            }}>
                ENTREGAS DE MATERIAIS
            </h1>

            {formattedMonth && (
                <h2 style={{
                    color: '#2c3e50',
                    fontSize: '16px',
                    fontWeight: 'bold',
                    marginTop: '10px',
                    textTransform: 'uppercase'
                }}>
                    {formattedMonth}
                </h2>
            )}

            <p style={{
                color: '#e54729',
                fontSize: '18px',
                marginTop: '10px'
            }}>
                {clientName}
            </p>

            <hr style={{
                border: 'none',
                borderTop: '1px solid #ccc',
                marginTop: '15px'
            }} />
        </div>
    );
};
