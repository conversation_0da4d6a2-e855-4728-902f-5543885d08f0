"use client";

import { useState, useEffect, use<PERSON>allback } from "react";
import { <PERSON><PERSON>sis, FilePenLine, AlertCircle, Sparkles } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, <PERSON>alogTitle, DialogTrigger } from "../ui/dialog";
import { Input } from "../ui/input";
import { toast } from "sonner";
import { Label } from "../ui/label";
import { Badge } from "../ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../ui/select";
import { Textarea } from "../ui/textarea";
import { Alert, AlertDescription } from "../ui/alert";
import { Switch } from "../ui/switch";
import { User } from "@prisma/client";
import Link from "next/link";

const contentTypeOptions = [
    { value: "estático", label: "Est<PERSON><PERSON><PERSON>" },
    { value: "vídeo", label: "Ví<PERSON><PERSON>" },
    { value: "carrossel", label: "Carrossel" },
    { value: "animação", label: "Animação" },
];

const destinationOptions = [
    { value: "Story", label: "Story" },
    { value: "Feed", label: "Feed" },
    { value: "Story/Feed", label: "Story/Feed" },
];

const priorityOptions = [
    { value: "baixa", label: "Baixa" },
    { value: "normal", label: "Normal" },
    { value: "alta", label: "Alta" },
    { value: "urgente", label: "Urgente" },
];

const statusOptions = [
    { value: "pendente", label: "Pendente" },
    { value: "estruturação de feed", label: "Estruturação de feed" },
    { value: "feed estruturado", label: "Feed estruturado" },
    { value: "repassado", label: "Repassado" },
    { value: "em andamento", label: "Em andamento" },
    { value: "em revisão", label: "Em revisão" },
    { value: "alteração", label: "Alteração" },
    { value: "pend. captação", label: "Pend. captação" },
    { value: "captado", label: "Captado" },
    { value: "anúncio concluído", label: "Anúncio concluído" },
    { value: "concluído", label: "Concluído" },
];

const channelOptions = [
    { value: "Instagram", label: "Instagram" },
    { value: "Facebook", label: "Facebook" },
    { value: "YouTube", label: "YouTube" },
    { value: "TikTok", label: "TikTok" },
    { value: "Kwai", label: "Kwai" },
    { value: "Behance", label: "Behance" },
    { value: "Pinterest", label: "Pinterest" },
];

const stepTypeOptions = [
    { value: "CAPTACAO", label: "Captação" },
    { value: "DESIGN", label: "Design" },
    { value: "EDICAO", label: "Edição" },
    { value: "TRAFEGO", label: "Tráfego" },
];

interface WeeklyActivity {
    id: string;
    week: number;
    description: string;
    monthlyPlanningId: string;
}

interface ContentUpdateData {
    activityDate: string;
    channel: string;
    contentType: string;
    destination: string;
    details: string;
    copywriting: string;
    reference: string;
    caption?: string;
    priority: string;
    status: string;
    weeklyActivityId?: string;
    urlStructuringFeed?: string[];
    carouselImagesCount?: number | null;
    assignedToId?: string | null;
}

interface EditPlanActivityProps {
    content: {
        clientId: string;
        id: string;
        activityDate: string;
        contentType: string;
        channel: string;
        details: string;
        destination?: string;
        copywriting?: string;
        reference?: string;
        caption?: string;
        priority?: string;
        status?: string;
        weeklyActivityId: string;
        urlStructuringFeed?: string[] | null;
        assignedToId?: string | null;
        carouselImagesCount?: number | null;
        assignedTo?: {
            id: string;
            name: string | null;
            email: string;
            image?: string | null;
        } | null;
    };
    onSuccess?: () => void;
    disabled?: boolean;
}

export const EditPlanActivity = ({ content, onSuccess, disabled }: EditPlanActivityProps) => {
    const [open, setOpen] = useState<boolean>(false);
    const [isLoading, setIsLoading] = useState<boolean>(false);
    const [activityDate, setActivityDate] = useState<string>("");
    const [selectedChannels, setSelectedChannels] = useState<string[]>([]);
    const [contentType, setContentType] = useState<string>("");
    const [destination, setDestination] = useState<string>("");
    const [details, setDetails] = useState<string>("");
    const [reference, setReference] = useState<string>("");
    const [copywriting, setCopywriting] = useState<string>("");
    const [caption, setCaption] = useState<string>("");
    const [originalCaption, setOriginalCaption] = useState<string>("");
    const [priority, setPriority] = useState<string>("normal");
    const [status, setStatus] = useState<string>("pendente");
    const [urlStructuringFeed, setUrlStructuringFeed] = useState<string[]>([]);
    const [carouselImagesCount, setCarouselImagesCount] = useState<number>(1);
    const [groupedWeeklyActivities, setGroupedWeeklyActivities] = useState<WeeklyActivity[]>([]);
    const [selectedWeeklyActivityId, setSelectedWeeklyActivityId] = useState<string>("");
    const [loadingWeeklyActivities, setLoadingWeeklyActivities] = useState<boolean>(false);
    const [weekChanged, setWeekChanged] = useState<boolean>(false);
    const [assignedToId, setAssignedToId] = useState<string | null>(null);

    const [users, setUsers] = useState<{
        role: string; id: string, name: string | null, email: string, image: string | null 
}[]>([]);
    const [useMultipleSteps, setUseMultipleSteps] = useState<boolean>(false);
    const [contentSteps, setContentSteps] = useState<{
        type: string,
        assignedToId: string,
        assignedTo?: {
            id: string,
            name: string | null,
            email: string,
            image: string | null
        }
    }[]>([]);
    const [originalContentSteps, setOriginalContentSteps] = useState<{
        type: string,
        assignedToId: string,
        assignedTo?: {
            id: string,
            name: string | null,
            email: string,
            image: string | null
        }
    }[]>([]);
    const [hasLoadedSteps, setHasLoadedSteps] = useState<boolean>(false);

    const usersWithRole = users.filter(user => user.role !== "DEVELOPER" && user.role !== "VIEWER");

    const fetchUsers = useCallback(async () => {
        try {
            const response = await fetch('/api/users');
            if (response.ok) {
                const data = await response.json();
                const filterUsers = data.filter((user: User) => user.role !== "DEVELOPER");
                setUsers(filterUsers);
            } else {
                console.error('Erro ao carregar usuários');
            }
        } catch (error) {
            console.error('Erro ao carregar usuários:', error);
        }
    }, []);

    const fetchWeeklyActivities = useCallback(async () => {
        if (!content.id) return;

        setLoadingWeeklyActivities(true);
        try {
            const response = await fetch(`/api/weekly-activities?contentId=${content.id}`);
            if (!response.ok) throw new Error('Erro ao buscar atividades semanais');

            const data = await response.json();

            const weekMap = new Map<number, WeeklyActivity>();

            data.forEach((activity: WeeklyActivity) => {
                if (activity.id === content.weeklyActivityId) {
                    weekMap.set(activity.week, activity);
                } else if (!weekMap.has(activity.week)) {
                    weekMap.set(activity.week, activity);
                }
            });

            const grouped = Array.from(weekMap.values()).sort((a, b) => a.week - b.week);
            setGroupedWeeklyActivities(grouped);

            setSelectedWeeklyActivityId(content.weeklyActivityId);
        } catch (error) {
            console.error('Erro ao buscar atividades semanais:', error);
            toast.error('Erro ao buscar atividades semanais');
        } finally {
            setLoadingWeeklyActivities(false);
        }
    }, [content.id, content.weeklyActivityId]);

    const fetchContentSteps = useCallback(async () => {
        try {
            const response = await fetch(`/api/content-steps?contentId=${content.id}`);

            if (response.ok) {
                const steps = await response.json();

                if (steps && steps.length > 0) {
                    setContentSteps(steps);
                    setOriginalContentSteps([...steps]);
                    setUseMultipleSteps(true);
                    setHasLoadedSteps(true);
                } else {
                    const contentResponse = await fetch(`/api/contents/${content.id}`);

                    if (contentResponse.ok) {
                        const contentData = await contentResponse.json();

                        const defaultSteps = stepTypeOptions.map(opt => ({
                            type: opt.value,
                            assignedToId: contentData.assignedToId || ''
                        }));

                        setContentSteps(defaultSteps);
                        setOriginalContentSteps([...defaultSteps]);

                        setUseMultipleSteps(false);
                        setHasLoadedSteps(true);
                    }
                }
            } else {
                console.error('Erro ao carregar etapas do conteúdo');
                toast.error('Erro ao carregar dados das etapas');
            }
        } catch (error) {
            console.error('Erro ao carregar etapas do conteúdo:', error);
            toast.error('Erro ao carregar dados das etapas');
        }
    }, [content.id]);

    useEffect(() => {
        if (content) {
            const date = new Date(content.activityDate);
            const formattedDate = date.toISOString().split('T')[0];

            setActivityDate(formattedDate);
            if (content.channel) {
                const channels = content.channel.split(',').map(ch => ch.trim());
                setSelectedChannels(channels);
            } else {
                setSelectedChannels([]);
            }
            setContentType(content.contentType);
            setDestination(content.destination || "Feed");
            setDetails(content.details);
            setCopywriting(content.copywriting || "");
            setReference(content.reference || "");
            try {
                const storageKey = `edit_plan_caption_${content.id}`;
                const saved = typeof window !== 'undefined' ? sessionStorage.getItem(storageKey) : null;
                if (saved !== null && saved !== undefined) {
                    setCaption(saved);
                } else {
                    setCaption(content.caption || "");
                }
            } catch {
                setCaption(content.caption || "");
            }
            setPriority(content.priority || "normal");
            setStatus(content.status || "pendente");
            setUrlStructuringFeed(content.urlStructuringFeed || []);
            setCarouselImagesCount(content.carouselImagesCount || 1);
            setSelectedWeeklyActivityId(content.weeklyActivityId);

            const assignedId = content.assignedToId || null;
            setAssignedToId(assignedId);

            setWeekChanged(false);
        }
    }, [content]);

    useEffect(() => {
        const storageKey = content ? `edit_plan_caption_${content.id}` : null;
        if (!storageKey) return;

        try {
            if (caption !== undefined && caption !== null) {
                sessionStorage.setItem(storageKey, caption);
            }
        } catch {
        }
    }, [caption, content]);

    useEffect(() => {
        if (open) return;
        try {
            const storageKey = content ? `edit_plan_caption_${content.id}` : null;
            if (storageKey) sessionStorage.removeItem(storageKey);
        } catch {
        }
    }, [open, content]);

    useEffect(() => {
        if (open) {
            fetchWeeklyActivities();
            fetchUsers();
            fetchContentSteps();
        }
    }, [open, fetchWeeklyActivities, fetchUsers, fetchContentSteps]);

    const updateStepAssignee = (type: string, userId: string) => {
        setContentSteps(current =>
            current.map(step =>
                step.type === type ? { ...step, assignedToId: userId } : step
            )
        );
    };

    const handleUseMultipleStepsChange = (useMultiple: boolean) => {
        setUseMultipleSteps(useMultiple);

        if (!useMultiple && assignedToId) {
            setContentSteps(current =>
                current.map(step => ({
                    ...step,
                    assignedToId: assignedToId || ''
                }))
            );
        }
    };

    const hasStepsChanged = () => {
        if (!useMultipleSteps) {
            return false;
        }

        if (!originalContentSteps || originalContentSteps.length === 0) {
            return false;
        }

        if (contentSteps.length !== originalContentSteps.length) {
            return true;
        }

        for (let i = 0; i < contentSteps.length; i++) {
            const currentStep = contentSteps[i];
            const originalStep = originalContentSteps.find(step => step.type === currentStep.type);

            if (!originalStep) {
                return true;
            }

            const normalizeValue = (value: string | null | undefined) => {
                return value === null || value === undefined || value === '' ? null : value;
            };

            const currentAssignee = normalizeValue(currentStep.assignedToId);
            const originalAssignee = normalizeValue(originalStep.assignedToId);

            if (currentAssignee !== originalAssignee) {
                return true;
            }
        }

        return false;
    };

    const getStepLabel = (type: string) => {
        const option = stepTypeOptions.find(opt => opt.value === type);
        return option ? option.label : type;
    };

    const handleSubmit = async (e: { preventDefault: () => void; }) => {
        e.preventDefault();
        setIsLoading(true);

        try {
            if (!activityDate || !contentType || !destination) {
                toast.error("Os campos Data, Tipo e Destino são obrigatórios");
                setIsLoading(false);
                return;
            }

            const formattedDate = (() => {
                const [year, month, day] = activityDate.split('-').map(Number);
                return new Date(Date.UTC(year, month - 1, day, 12, 0, 0)).toISOString();
            })();

            const updateData: ContentUpdateData = {
                activityDate: formattedDate,
                channel: selectedChannels.join(', '),
                contentType,
                destination,
                details,
                copywriting,
                reference,
                caption,
                priority,
                status,
                urlStructuringFeed,
                assignedToId
            };

            if (contentType === "carrossel" && destination === "Story") {
                updateData.carouselImagesCount = carouselImagesCount;
            } else {
                updateData.carouselImagesCount = null;
            }

            if (selectedWeeklyActivityId !== content.weeklyActivityId) {
                updateData.weeklyActivityId = selectedWeeklyActivityId;
                setWeekChanged(true);
            }

            const response = await fetch(`/api/contents/${content.id}`, {
                method: "PUT",
                headers: {
                    "Content-Type": "application/json",
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                },
                body: JSON.stringify(updateData),
            });

            if (!response.ok) {
                const errorText = await response.text();

                let errorMessage = "Falha ao atualizar conteúdo";
                try {
                    const errorData = JSON.parse(errorText);
                    errorMessage = errorData.message || errorMessage;
                } catch { }

                throw new Error(errorMessage);
            }

            const responseText = await response.text();

            try {
                JSON.parse(responseText);
            } catch {
                throw new Error("Erro ao processar resposta do servidor");
            }

            if (assignedToId !== content.assignedToId) {
                const assignResponse = await fetch(`/api/contents/${content.id}/assign`, {
                    method: 'PATCH',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ userId: assignedToId }),
                });

                if (!assignResponse.ok) {
                    console.warn('Falha ao enviar notificação de atribuição:', await assignResponse.text());
                }
            }

            if (useMultipleSteps) {
                const validSteps = contentSteps.filter(step => step.assignedToId);

                if (hasStepsChanged() && validSteps.length > 0) {
                    await fetch(`/api/contents/${content.id}/assign`, {
                        method: 'PATCH',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ userId: null }),
                    });

                    const stepsResponse = await fetch('/api/content-steps', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contentId: content.id,
                            steps: validSteps
                        }),
                    });

                    if (!stepsResponse.ok) {
                        console.error('Erro ao salvar etapas:', await stepsResponse.text());
                    }
                }
            } else {
                if (originalContentSteps && originalContentSteps.length > 0) {
                    await fetch('/api/content-steps', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                            contentId: content.id,
                            steps: []
                        }),
                    });
                }
            }

            const successMessage = weekChanged
                ? "Conteúdo atualizado e movido para outra semana com sucesso!"
                : "Conteúdo atualizado com sucesso!";

            toast.success(successMessage);
            try {
                const storageKey = content ? `edit_plan_caption_${content.id}` : null;
                if (storageKey) sessionStorage.removeItem(storageKey);
            } catch {
            }

            setOpen(false);

            const verifyResponse = await fetch(`/api/contents/${content.id}?t=${Date.now()}`, {
                headers: {
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                }
            });

            if (verifyResponse.ok) {
                await verifyResponse.json();
            }

            if (onSuccess) {
                setTimeout(() => {
                    onSuccess();
                }, 500);
            }
        } catch (error) {
            toast.error(error instanceof Error ? error.message : "Erro ao atualizar conteúdo");
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Dialog open={open} onOpenChange={setOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" size="icon" title="Editar atividade" disabled={disabled}>
                    <FilePenLine />
                </Button>
            </DialogTrigger>
            <DialogContent className="max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>
                        Editar atividade
                        <Badge className="ml-2" variant="secondary">
                            {new Date(content.activityDate).toLocaleDateString("pt-BR")}
                        </Badge>
                    </DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-4">
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div className="space-y-2">
                            <Label htmlFor="activityDate">Data</Label>
                            <Input
                                id="activityDate"
                                type="date"
                                value={activityDate}
                                onChange={(e) => setActivityDate(e.target.value)}
                                required
                            />
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="contentType">Tipo</Label>
                            <Select
                                onValueChange={setContentType}
                                value={contentType}

                                required
                            >
                                <SelectTrigger onFocus={(e) => e.stopPropagation()}>
                                    <SelectValue placeholder="Selecione o tipo" />
                                </SelectTrigger>
                                <SelectContent>
                                    {contentTypeOptions.map(option => (
                                        <SelectItem key={option.value} value={option.value}>
                                            {option.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="space-y-2 mt-3">
                        <Label htmlFor="weeklyActivity">Semana</Label>
                        <Select
                            onValueChange={setSelectedWeeklyActivityId}
                            value={selectedWeeklyActivityId}
                            disabled={loadingWeeklyActivities}
                        >
                            <SelectTrigger onFocus={(e) => e.stopPropagation()}>
                                <SelectValue placeholder={loadingWeeklyActivities ? <Ellipsis /> : "Selecione a semana"} />
                            </SelectTrigger>
                            <SelectContent>
                                {groupedWeeklyActivities.length > 0 ? (
                                    groupedWeeklyActivities.map(activity => (
                                        <SelectItem key={activity.id} value={activity.id}>
                                            Semana {activity.week}
                                        </SelectItem>
                                    ))
                                ) : (
                                    <div className="p-2 text-center text-sm text-muted-foreground">
                                        Nenhuma semana disponível
                                    </div>
                                )}
                            </SelectContent>
                        </Select>
                        {selectedWeeklyActivityId !== content.weeklyActivityId && (
                            <Alert className="mt-2">
                                <AlertCircle className="h-4 w-4" />
                                <AlertDescription>
                                    Ao mudar a semana, o conteúdo será movido para o card da semana selecionada.
                                </AlertDescription>
                            </Alert>
                        )}
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                        <div className="space-y-2">
                            <Label htmlFor="channel">Canal</Label>
                            <div className="border rounded-md p-2">
                                <Select
                                    onValueChange={(value) => {
                                        if (!selectedChannels.includes(value)) {
                                            setSelectedChannels([...selectedChannels, value]);
                                        }
                                    }}
                                    value="channel_selector"
                                >
                                    <SelectTrigger className="w-full mb-2">
                                        <SelectValue placeholder="Adicionar canal" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="channel_selector">Selecione um canal</SelectItem>
                                        {channelOptions
                                            .filter(option => !selectedChannels.includes(option.value))
                                            .map(option => (
                                                <SelectItem key={option.value} value={option.value}>
                                                    {option.label}
                                                </SelectItem>
                                            ))}
                                    </SelectContent>
                                </Select>

                                <div className="flex flex-wrap gap-1 mt-2">
                                    {selectedChannels.map(channel => (
                                        <Badge
                                            key={channel}
                                            variant="secondary"
                                            className="flex items-center gap-1"
                                        >
                                            {channel}
                                            <button
                                                type="button"
                                                onClick={() => setSelectedChannels(selectedChannels.filter(c => c !== channel))}
                                                className="ml-1 hover:text-red-500 focus:outline-none"
                                                aria-label={`Remover ${channel}`}
                                            >
                                                ×
                                            </button>
                                        </Badge>
                                    ))}
                                    {selectedChannels.length === 0 && (
                                        <p className="text-sm text-gray-500">Nenhum canal selecionado</p>
                                    )}
                                </div>
                            </div>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="destination">Destino</Label>
                            <Select
                                onValueChange={setDestination}
                                value={destination}

                                required
                            >
                                <SelectTrigger onFocus={(e) => e.stopPropagation()}>
                                    <SelectValue placeholder="Selecione o destino" />
                                </SelectTrigger>
                                <SelectContent>
                                    {destinationOptions.map(option => (
                                        <SelectItem key={option.value} value={option.value}>
                                            {option.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {contentType === "carrossel" && destination === "Story" && (
                        <div className="space-y-2 mt-3">
                            <Label htmlFor="carouselImagesCount">Quantidade de imagens</Label>
                            <Input
                                id="carouselImagesCount"
                                type="number"
                                min="1"
                                max="10"
                                value={carouselImagesCount}
                                onChange={(e) => setCarouselImagesCount(parseInt(e.target.value) || 1)}
                                className="w-full"
                            />
                            <p className="text-xs text-muted-foreground">
                                Cada imagem será contabilizada como um Story no limite mensal
                            </p>
                        </div>
                    )}

                    <div className="space-y-2 mt-3">
                        <Label htmlFor="details">Assunto</Label>
                        <Input
                            id="details"
                            placeholder="Digite o assunto"
                            value={details}
                            onChange={(e) => setDetails(e.target.value)}
                        />
                    </div>

                    <div className="space-y-2 mt-3">
                        <Label htmlFor="reference">Referência</Label>
                        <Input
                            id="reference"
                            placeholder="Digite a referência"
                            value={reference}
                            onChange={(e) => setReference(e.target.value)}
                        />
                    </div>

                    <div className="space-y-2 mt-3">
                        <div className="flex justify-between items-start">
                            <div>
                                <Label htmlFor="originalCaption">Legenda original (para otimizar com IA)</Label>
                                <p className="text-xs text-muted-foreground">Se você não deseja otimizar, preencha a legenda final manualmente.</p>
                            </div>
                            <div className="text-xs text-muted-foreground ml-2">{originalCaption.length}/523</div>
                        </div>

                        <Textarea
                            id="originalCaption"
                            placeholder="Cole aqui a legenda se deseja otimizar com IA"
                            value={originalCaption}
                            onChange={(e) => setOriginalCaption(e.target.value)}
                            rows={8}
                        />
                        <div>
                            <div className="flex justify-end">
                                <Button
                                    type="button"
                                    size="sm"
                                    onClick={async () => {
                                        if (!originalCaption?.trim()) {
                                            toast.error('Preencha a legenda original primeiro');
                                            return;
                                        }
                                        setIsLoading(true);
                                        try {
                                            const response = await fetch('/api/generate-caption', {
                                                method: 'POST',
                                                headers: { 'Content-Type': 'application/json' },
                                                body: JSON.stringify({ originalCaption: originalCaption.trim() })
                                            });
                                            const data = await response.json();
                                            if (response.ok) {
                                                setCaption(data.caption);
                                                toast.success('Legenda otimizada com sucesso!');
                                            } else {
                                                toast.error(data.error || 'Erro ao otimizar legenda');
                                            }
                                        } catch (error) {
                                            toast.error('Erro ao otimizar legenda');
                                            console.error('Erro ao otimizar legenda:', error);
                                        } finally {
                                            setIsLoading(false);
                                        }
                                    }}
                                    disabled={isLoading || originalCaption.length < 523}
                                    className="flex items-center gap-1 bg-gradient-to-r from-green-600 to-orange-600 text-white hover:from-green-700 hover:to-orange-700"
                                >
                                    <Sparkles size={14} />
                                    Otimizar com IA
                                </Button>
                            </div>

                            {originalCaption.length < 523 && (
                                <p className="text-xs text-right text-amber-600 mt-1">
                                    Atenção: A legenda original só deve ser otimizada se estiver acima do limite de caracteres (523). Se estiver abaixo, preencha a legenda final manualmente.
                                </p>
                            )}
                        </div>
                    </div>

                    <div className="space-y-2 mt-3">
                        <div className="flex justify-between items-center">
                            <Label htmlFor="caption">Legenda final</Label>
                            <span className={`text-xs ${caption.length > 523 ? 'text-red-500' : 'text-gray-500'}`}>
                                {caption.length}/523
                            </span>
                        </div>
                        <Textarea
                            id="caption"
                            placeholder="Legenda otimizada aparecerá aqui"
                            value={caption}
                            onChange={(e) => {
                                const value = e.target.value;
                                if (value.length <= 523) {
                                    setCaption(value);
                                }
                            }}
                            rows={8}
                        />
                        {caption.length > 500 && caption.length <= 523 && (
                            <p className="text-xs text-amber-600">
                                Atenção: Você está próximo do limite de caracteres
                            </p>
                        )}

                        {caption.length > 523 && (
                            <p className="text-xs text-red-500">
                                Atenção: Você excedeu o limite de caracteres
                            </p>
                        )}
                    </div>

                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 mt-3">
                        <div className="space-y-2">
                            <Label htmlFor="priority">Prioridade</Label>
                            <Select
                                onValueChange={setPriority}
                                value={priority}
                            >
                                <SelectTrigger onFocus={(e) => e.stopPropagation()}>
                                    <SelectValue placeholder="Selecione a prioridade" />
                                </SelectTrigger>
                                <SelectContent>
                                    {priorityOptions.map(option => (
                                        <SelectItem key={option.value} value={option.value}>
                                            {option.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div className="space-y-2">
                            <Label htmlFor="status">Status</Label>
                            <Select
                                onValueChange={setStatus}
                                value={status}
                            >
                                <SelectTrigger onFocus={(e) => e.stopPropagation()}>
                                    <SelectValue placeholder="Selecione o status" />
                                </SelectTrigger>
                                <SelectContent>
                                    {statusOptions.map(option => (
                                        <SelectItem key={option.value} value={option.value}>
                                            {option.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    <div className="space-y-2 mt-3">
                        <Label htmlFor="assignedToId">Responsável principal</Label>
                        <Select
                            value={assignedToId || 'none'}
                            onValueChange={(value) => {
                                setAssignedToId(value === 'none' ? null : value);
                                if (!useMultipleSteps && value !== 'none') {
                                    setContentSteps(current =>
                                        current.map(step => ({
                                            ...step,
                                            assignedToId: value
                                        }))
                                    );
                                }
                            }}
                        >
                            <SelectTrigger onFocus={(e) => e.stopPropagation()}>
                                <SelectValue placeholder="Selecione um responsável" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="none">
                                    Nenhum responsável
                                </SelectItem>
                                {usersWithRole
                                    .sort((a, b) => {
                                        const nameA = (a.name || a.email).toLowerCase();
                                        const nameB = (b.name || b.email).toLowerCase();
                                        return nameA.localeCompare(nameB);
                                    })
                                    .map(user => (
                                        <SelectItem key={user.id} value={user.id}>
                                            {user.name}
                                        </SelectItem>
                                    ))}
                            </SelectContent>
                        </Select>
                        {useMultipleSteps && assignedToId && (
                            <p className="text-xs text-amber-600 mt-1">
                                Atenção: Como você está usando etapas múltiplas,
                                o responsável principal será substituído pelos responsáveis das etapas.
                            </p>
                        )}
                    </div>

                    <div className="space-y-2 mt-3">
                        <Label htmlFor="copywriting">Copy</Label>
                        <Textarea
                            id="copywriting"
                            placeholder="Digite o copy"
                            value={copywriting}
                            onChange={(e) => setCopywriting(e.target.value)}
                            rows={3}
                        />
                    </div>

                    <div className="space-y-2 mt-3">
                        <Label htmlFor="urlStructuringFeed">URLs</Label>
                        <p className="text-sm text-muted-foreground">
                            Para adicionar ou editar URLs, vá até a página de <Link href={`/deliveries/${content.clientId}`} className="text-blue-600 hover:underline">Entregas</Link> do cliente.
                        </p>
                    </div>

                    {hasLoadedSteps && (
                        <div className="space-y-3 mt-3 pt-3 border-t border-gray-200 dark:border-gray-700">
                            <Label>Responsáveis pelas etapas</Label>

                            <div className="flex items-center justify-between">
                                <span className="text-sm text-gray-600 dark:text-gray-300">
                                    Atribuir responsáveis por etapas
                                </span>
                                <Switch
                                    id="useMultipleSteps"
                                    checked={useMultipleSteps}
                                    onCheckedChange={handleUseMultipleStepsChange}
                                />
                            </div>

                            {useMultipleSteps ? (
                                <div className="space-y-2">
                                    {contentSteps.map((step, index) => (
                                        <div key={index} className="flex items-center justify-between gap-2">
                                            <span className="text-sm font-medium">{getStepLabel(step.type)}:</span>
                                            <Select
                                                value={step.assignedToId}
                                                onValueChange={(value) => updateStepAssignee(step.type, value)}
                                            >
                                                <SelectTrigger className="w-[200px]" onFocus={(e) => e.stopPropagation()}>
                                                    <SelectValue placeholder="Selecionar responsável" />
                                                </SelectTrigger>
                                                <SelectContent>
                                                    {usersWithRole.map(user => (
                                                        <SelectItem key={user.id} value={user.id}>
                                                            {user.name}
                                                        </SelectItem>
                                                    ))}
                                                </SelectContent>
                                            </Select>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <div className="opacity-50">
                                    <span className="text-sm">
                                        As etapas múltiplas estão desativadas. Este conteúdo usa um responsável único para todas as etapas.
                                    </span>
                                </div>
                            )}
                        </div>
                    )}

                    <div className="flex justify-end flex-col space-y-2 sm:flex-row sm:space-y-0 sm:space-x-2 mt-4 w-full">
                        <Button
                            variant="outline"
                            type="button"
                            onClick={() => setOpen(false)}
                            className="w-full sm:w-auto"
                        >
                            Cancelar
                        </Button>
                        <Button
                            type="submit"
                            disabled={isLoading}
                            className="w-full sm:w-auto flex justify-center items-center"
                        >
                            {isLoading ? <Ellipsis /> : null}
                            Salvar alterações
                        </Button>
                    </div>
                </form>
            </DialogContent>
        </Dialog>
    );
};