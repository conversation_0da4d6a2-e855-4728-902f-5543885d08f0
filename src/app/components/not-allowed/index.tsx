import { MoveLeft, OctagonXIcon } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { useRouter } from "next/navigation";

type NotAllowedProps = {
    page: string;
}

export const NotAllowed = ({ page }: NotAllowedProps) => {
    const router = useRouter();

    return (
        <>
            <Button
                className="mt-2"
                variant="outline"
                onClick={() => router.push(`/${page}`)}
            >
                <MoveLeft className="h-4 w-4" />
            </Button>
            <div className="text-center flex gap-2 flex-col items-center justify-center h-[calc(100vh-20rem)] border border-zinc-200 dark:border-zinc-800 rounded-lg p-4 mt-4 space-y-2">
                <OctagonXIcon size={17} />
                <p className="border-t border-zinc-200 pt-4 text-sm">
                    Você não tem permissão para acessar esta página ou seção. Por favor, entre em
                    contato com o administrador do sistema.
                </p>
            </div>
        </>
    );
}