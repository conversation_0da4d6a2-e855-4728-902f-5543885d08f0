"use client";

import { useState, useRef, useEffect } from 'react';
import { useSession } from 'next-auth/react';
import { Button } from '@/app/components/ui/button';
import { uploadToS3 } from '@/lib/hooks/useS3Upload';
import { toast } from 'sonner';
import { CloudUpload } from 'lucide-react';

interface Props {
    folder?: string;
    onUploaded?: (result: { key: string; bucket: string; publicUrl?: string; presignedUrl?: string }) => void;
    accept?: string;
}

export function S3UploadButton({ folder, onUploaded, accept }: Props) {
    const [progress, setProgress] = useState<number | null>(null);
    const [loading, setLoading] = useState(false);
    const [userDeveloper, setUserDeveloper] = useState<string | null>(null);

    const { data: session, status } = useSession();

    useEffect(() => {
        const getUser = async () => {
            if (!session?.user?.email) {
                setUserDeveloper(null);
                return;
            }
            try {
                const response = await fetch(`/api/users/${session.user.email}`);
                if (!response.ok) {
                    setUserDeveloper(null);
                    return;
                }
                const data = await response.json();
                setUserDeveloper(data.role === "DEVELOPER" ? "DEVELOPER" : null);
            } catch {
                setUserDeveloper(null);
            }
        };

        getUser();
    }, [session?.user?.email]);

    const handleFile = async (file?: File) => {
        if (!file) return;
        setLoading(true);
        setProgress(0);
        try {
            const result = await uploadToS3(file, {
                folder,
                onProgress: (p) => setProgress(p),
            });
            toast.success('Upload concluído');
            onUploaded?.(result as { key: string; bucket: string; publicUrl?: string; presignedUrl?: string });
        } catch (err: unknown) {
            console.error('Upload error', err);
            if (err && typeof err === 'object') {
                const e = err as { message?: string; status?: number; responseText?: string };
                if (e.status === 403) {
                    console.error('Upload 403 details:', e.responseText);
                    toast.error(`Upload negado (403). Verifique credenciais/CORS. Detalhe: ${e.responseText?.slice(0, 200)}`);
                } else {
                    toast.error(e.message ?? 'Erro no upload');
                }
            } else {
                toast.error('Erro no upload');
            }
        } finally {
            setLoading(false);
            setProgress(null);
        }
    };

    const inputRef = useRef<HTMLInputElement | null>(null);

    return (
        <div>
            <input
                ref={inputRef}
                type="file"
                accept={accept}
                style={{ position: 'absolute', opacity: 0, width: 0, height: 0, pointerEvents: 'none' }}
                onChange={(e) => {
                    const file = e.target.files?.[0];
                    handleFile(file);
                    e.currentTarget.value = '';
                }}
            />

            <Button
                variant="outline"
                disabled={loading || status === 'loading' || !userDeveloper}
                onClick={() => inputRef.current?.click()}
            >
                <CloudUpload /> {loading ? `Enviando ${progress ?? 0}%` : 'Enviar arquivo'}
            </Button>
            <p className='text-xs text-right text-muted-foreground mt-1'>
                Em breve
            </p>
        </div>
    );
}

export default S3UploadButton;
