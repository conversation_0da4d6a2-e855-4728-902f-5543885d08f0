"use client"

import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from './ui/dialog';
import { Input } from './ui/input';
import { Label } from './ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from './ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from './ui/table';
import { toast } from 'sonner';
import { CalendarDays, Trash } from 'lucide-react';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';

interface User {
    workArrangement: string;
    id: string;
    name: string;
    email: string;
}

interface ExcusedDay {
    id: string;
    userId: string;
    date: string;
    reason: string;
    user: {
        name: string;
        email: string;
    };
}

export function ExcusedDaysManager() {
    const [isOpen, setIsOpen] = useState(false);
    const [users, setUsers] = useState<User[]>([]);
    const [excusedDays, setExcusedDays] = useState<ExcusedDay[]>([]);
    const [selectedUserId, setSelectedUserId] = useState('');
    const [selectedDate, setSelectedDate] = useState('');
    const [reason, setReason] = useState('');
    const [type, setType] = useState<'ABONO' | 'FALTA'>('ABONO');
    const [isLoading, setIsLoading] = useState(false);

    const usersOnSite = users.filter(user => user.workArrangement === "ON_SITE");

    useEffect(() => {
        if (isOpen) {
            fetchUsers();
            fetchExcusedDays();
        }
    }, [isOpen]);

    const fetchUsers = async () => {
        try {
            const response = await fetch('/api/users');
            if (response.ok) {
                const data = await response.json();
                setUsers(data.filter((user: User) => user.name));
            }
        } catch (error) {
            console.error('Erro ao carregar usuários:', error);
        }
    };

    const fetchExcusedDays = async () => {
        try {
            const response = await fetch('/api/admin/excused-days');
            if (response.ok) {
                const data = await response.json();
                setExcusedDays(data);
            }
        } catch (error) {
            console.error('Erro ao carregar dias abonados:', error);
        }
    };

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        if (!selectedUserId || !selectedDate || !reason.trim()) {
            toast.error('Preencha todos os campos');
            return;
        }

        setIsLoading(true);
        try {
            const response = await fetch('/api/admin/excused-days', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    userId: selectedUserId,
                    date: selectedDate,
                    reason: reason.trim(),
                    type
                })
            });

            if (response.ok) {
                toast.success(type === 'FALTA' ? 'Falta cadastrada com sucesso!' : 'Dia abonado com sucesso!');
                setSelectedUserId('');
                setSelectedDate('');
                setReason('');
                    setType('ABONO');
                fetchExcusedDays();
            } else {
                    const err = await response.json().catch(() => ({}));
                    toast.error(err.error || 'Erro ao abonar dia');
            }
        } catch (error) {
            toast.error('Erro ao abonar dia');
            console.error(error)
        } finally {
            setIsLoading(false);
        }
    };

    const handleDelete = async (id: string) => {
        try {
            const response = await fetch(`/api/admin/excused-days/${id}`, {
                method: 'DELETE'
            });

            if (response.ok) {
                toast.success('Abono removido com sucesso!');
                fetchExcusedDays();
            } else {
                toast.error('Erro ao remover abono');
            }
        } catch (error) {
            toast.error('Erro ao remover abono');
            console.error(error)
        }
    };

    const formatDate = (dateString: string) => {
        return format(new Date(dateString), 'dd/MM/yyyy', { locale: ptBR });
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button variant="outline" className="flex gap-2">
                    <CalendarDays size={16} />
                    Gerenciar abonos e faltas
                </Button>
            </DialogTrigger>
            <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
                <DialogHeader>
                    <DialogTitle>Gerenciar dias abonados</DialogTitle>
                </DialogHeader>

                <form onSubmit={handleSubmit} className="space-y-4 mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <div>
                            <Label htmlFor="user">Colaborador</Label>
                            <Select value={selectedUserId} onValueChange={setSelectedUserId}>
                                <SelectTrigger>
                                    <SelectValue placeholder="Selecione um colaborador" />
                                </SelectTrigger>
                                <SelectContent>
                                    {usersOnSite.map(user => (
                                        <SelectItem key={user.id} value={user.id}>
                                            {user.name}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label htmlFor="date">Data</Label>
                            <Input
                                id="date"
                                type="date"
                                value={selectedDate}
                                onChange={(e) => setSelectedDate(e.target.value)}
                            />
                        </div>

                        <div>
                            <Label htmlFor="type">Tipo</Label>
                            <Select value={type} onValueChange={(v) => setType(v as 'ABONO' | 'FALTA')}>
                                <SelectTrigger>
                                    <SelectValue />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="ABONO">Abono</SelectItem>
                                    <SelectItem value="FALTA">Falta</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>

                        <div>
                            <Label htmlFor="reason">Motivo</Label>
                            <Input
                                id="reason"
                                placeholder="Ex: Feriado, Licença médica..."
                                value={reason}
                                onChange={(e) => setReason(e.target.value)}
                            />
                        </div>
                    </div>

                    <Button type="submit" disabled={isLoading}>
                        {isLoading ? 'Abonando' : 'Abonar dia'}
                    </Button>
                </form>

                <div>
                    <h3 className="text-lg font-semibold mb-3">Dias abonados</h3>
                    <Table>
                        <TableHeader>
                            <TableRow>
                                <TableHead>Data</TableHead>
                                <TableHead>Tipo</TableHead>
                                <TableHead>Colaborador</TableHead>
                                <TableHead>Motivo</TableHead>
                                <TableHead>Ações</TableHead>
                            </TableRow>
                        </TableHeader>
                        <TableBody>
                            {excusedDays.map((excusedDay) => (
                                <TableRow key={excusedDay.id}>
                                    <TableCell>{formatDate(excusedDay.date)}</TableCell>
                                    <TableCell>
                                        {excusedDay.reason && excusedDay.reason.startsWith('FALTA|') ? (
                                            <span className="text-red-600 font-medium">Falta</span>
                                        ) : (
                                            <span className="text-green-600 font-medium">Abono</span>
                                        )}
                                    </TableCell>
                                    <TableCell>
                                        <div>
                                            <div className="font-medium">{excusedDay.user.name}</div>
                                            <div className="text-sm text-muted-foreground">{excusedDay.user.email}</div>
                                        </div>
                                    </TableCell>
                                    <TableCell>{excusedDay.reason && excusedDay.reason.startsWith('FALTA|') ? excusedDay.reason.split('|').slice(1).join('|') : excusedDay.reason}</TableCell>
                                    <TableCell>
                                        <Button
                                            variant="outline"
                                            size="icon"
                                            onClick={() => handleDelete(excusedDay.id)}
                                            title="Remover abono"
                                        >
                                            <Trash size={16} color="red" />
                                        </Button>
                                    </TableCell>
                                </TableRow>
                            ))}
                            {excusedDays.length === 0 && (
                                <TableRow>
                                    <TableCell colSpan={4} className="text-center py-6">
                                        Nenhum dia abonado
                                    </TableCell>
                                </TableRow>
                            )}
                        </TableBody>
                    </Table>
                </div>
            </DialogContent>
        </Dialog>
    );
}