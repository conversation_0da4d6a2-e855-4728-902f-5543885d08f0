import { useState, useEffect } from "react";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { Separator } from "@/app/components/ui/separator";

interface WelcomeAlertProps {
    userId: string;
    userName?: string;
    version?: string;
}

export function WelcomeAlert({ userId, userName, version = "1.0" }: WelcomeAlertProps) {
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const seenVersion = localStorage.getItem(`welcome-seen-${userId}`);

        if (!seenVersion || seenVersion !== version) {
            setIsOpen(true);
        }
    }, [userId, version]);

    const handleClose = () => {
        localStorage.setItem(`welcome-seen-${userId}`, version);
        setIsOpen(false);
    };

    return (

        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>
                        Instruções para o módulo de entregas!
                    </DialogTitle>
                    <DialogDescription className="pt-2">
                        Olá <span className="font-medium">{userName || "usuário"}</span>, confira as últimas atualizações!
                    </DialogDescription>
                </DialogHeader>
                <Separator />
                <div className="text-sm text-zinc-700 dark:text-zinc-300 space-y-2">
                    <p>
                        📝 Temporariamente, você só poderá gerar o PDF de entregas em telas maiores. Sugerimos usar os navegadores Chrome e Firefox para uma melhor experiência.

                    </p>

                    <Separator className="my-2" />

                    <p>
                        Estamos trabalhando para trazer essa funcionalidade para dispositivos móveis em breve. Agradecemos sua paciência !
                    </p>
                </div>

                <DialogFooter>
                    <Button onClick={handleClose} className="w-full sm:w-auto">
                        Entendi
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}