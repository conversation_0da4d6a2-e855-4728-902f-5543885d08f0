/*
*
*
* Adicionar ao dashboard na linha 325 *
*
*
*/

import { useState, useEffect } from "react";
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogDescription,
    DialogFooter
} from "@/app/components/ui/dialog";
import { Button } from "@/app/components/ui/button";
import { Separator } from "@/app/components/ui/separator";
import Image from "next/image";

interface WelcomeAlertProps {
    userId: string;
    userName?: string;
    version?: string;
}

export function WelcomeAlert({ userId, userName, version = "1.0" }: WelcomeAlertProps) {
    const [isOpen, setIsOpen] = useState(false);

    useEffect(() => {
        const seenVersion = localStorage.getItem(`welcome-seen-${userId}`);

        if (!seenVersion || seenVersion !== version) {
            setIsOpen(true);
        }
    }, [userId, version]);

    const handleClose = () => {
        localStorage.setItem(`welcome-seen-${userId}`, version);
        setIsOpen(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogContent className="sm:max-w-md">
                <DialogHeader>
                    <DialogTitle>
                        ✨ Primeiro recurso de IA está aqui!
                    </DialogTitle>
                    <DialogDescription className="pt-2">
                        Olá <span className="font-medium">{userName || "usuário"}</span>, confira as últimas atualizações!
                    </DialogDescription>
                </DialogHeader>
                <Separator />
                <div className="text-sm text-zinc-700 dark:text-zinc-300 space-y-2">
                    <p>
                        Agora você pode otimizar suas legendas usando IA! Basta clicar no botão &quot;Otimizar com IA&quot; ao editar ou criar uma atividade.
                    </p>
                    <Image
                        src="/images/otimizar-legendas-ia.png"
                        alt="Otimizar legendas com IA"
                        width={500}
                        height={300}
                        className="w-full h-auto rounded-lg border"
                    />
                    <p>
                        Esse recurso adapta automaticamente suas legendas para o limite de 523 caracteres, mantendo o conteúdo essencial e o tom original da mensagem.
                    </p>
                </div>

                <DialogFooter>
                    <Button onClick={handleClose} className="w-full sm:w-auto">
                        Entendi
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}