"use client";

import { useEffect, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, DialogTitle } from "@/app/components/ui/dialog";
import { Progress } from "@/app/components/ui/progress";
import { CheckCircle, Ellipsis } from "lucide-react";

interface PDFProgressModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentStep: number;
  totalSteps: number;
  currentStepName: string;
  isComplete: boolean;
  error?: string;
}

const steps = [
  "Preparando ambiente",
  "Carregando dados",
  "Processando conteúdo",
  "Renderizando imagens",
  "Gerando PDF",
  "Finalizando"
];

export const PDFProgressModal = ({
  isOpen,
  onClose,
  currentStep,
  totalSteps,
  currentStepName,
  isComplete,
  error
}: PDFProgressModalProps) => {
  const [progress, setProgress] = useState(0);

  useEffect(() => {
    if (currentStep > 0) {
      const newProgress = Math.round((currentStep / totalSteps) * 100);
      setProgress(newProgress);
    }
  }, [currentStep, totalSteps]);

  const getStepIcon = (stepIndex: number) => {
    if (stepIndex < currentStep) {
      return <CheckCircle className="w-5 h-5 text-primary3" />;
    } else if (stepIndex === currentStep) {
      return <Ellipsis className="w-5 h-5 animate-pulse" />;
    } else {
      return <Ellipsis className="w-5 h-5 text-gray-300" />;
    }
  };

  const getStepTextColor = (stepIndex: number) => {
    if (stepIndex < currentStep) {
      return "text-primary3 font-medium text-sm";
    } else if (stepIndex === currentStep) {
      return  "font-medium text-sm";
    } else {
      return "text-gray-400 text-sm";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {error ? (
              <>
                <div className="w-5 h-5 bg-red-500 rounded-full" />
                Erro na exportação
              </>
            ) : isComplete ? (
              <>
                <CheckCircle className="w-5 h-5 text-primary3" />
                PDF gerado com sucesso!
              </>
            ) : (
              <>
                <Ellipsis className="w-5 h-5 animate-pulse" />
                Gerando PDF
              </>
            )}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {error ? (
            <div className="text-center py-4">
              <div className="text-red-600 mb-2">Ocorreu um erro:</div>
              <div className="text-sm text-gray-600 bg-red-50 p-3 rounded-lg">
                {error}
              </div>
            </div>
          ) : (
            <>
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Progresso</span>
                  <span className="text-gray-600">{progress}%</span>
                </div>
                <Progress value={progress} className="h-2" />
              </div>

              <div className="text-center">
                <div className="text-sm text-gray-600 mb-1">Etapa atual:</div>
                <div className="font-medium text-sm">
                  {currentStepName || steps[currentStep] || <Ellipsis className="w-5 h-5 animate-pulse" />}
                </div>
              </div>

              <div className="space-y-3">
                <div className="text-sm font-medium text-gray-700 mb-2">Etapas:</div>
                {steps.map((step, index) => (
                  <div key={index} className="flex items-center gap-3">
                    {getStepIcon(index)}
                    <span className={getStepTextColor(index)}>
                      {step}
                    </span>
                  </div>
                ))}
              </div>

              {isComplete && (
                <div className="text-center py-2">
                  <div className="text-primary3 font-medium">
                    PDF gerado e download iniciado!
                  </div>
                </div>
              )}
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};
