import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { toast } from 'sonner';
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogTrigger,
    DialogFooter,
    DialogDescription,
} from './ui/dialog';
import { Ellipsis, PlusCircle, Replace } from 'lucide-react';

export function AddContentReview({
    contentId,
    currentReview = '',
    onReviewUpdate,
    onStatusUpdate,
    type = 'content',
    size = 'default',
    label = '',
    witdh = '',
    isClient = false,
    disabled = false
    , reviewedBy
}: {
    contentId: string;
    currentReview?: string;
    onReviewUpdate?: (newReview: string) => void;
    onStatusUpdate?: (newStatus: string) => void;
    type?: 'content' | 'general';
    size?: 'icon' | 'sm' | 'default';
    label?: string;
    witdh?: string;
    isClient?: boolean;
    disabled?: boolean;
    reviewedBy?: { id?: string; name?: string; email?: string; image?: string };
}) {
    const [reviewText, setReviewText] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        setReviewText(currentReview || '');
    }, [currentReview]);

    const handleSave = async () => {
        try {
            setIsLoading(true);
            const apiUrl = type === 'general'
                ? `/api/general-demands/${contentId}`
                : `/api/contents/${contentId}`;

            const requestData: { review: string; status?: string } = {
                review: reviewText
            };

            if (reviewText.trim() !== '') {
                requestData.status = 'alteração';
            }

            const response = await fetch(apiUrl, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData),
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Response error:', response.status, errorText);
                throw new Error(`HTTP error! status: ${response.status}`);
            }

            onReviewUpdate?.(reviewText);

            if (reviewText.trim() !== '') {
                onStatusUpdate?.('alteração');
                toast.success('Alteração solicitada com sucesso!');
            } else {
                toast.success('Alteração removida com sucesso!');
            }

            setIsOpen(false);
        } catch (error) {
            console.error('Error saving review:', error);
            toast.error('Erro ao salvar alteração. Tente novamente.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        setReviewText(currentReview);
        setIsOpen(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={disabled ? undefined : setIsOpen}>
            <DialogTrigger asChild disabled={disabled}>
                <div className="relative flex items-center w-full">
                    <Button
                        size={size}
                        variant="primary"
                        title={currentReview ? 'Editar solicitação de ajuste' : 'Solicitar ajuste'}
                        disabled={disabled}
                        className={`w-full ${witdh} border-red-300 dark:border-red-900`}
                        onClick={disabled ? (e) => e.preventDefault() : undefined}
                    >
                        {label ? (<span className='flex gap-2'><Replace /> {label}</span>) : (<PlusCircle size={16} />)}
                    </Button>
                    {currentReview && (
                        <span className="absolute top-0 right-0 mt-1 mr-1 bg-red-500 h-2 w-2 rounded-full">
                        </span>
                    )}
                </div>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>
                        {currentReview ? 'Editar solicitação de ajuste' : 'Solicitar ajuste'}
                    </DialogTitle>
                    {isClient ? (
                        <DialogDescription>
                            Faça uma solicitação geral em todo o conteúdo. Se quiser remover a solicitação, basta deixar o campo vazio e salvar.
                        </DialogDescription>
                    ) : (
                        <DialogDescription>
                            Ao adicionar uma alteração, o status da demanda será alterado para &quot;alteração&quot; e a alteração solicitada será enviada para o colaborador.
                        </DialogDescription>
                    )}
                </DialogHeader>
                <div className="grid gap-4">
                    <Textarea
                        placeholder="Digite a alteração aqui..."
                        value={reviewText}
                        onChange={(e) => setReviewText(e.target.value)}
                        className="min-h-[100px]"
                        rows={7}
                    />
                </div>
                {currentReview && reviewedBy && (
                    <div className="text-xs text-muted-foreground mt-1">Solicitado por {reviewedBy.name || 'Usuário anônimo'}</div>
                )}
                <DialogFooter>
                    <Button variant="outline" onClick={handleCancel}>
                        Cancelar
                    </Button>
                    <Button onClick={handleSave} disabled={isLoading}>
                        {isLoading ? <Ellipsis /> : null}
                        Salvar
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}