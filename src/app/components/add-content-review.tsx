import React, { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { Textarea } from './ui/textarea';
import { toast } from 'sonner';
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    <PERSON><PERSON>Title,
    <PERSON><PERSON>Trigger,
    DialogFooter,
} from './ui/dialog';
import { Ellipsis, PlusCircle } from 'lucide-react';

export function AddContentReview({
    contentId,
    currentReview = '',
    onReviewUpdate,
    onStatusUpdate,
    type = 'content',
}: {
    contentId: string;
    currentReview?: string;
    onReviewUpdate?: (newReview: string) => void;
    onStatusUpdate?: (newStatus: string) => void;
    type?: 'content' | 'general';
}) {
    const [reviewText, setReviewText] = useState('');
    const [isOpen, setIsOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        setReviewText(currentReview || '');
    }, [currentReview]);

    const handleSave = async () => {
        try {
            setIsLoading(true);
            const apiUrl = type === 'general'
                ? `/api/general-demands/${contentId}`
                : `/api/contents/${contentId}`;
            
            const requestData: { review: string; status?: string } = {
                review: reviewText
            };
            
            if (reviewText.trim() !== '') {
                requestData.status = 'alteração';
            }

            const response = await fetch(apiUrl, {
                method: 'PUT',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(requestData),
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Response error:', response.status, errorText);
                throw new Error(`HTTP error! status: ${response.status}`);
            }
            
            onReviewUpdate?.(reviewText);
            
            if (reviewText.trim() !== '') {
                onStatusUpdate?.('alteração');
                toast.success('Alteração solicitada com sucesso. O colaborador responsável foi notificado.');
            } else {
                toast.success('Alteração removida com sucesso.');
            }
            
            setIsOpen(false);
        } catch (error) {
            console.error('Error saving review:', error);
            toast.error('Erro ao salvar alteração. Tente novamente.');
        } finally {
            setIsLoading(false);
        }
    };

    const handleCancel = () => {
        setReviewText(currentReview);
        setIsOpen(false);
    };

    return (
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
            <DialogTrigger asChild>
                <Button
                    size="icon"
                    variant="outline"
                    title={currentReview ? 'Editar alteração' : 'Adicionar alteração'}
                    className={currentReview ? 'text-primary2 bg-orange-200 dark:bg-orange-950 hover:bg-orange-200 hover:text-black dark:hover:text-white' : ''}
                >
                    <PlusCircle size={16} />
                </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[425px]">
                <DialogHeader>
                    <DialogTitle>
                        {currentReview ? 'Editar alteração' : 'Adicionar alteração'}
                    </DialogTitle>
                    <p className='text-sm text-muted-foreground'>
                        Ao adicionar uma alteração, o status da demanda será alterado para &quot;alteração&quot; e a alteração solicitada será enviada para o colaborador.
                    </p>
                </DialogHeader>
                <div className="grid gap-4 pb-4">
                    <Textarea
                        placeholder="Digite a alteração aqui..."
                        value={reviewText}
                        onChange={(e) => setReviewText(e.target.value)}
                        className="min-h-[100px]"
                    />
                </div>
                <DialogFooter>
                    <Button variant="outline" onClick={handleCancel}>
                        Cancelar
                    </Button>
                    <Button onClick={handleSave} disabled={isLoading}>
                        {isLoading ? <Ellipsis /> : null}
                        Salvar
                    </Button>
                </DialogFooter>
            </DialogContent>
        </Dialog>
    );
}