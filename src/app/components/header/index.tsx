"use client"

import Image from 'next/image';
import { Bell, Calendar, Cog, FileClock, Grid3X3, Home, LayoutDashboard, ListTodo, LogOut, Menu, MonitorCog, PanelTop, User, Users } from 'lucide-react';
import { Button } from '../ui/button';
import { Sheet, SheetContent, SheetTitle, SheetTrigger } from '../ui/sheet';
import Link from 'next/link';
import { signOut, useSession } from 'next-auth/react';
import { Avatar, AvatarFallback, AvatarImage } from '../ui/avatar';
import { useEffect, useState } from 'react';
import { usePathname } from 'next/navigation';
import { Separator } from '../ui/separator';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '../ui/alert-dialog';
import { ThemeToggle } from '../ui/theme-toggle';
import { useTheme } from 'next-themes';
import { Notifications } from '../notifications';
import { Badge } from '../ui/badge';

export const Header = () => {
    const { data: session, status } = useSession();
    const [userRole, setUserRole] = useState('');
    const [clientId, setClientId] = useState<string | null>(null);
    const { resolvedTheme } = useTheme();
    const [mounted, setMounted] = useState(false);
    const pathname = usePathname();

    const isActive = (path: string) => {
        if (!pathname) return false;
        if (path === '/') return pathname === '/';
        return pathname === path || pathname.startsWith(path + '/');
    };

    useEffect(() => {
        setMounted(true);
    }, []);

    useEffect(() => {
        if (status === "authenticated") {
            const fetchUserData = async () => {
                try {
                    if (session?.user?.email) {
                        const email = session.user.email;

                        const response = await fetch(`/api/users/${email}`, {
                            cache: 'no-store'
                        });

                        if (response.ok) {
                            const user = await response.json();
                            setUserRole(user?.role || '');
                            setClientId(user?.client?.id ?? null);
                        } else {
                            console.error(`Failed to fetch user role. Status: ${response.status}`);
                            const errorResponse = await response.json();
                            console.error("Error response:", errorResponse);
                        }
                    } else {
                        console.error("No email found in session.");
                    }
                } catch (error) {
                    console.error("Error fetching user role:", error);
                }
            };

            fetchUserData();
        }
    }, [status, session]);

    const handleSignOutClick = () => signOut();

    return (
        <>
            <header className='hidden md:flex items-center p-4 xs:px-8 xs:py-4 justify-between border-b border-zinc-200 dark:border-zinc-800'>
                <div className='flex items-center gap-1'>
                    <Sheet>
                        <SheetTrigger asChild>
                            <Button variant="outline" size="icon">
                                <Menu size={24} />
                            </Button>
                        </SheetTrigger>
                        <SheetContent side="left">
                            <SheetTitle className='left-9'>
                                <Link href="/">
                                    <Image src='/icon-b4desk.svg' width={25} height={25} alt="B4Desk" />
                                </Link>
                            </SheetTitle>
                            <nav className='flex flex-col mt-14'>
                                <span className='text-xs text-muted-foreground mb-2 ml-2'>
                                    Geral
                                </span>
                                <Button variant="ghost" className='justify-start gap-1 items-center font-normal' asChild>
                                    <Link href="/">
                                        <LayoutDashboard size={24} />
                                        Dashboard
                                    </Link>
                                </Button>

                                {(userRole === 'ADMIN' || userRole === 'DEVELOPER' || userRole === 'GENERAL_ASSISTANT') && (
                                    <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                        <Link href="/panel">
                                            <PanelTop size={24} />
                                            Painel
                                        </Link>
                                    </Button>
                                )}

                                {(['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER', 'DESIGNER_SENIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                    <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                        <Link href="/clients">
                                            <Users size={24} />
                                            Clientes
                                        </Link>
                                    </Button>
                                )}

                                {(['COPY', 'ADMIN', 'DEVELOPER', 'DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                    <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                        <Link href="/my-demands">
                                            <ListTodo size={24} />
                                            Minhas demandas
                                        </Link>
                                    </Button>
                                )}

                                {(['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                    <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                        <Link href="/history-point-record">
                                            <FileClock size={24} />
                                            Meu histórico de ponto
                                        </Link>
                                    </Button>
                                )}

                                {(['CLIENT'].includes(userRole)) && (
                                    <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                        <Link href={`/clients/${clientId || session?.user?.id}`}>
                                            <User size={24} />
                                            Meu perfil
                                        </Link>
                                    </Button>
                                )}

                                {(['CLIENT'].includes(userRole)) && (
                                    <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                        <Link href={`/clients/${clientId || session?.user?.id}/calendars`}>
                                            <Calendar size={24} />
                                            Calendários
                                        </Link>
                                    </Button>
                                )}

                                {(['CLIENT', 'ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                    <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                        <Link href="/deliveries">
                                            <Grid3X3 size={24} />
                                            Redes sociais
                                        </Link>
                                    </Button>
                                )}

                                <span className='text-xs text-muted-foreground mb-2 mt-4 ml-2'>
                                    Sistema
                                </span>

                                {(!['CLIENT'].includes(userRole)) && (
                                    <Button
                                        variant="ghost"
                                        className='flex justify-start gap-1 items-center font-normal'
                                        asChild
                                    >
                                        <Link href="/notifications">
                                            <Bell size={24} />
                                            Notificações
                                        </Link>
                                    </Button>
                                )}

                                {(['CLIENT'].includes(userRole)) && (
                                    <Button
                                        variant="ghost"
                                        disabled
                                        className='flex justify-start gap-1 items-center font-normal cursor-not-allowed opacity-50'
                                        asChild
                                    >
                                        <div>
                                            <Bell size={24} />
                                            Notificações
                                            <Badge variant="secondary" className="ml-2 text-xs">
                                                Em breve
                                            </Badge>
                                        </div>
                                    </Button>
                                )}

                                {(['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                    <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                        <Link href={userRole !== 'VIEWER' ? "/system" : "/updates"}>
                                            <MonitorCog size={24} />
                                            Sistema
                                        </Link>
                                    </Button>
                                )}
                            </nav>
                        </SheetContent>
                    </Sheet>
                    <Link href="/">
                        <div className='flex items-center mx-2 gap-1'>
                            {mounted && (
                                <Image
                                    src={resolvedTheme === "dark"
                                        ? "/logo-b4desk-dark.png"
                                        : "/logo-b4desk-light.png"}
                                    width={85}
                                    height={32}
                                    alt="B4Desk"
                                />
                            )}
                        </div>
                    </Link>
                </div>
                <div className='flex items-center gap-4'>
                    <div className='flex gap-2'>
                        <Button variant="outline" size="icon" title='Início' asChild>
                            <Link href="/">
                                <Home />
                            </Link>
                        </Button>
                        <ThemeToggle />
                        {(['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                            <Notifications />
                        )}
                    </div>
                    <Sheet>
                        <SheetTrigger title='Minha conta'>
                            <Avatar className='border-2 border-zinc-200'>
                                <AvatarImage src={session?.user?.image ?? 'https://github.com/shadcn.png'} alt={session?.user?.name ?? 'Usuário sem nome'} />
                                <AvatarFallback>
                                    {session?.user?.name?.charAt(0).toUpperCase()}
                                </AvatarFallback>
                            </Avatar>
                        </SheetTrigger>
                        <SheetContent side="right">
                            <SheetTitle>
                                Minha conta
                            </SheetTitle>
                            <nav className='flex flex-col gap-2 mt-14'>
                                <Button variant="outline" className='flex justify-start gap-1 items-center font-normal' asChild>
                                    <Link href="/profile">
                                        <User size={24} />
                                        Perfil
                                    </Link>
                                </Button>
                                <Button variant="outline" className='flex justify-start gap-1 items-center font-normal' asChild>
                                    <Link href="/settings">
                                        <Cog size={24} />
                                        Configurações
                                    </Link>
                                </Button>
                                <Separator orientation="horizontal" className='my-6' />
                                <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                        <Button variant="outline" className='flex justify-start gap-1 items-center font-normal'>
                                            <LogOut />
                                            Sair
                                        </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                        <AlertDialogHeader>
                                            <AlertDialogTitle>
                                                Sair da conta
                                            </AlertDialogTitle>
                                            <AlertDialogDescription>
                                                Deseja realmente sair da sua conta?
                                            </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                            <AlertDialogAction onClick={handleSignOutClick}>Continuar</AlertDialogAction>
                                        </AlertDialogFooter>
                                    </AlertDialogContent>
                                </AlertDialog>
                            </nav>
                        </SheetContent>
                    </Sheet>
                </div>
            </header>

            <header className='md:hidden flex items-center p-4 xs:px-8 xs:py-4 justify-between border-b border-zinc-200 dark:border-zinc-800 fixed top-0 left-0 right-0 bg-zinc-100 dark:bg-zinc-900 z-50'>
                <div className='flex items-center gap-1'>
                    <Sheet>
                        <SheetTrigger asChild>
                            <Button variant="outline" size="icon">
                                <Menu size={24} />
                            </Button>
                        </SheetTrigger>
                        <SheetContent side="left">
                            <SheetTitle className='left-9'>
                                <Link href="/">
                                    <Image src='/icon-b4desk.svg' width={25} height={25} alt="B4Desk" />
                                </Link>
                            </SheetTitle>
                            <div className='flex flex-col h-full justify-between'>
                                <nav className='flex flex-col mt-14'>
                                    <span className='text-xs text-muted-foreground mb-2 ml-2'>
                                        Geral
                                    </span>
                                    <Button variant="ghost" className='justify-start gap-1 items-center font-normal' asChild>
                                        <Link href="/">
                                            <LayoutDashboard size={24} />
                                            Dashboard
                                        </Link>
                                    </Button>

                                    {(userRole === 'ADMIN' || userRole === 'DEVELOPER' || userRole === 'GENERAL_ASSISTANT') && (
                                        <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                            <Link href="/panel">
                                                <PanelTop size={24} />
                                                Painel
                                            </Link>
                                        </Button>
                                    )}

                                    {(['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER_SENIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                        <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                            <Link href="/clients">
                                                <Users size={24} />
                                                Clientes
                                            </Link>
                                        </Button>
                                    )}

                                    {(['COPY', 'ADMIN', 'DEVELOPER', 'DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                        <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                            <Link href="/my-demands">
                                                <ListTodo size={24} />
                                                Minhas demandas
                                            </Link>
                                        </Button>
                                    )}

                                    {(['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                        <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                            <Link href="/history-point-record">
                                                <FileClock size={24} />
                                                Meu histórico de ponto
                                            </Link>
                                        </Button>
                                    )}

                                    {(['CLIENT'].includes(userRole)) && (
                                        <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                            <Link href={`/clients/${clientId || session?.user?.id}`}>
                                                <User size={24} />
                                                Meu perfil
                                            </Link>
                                        </Button>
                                    )}

                                    {(['CLIENT'].includes(userRole)) && (
                                        <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                            <Link href={`/clients/${clientId || session?.user?.id}/calendars`}>
                                                <Calendar size={24} />
                                                Calendários
                                            </Link>
                                        </Button>
                                    )}

                                    {(['CLIENT', 'ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                        <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                            <Link href="/deliveries">
                                                <Grid3X3 size={24} />
                                                Redes sociais
                                            </Link>
                                        </Button>
                                    )}

                                    <span className='text-xs text-muted-foreground mb-2 mt-4 ml-2'>
                                        Sistema
                                    </span>
                                    {(!['CLIENT'].includes(userRole)) && (
                                        <Button
                                            variant="ghost"
                                            className='flex justify-start gap-1 items-center font-normal'
                                            asChild
                                        >
                                            <Link href="/notifications">
                                                <Bell size={24} />
                                                Notificações
                                            </Link>
                                        </Button>
                                    )}

                                    {(['CLIENT'].includes(userRole)) && (
                                        <Button
                                            variant="ghost"
                                            disabled
                                            className='flex justify-start gap-1 items-center font-normal cursor-not-allowed opacity-50'
                                            asChild
                                        >
                                            <div>
                                                <Bell size={24} />
                                                Notificações
                                                <Badge variant="secondary" className="ml-2 text-xs">
                                                    Em breve
                                                </Badge>
                                            </div>
                                        </Button>
                                    )}

                                    {(['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                                        <Button variant="ghost" className='flex justify-start gap-1 items-center font-normal' asChild>
                                            <Link href={userRole !== 'VIEWER' ? "/system" : "/updates"}>
                                                <MonitorCog size={24} />
                                                Sistema
                                            </Link>
                                        </Button>
                                    )}
                                </nav>
                                <div className='space-y-2'>
                                    <p className='text-xs text-muted-foreground mt-4 ml-2'>
                                        © B4Desk – B4 Comunicação
                                    </p>
                                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-300 rounded-full">
                                        v1.0.2
                                    </Badge>
                                </div>
                            </div>
                        </SheetContent>
                    </Sheet>
                    <Link href="/">
                        <div className='flex items-center mx-2 gap-1'>
                            {mounted && (
                                <Image
                                    src={resolvedTheme === "dark"
                                        ? "/logo-b4desk-dark.png"
                                        : "/logo-b4desk-light.png"}
                                    width={85}
                                    height={32}
                                    alt="B4Desk"
                                />
                            )}
                        </div>
                    </Link>
                </div>
                <div className='flex items-center gap-4'>
                    <div className='flex gap-2'>
                        <ThemeToggle />
                        {(['ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                            <Notifications />
                        )}
                    </div>
                    <Sheet>
                        <SheetTrigger title='Minha conta'>
                            <Avatar className='border-2 border-zinc-200'>
                                <AvatarImage src={session?.user?.image ?? 'https://github.com/shadcn.png'} alt={session?.user?.name ?? 'Usuário sem nome'} />
                                <AvatarFallback>
                                    {session?.user?.name?.charAt(0).toUpperCase()}
                                </AvatarFallback>
                            </Avatar>
                        </SheetTrigger>
                        <SheetContent side="right">
                            <SheetTitle>
                                Minha conta
                            </SheetTitle>
                            <nav className='flex flex-col gap-2 mt-14'>
                                <Button variant="outline" className='flex justify-start gap-1 items-center font-normal' asChild>
                                    <Link href="/profile">
                                        <User size={24} />
                                        Perfil
                                    </Link>
                                </Button>
                                <Button variant="outline" className='flex justify-start gap-1 items-center font-normal' asChild>
                                    <Link href="/settings">
                                        <Cog size={24} />
                                        Configurações
                                    </Link>
                                </Button>
                                <Separator orientation="horizontal" className='my-6' />
                                <AlertDialog>
                                    <AlertDialogTrigger asChild>
                                        <Button variant="outline" className='flex justify-start gap-1 items-center font-normal'>
                                            <LogOut />
                                            Sair
                                        </Button>
                                    </AlertDialogTrigger>
                                    <AlertDialogContent>
                                        <AlertDialogHeader>
                                            <AlertDialogTitle>
                                                Sair da conta
                                            </AlertDialogTitle>
                                            <AlertDialogDescription>
                                                Deseja realmente sair da sua conta?
                                            </AlertDialogDescription>
                                        </AlertDialogHeader>
                                        <AlertDialogFooter>
                                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                            <AlertDialogAction onClick={handleSignOutClick}>Continuar</AlertDialogAction>
                                        </AlertDialogFooter>
                                    </AlertDialogContent>
                                </AlertDialog>
                            </nav>
                        </SheetContent>
                    </Sheet>
                </div>
            </header>

            <div className='fixed bottom-0 left-0 right-0 flex md:hidden items-center p-4 xs:px-8 xs:py-4 justify-between border-t border-zinc-200 dark:border-zinc-800 bg-zinc-100 dark:bg-zinc-900 z-50'>
                <div className='flex justify-evenly w-full'>
                    {(['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                        <div className="flex flex-col items-center gap-2">
                            <Button variant="outline" title='Painel' asChild className={`${isActive('/panel') ? 'bg-primary2 text-white' : ''}`}>
                                <Link href="/panel">
                                    <PanelTop />
                                </Link>
                            </Button>
                            <span className='text-xs text-muted-foreground'>
                                Painel
                            </span>
                        </div>
                    )}

                    {(['DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'COPY'].includes(userRole)) && (
                        <div className="flex flex-col items-center gap-2">
                            <Button variant="outline" title='Ponto' asChild className={`${isActive('/point') ? 'bg-primary2 text-white' : ''}`}>
                                <Link href="/point">
                                    <FileClock />
                                </Link>
                            </Button>
                            <span className='text-xs text-muted-foreground'>
                                Ponto
                            </span>
                        </div>
                    )}

                    {(['VIEWER'].includes(userRole)) && (
                        <div className="flex flex-col items-center gap-2">
                            <Button
                                variant="outline"
                                title='Início'
                                asChild
                                className={`${isActive('/system') ? 'bg-primary2 text-white' : ''}`}
                            >
                                <Link href="/system">
                                    <Cog />
                                </Link>
                            </Button>
                            <span className='text-xs text-muted-foreground'>
                                Sistema
                            </span>
                        </div>
                    )}

                    {(['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                        <div className="flex flex-col items-center gap-2">
                            <Button variant="outline" title='Demandas' asChild className={`${isActive('/admin/demands') ? 'bg-primary2 text-white' : ''}`}>
                                <Link href="/admin/demands">
                                    <ListTodo />
                                </Link>
                            </Button>
                            <span className='text-xs text-muted-foreground'>
                                Demandas
                            </span>
                        </div>
                    )}

                    {(['DESIGNER', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'COPY'].includes(userRole)) && (
                        <div className="flex flex-col items-center gap-2">
                            <Button variant="outline" title='Minhas demandas' asChild className={`${isActive('/my-demands') ? 'bg-primary2 text-white' : ''}`}>
                                <Link href="/my-demands">
                                    <ListTodo />
                                </Link>
                            </Button>
                            <span className='text-xs text-muted-foreground'>
                                Demandas
                            </span>
                        </div>
                    )}

                    {(['CLIENT'].includes(userRole)) && (
                        <div className="flex flex-col items-center gap-2">
                            <Button variant="outline" title='Meu perfil' asChild className={`${isActive(`/clients/${clientId}`) ? 'bg-primary2 text-white' : ''}`}>
                                <Link href={`/clients/${clientId}`}>
                                    <User />
                                </Link>
                            </Button>
                            <span className='text-xs text-muted-foreground'>
                                Perfil
                            </span>
                        </div>
                    )}

                    <div className="flex flex-col items-center gap-2">
                        <Button variant="outline" title='Início' asChild className={`${isActive('/dashboard') ? 'bg-primary2 text-white' : ''}`}>
                            <Link href="/">
                                <Home />
                            </Link>
                        </Button>
                        <span className='text-xs text-muted-foreground'>
                            Início
                        </span>
                    </div>

                    {(['ADMIN', 'DEVELOPER', 'GENERAL_ASSISTANT', 'COPY', 'DESIGNER_SENIOR'].includes(userRole)) && (
                        <div className="flex flex-col items-center gap-2">
                            <Button variant="outline" title='Clientes' asChild className={`${isActive('/clients') ? 'bg-primary2 text-white' : ''}`}>
                                <Link href="/clients">
                                    <Users />
                                </Link>
                            </Button>
                            <span className='text-xs text-muted-foreground'>
                                Clientes
                            </span>
                        </div>
                    )}

                    {(['CLIENT'].includes(userRole)) && (
                        <div className="flex flex-col items-center gap-2">
                            <Button variant="outline" title='Calendários' asChild className={`${isActive(`/clients/${clientId}/calendars`) ? 'bg-primary2 text-white' : ''}`}>
                                <Link href={`/clients/${clientId}/calendars`}>
                                    <Calendar />
                                </Link>
                            </Button>
                            <span className='text-xs text-muted-foreground'>
                                Calend
                            </span>
                        </div>
                    )}
                    {(['CLIENT', 'ADMIN', 'DEVELOPER', 'COPY', 'DESIGNER_SENIOR', 'DESIGNER_JUNIOR', 'GENERAL_ASSISTANT'].includes(userRole)) && (
                        <div className="flex flex-col items-center gap-2">
                            <Button variant="outline" title='Redes sociais' asChild className={`${isActive('/deliveries') ? 'bg-primary2 text-white' : ''}`}>
                                <Link href="/deliveries">
                                    <Grid3X3 />
                                </Link>
                            </Button>
                            <span className='text-xs text-muted-foreground'>
                                Redes
                            </span>
                        </div>
                    )}
                </div>
            </div >
        </>
    );
};