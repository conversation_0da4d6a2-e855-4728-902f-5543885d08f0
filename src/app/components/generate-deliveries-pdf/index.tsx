"use client";

import { useState } from "react";
import { Button } from "@/app/components/ui/button";
import { Download, Ellipsis } from "lucide-react";
import { toast } from "sonner";
import html2canvas from "html2canvas";
import jsPDF from "jspdf";
import { format } from "date-fns";
import { ptBR } from "date-fns/locale";
import { PDFProgressModal } from "@/app/components/pdf-progress-modal";

interface GenerateDeliveriesPDFProps {
  clientId: string;
  clientName: string;
  month?: string;
  disabled?: boolean;
  captionFilter?: "todos" | "com-legenda" | "sem-legenda";
  selectedItems?: string[];
}

export const GenerateDeliveriesPDF = ({
  clientId,
  clientName,
  month,
  disabled = false,
  captionFilter = "todos",
  selectedItems,
}: GenerateDeliveriesPDFProps) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const [showProgressModal, setShowProgressModal] = useState(false);
  const [currentStep, setCurrentStep] = useState(0);
  const [currentStepName, setCurrentStepName] = useState("");
  const [progressError, setProgressError] = useState<string | undefined>();

  const updateProgress = (step: number, stepName: string) => {
    setCurrentStep(step);
    setCurrentStepName(stepName);
  };

  const handleGeneratePDF = async () => {
    if (!clientId || disabled) return;

    setIsGenerating(true);
    setShowProgressModal(true);
    setCurrentStep(0);
    setProgressError(undefined);

    updateProgress(1, "Preparando ambiente");

    try {
      // Criar um iframe oculto para renderizar o conteúdo
      const iframe = document.createElement("iframe");
      iframe.style.width = "1100px";
      iframe.style.height = "1px";
      iframe.style.position = "absolute";
      iframe.style.top = "-9999px";
      iframe.style.left = "-9999px";
      document.body.appendChild(iframe);

      updateProgress(2, "Carregando dados");

      // Definir a URL para o iframe
      const url = `/deliveries-pdf?id=${clientId}${month ? `&month=${month}` : ""}${captionFilter && captionFilter !== "todos" ? `&filter=${captionFilter}` : ""}${selectedItems && selectedItems.length > 0 ? `&selectedItems=${selectedItems.join(',')}` : ""}`;
      iframe.src = url;

      // Aguardar o carregamento do iframe
      iframe.onload = async () => {
        try {
          updateProgress(3, "Processando conteúdo");
          await new Promise((resolve) => setTimeout(resolve, 5000)); // Aumentado de 3s para 5s

          const iframeDocument = iframe.contentDocument || iframe.contentWindow?.document;
          if (!iframeDocument) {
            throw new Error("Não foi possível acessar o conteúdo do iframe.");
          }

          // Garantir que o CSS do PDF está presente no iframe
          const styleHref = '/app/feed-structure-pdf/pdf-styles.css';
          if (iframeDocument.head && !iframeDocument.head.querySelector(`link[href*="${styleHref}"]`)) {
            const styleLink = iframeDocument.createElement('link');
            styleLink.rel = 'stylesheet';
            styleLink.type = 'text/css';
            styleLink.href = styleHref;
            iframeDocument.head.appendChild(styleLink);
            // Aguarda o carregamento do CSS
            await new Promise((resolve) => setTimeout(resolve, 500));
          }

          // CSS inline para garantir layout correto no PDF
          const style = iframeDocument.createElement('style');
          style.innerHTML = `
            /* CSS com alta especificidade para sobrescrever estilos externos */
            .pdf-container .pdf-card,
            div.pdf-card,
            .pdf-grid .pdf-card {
              width: 160px !important;
              max-width: 160px !important;
              min-width: 120px !important;
              flex: 0 0 160px !important;
              margin: 0 !important;
              break-inside: avoid !important;
              page-break-inside: avoid !important;
              background: #fff !important;
              background-color: #fff !important;
              border-radius: 0px !important;
              box-shadow: 0 1px 4px rgba(0,0,0,0.04) !important;
              display: flex !important;
              flex-direction: column !important;
              align-items: center !important;
              padding: 0px !important;
            }
            .pdf-grid {
              display: flex !important;
              flex-direction: row !important;
              flex-wrap: wrap !important;
              gap: 16px !important;
              justify-content: flex-start !important;
              align-items: flex-start !important;
              margin-top: 0px !important;
              background: #f9f8f4 !important;
              background-color: #f9f8f4 !important;
              padding: 0px !important;
            }
            /* CSS com alta especificidade para containers de imagem */
            .pdf-container .pdf-image-container,
            div.pdf-image-container,
            .pdf-card .pdf-image-container {
              width: 100% !important;
              aspect-ratio: 1/1 !important;
              position: relative !important;
              overflow: hidden !important;
              background: #f7f7f7 !important;
              background-color: #f7f7f7 !important;
              border-radius: 4px !important;
              display: flex !important;
              align-items: center !important;
              justify-content: center !important;
            }
            .pdf-image-container img {
              width: 100% !important;
              height: 100% !important;
              object-fit: contain !important;
              display: block !important;
            }
            /* Estilos para o ícone de play - não afeta layout */
            .pdf-container .play-icon,
            div.play-icon {
              position: absolute !important;
              top: 50% !important;
              left: 50% !important;
              transform: translate(-50%, -50%) !important;
              z-index: 10 !important;
              pointer-events: none !important;
              display: flex !important;
              align-items: center !important;
              justify-content: center !important;
            }
            /* Forçar tamanho pequeno do ícone de play */
            .pdf-single-image-row img[src*="play.png"],
            .pdf-single-image-row img[alt="Play"] {
              width: 70px !important;
              height: 70px !important;
              max-width: 70px !important;
              max-height: 70px !important;
            }
            .pdf-single-image-row > div > div[style*="position: absolute"] {
              width: 70px !important;
              height: 70px !important;
            }
            /* Para imagens únicas grandes - aplicar diretamente na div que contém a imagem */
            .pdf-container .pdf-single-image-row,
            div.pdf-single-image-row {
              width: 100% !important;
              height: auto !important;
              display: flex !important;
              justify-content: center !important;
              align-items: center !important;
              background: #f9f8f4 !important;
              background-color: #f9f8f4 !important;
              border-radius: 8px !important;
              padding: 20px !important;
            }
            .pdf-single-image-row > div {
              width: auto !important;
              max-width: 400px !important;
              height: auto !important;
              aspect-ratio: auto !important;
            }
            .pdf-single-image-row img,
            .pdf-single-image-row [data-pdf-image="true"] {
              width: auto !important;
              height: auto !important;
              max-width: 400px !important;
              max-height: 400px !important;
              object-fit: contain !important;
              display: block !important;
              position: static !important;
            }
            /* Sobrescrever estilos específicos para imagens de feed únicas */
            .pdf-single-image-row img[alt*="Visualização da entrega"] {
              width: auto !important;
              height: auto !important;
              max-width: 400px !important;
              max-height: 400px !important;
              object-fit: contain !important;
            }
            /* CSS mais específico para forçar o layout correto das imagens únicas */
            div[style*="minHeight: '600px'"] img,
            div[style*="min-height: 600px"] img {
              width: auto !important;
              height: auto !important;
              max-width: 100% !important;
              max-height: 100% !important;
              object-fit: contain !important;
              position: static !important;
            }
            div[style*="aspect-ratio: 1/1"] {
              aspect-ratio: auto !important;
            }
            .pdf-caption-container {
              margin-top: 0px !important;
              padding-top: 0px !important;
              padding-bottom: 0px !important;
              border-top: none !important;
              width: 100% !important;
              box-sizing: border-box !important;
              background: #fff !important;
              background-color: #fff !important;
            }
            .pdf-caption {
              font-size: 16px !important;
              width: 100% !important;
              background: #fff !important;
              background-color: #fff !important;
            }
            .pdf-caption span {
              font-weight: bold !important;
              display: none !important;
              margin-bottom: 0px !important;
            }
            .pdf-caption-text {
              overflow: visible !important;
              text-align: justify !important;
              font-size: 16px !important;
              width: 100% !important;
              box-sizing: border-box !important;
              border: none !important;
              padding: 0px !important;
              border-radius: 0 !important;
              background-color: transparent !important;
              margin-top: 0px !important;
            }
            /* CSS mais específico para sobrescrever estilos externos */
            .pdf-container .pdf-caption-text,
            div.pdf-caption-text {
              font-size: 16px !important;
            }
            /* Remover qualquer texto "Legenda" */
            .pdf-caption span,
            .pdf-container .pdf-caption span,
            div.pdf-caption span,
            [class*="caption"] span {
              display: none !important;
              visibility: hidden !important;
            }
            /* Ocultar elementos que contenham "Legenda" */
            .pdf-caption-container span:contains("Legenda"),
            .pdf-caption span:contains("Legenda"),
            span:contains("Legenda") {
              display: none !important;
              visibility: hidden !important;
            }
            .pdf-content-type, .pdf-date, .pdf-destination {
              display: inline-flex !important;
              align-items: center !important;
              justify-content: center !important;
              border: 1.5px solid #e64729 !important;
              border-radius: 18px !important;
              padding: 0 8px 16px 8px !important;
              margin-right: 8px !important;
              margin-bottom: 6px !important;
              background: #fff7f5 !important;
              font-size: 13px !important;
              font-weight: 600 !important;
              color: #e64729 !important;
              box-sizing: border-box !important;
              text-align: center !important;
              vertical-align: middle !important;
              min-height: 28px !important;
            }
            .pdf-folder-link {
              display: none !important; /* Ocultar no HTML, será desenhado no PDF */
            }
            .pdf-folder-button {
              display: none !important; /* Ocultar no HTML, será desenhado no PDF */
            } no PDF */
            }
          `;
          iframeDocument.head.appendChild(style);
          await new Promise((resolve) => setTimeout(resolve, 200));

          const pdfContainer = iframeDocument.querySelector(".pdf-container") as HTMLElement;
          if (!pdfContainer) {
            throw new Error("Elemento .pdf-container não encontrado no iframe.");
          }

          const options = {
            scale: 2,
            useCORS: true,
            allowTaint: true,
            logging: false,
            windowWidth: iframe.style.width ? parseInt(iframe.style.width, 10) : 1100,
            imageTimeout: 20000,
          };

          const pdf = new jsPDF({
            orientation: "landscape",
            unit: "mm",
            format: "a4",
            compress: true,
          });

          pdf.setProperties({
            title: `Entregas - ${clientName}`,
            subject: `Entregas para ${clientName}`,
            author: "B4Desk",
            creator: "B4Desk",
          });

          const createHeader = (pdfInstance: jsPDF) => {
            pdfInstance.setFillColor(229, 71, 41);
            pdfInstance.roundedRect(15, 5, 38, 8, 4, 4, 'F');
            pdfInstance.setTextColor(255, 255, 255);
            pdfInstance.setFontSize(10);
            pdfInstance.text("Etapa 07", 18, 10.5);

            pdfInstance.setDrawColor(229, 71, 41);
            pdfInstance.setLineWidth(0.3);
            pdfInstance.roundedRect(56, 5, 48, 8, 4, 4, 'S');
            pdfInstance.setTextColor(229, 71, 41);
            pdfInstance.text("Entregas", 60, 10.5);

            pdfInstance.setTextColor(229, 71, 41);
            pdfInstance.setFontSize(18);
            pdfInstance.setFont("helvetica", "bold");
            pdfInstance.text("ENTREGAS DE MATERIAIS", 15, 20);

            pdfInstance.setDrawColor(200, 200, 200);
            pdfInstance.line(15, 27, 282, 27); // Ajustado para largura A4 landscape

            return 32;
          };

          const createCoverPage = (pdfInstance: jsPDF) => {
            // Criar cabeçalho na capa para teste
            createHeader(pdfInstance);

            const centerX = pdfWidth / 2;
            const centerY = pdfHeight / 2;

            // Adicionar imagem do logo com proporção correta
            const logoPath = '/logo-rodapé-17-b4(1).png';
            // Usando proporção 3:1 que é mais comum para logos horizontais
            const logoWidth = 33;
            const logoHeight = 38; // Proporção 3:1 para evitar distorção
            const logoX = centerX - (logoWidth / 2);
            const logoY = centerY - 35; // Reduzido de -50 para -35

            try {
              pdfInstance.addImage(logoPath, 'PNG', logoX, logoY, logoWidth, logoHeight);
            } catch (error) {
              console.warn('Erro ao carregar logo:', error);
            }

            // Nome do cliente
            pdfInstance.setTextColor(229, 71, 41);
            pdfInstance.setFontSize(24);
            pdfInstance.setFont("helvetica", "bold");
            pdfInstance.text(clientName, centerX, centerY + 15, { align: 'center' });

            // Mês e ano
            if (month) {
              const [m, y] = month.split('-');
              const monthName = format(new Date(Number(y), Number(m) - 1, 1), 'MMMM', { locale: ptBR });
              const formattedMonth = `${monthName.charAt(0).toUpperCase() + monthName.slice(1)} de ${y}`;

              pdfInstance.setFontSize(18);
              pdfInstance.setTextColor(44, 62, 80);
              pdfInstance.setFont("helvetica", "normal");
              pdfInstance.text(formattedMonth.toUpperCase(), centerX, centerY + 25, { align: 'center' });
            }

            // Rodapé discreto no lado direito
            pdfInstance.setTextColor(150, 150, 150); // Cor cinza clara
            pdfInstance.setFontSize(10); // Fonte pequena
            pdfInstance.setFont("helvetica", "normal");
            pdfInstance.text("Feito com B4Desk", pdfWidth - 15, pdfHeight - 10, { align: 'right' }); // 15mm da borda direita e 10mm da borda inferior
          };

          let demandGroups = Array.from(iframeDocument.querySelectorAll('.pdf-demand-group')) as HTMLElement[];

          if (demandGroups.length === 0) {
            updateProgress(3, "Aguardando carregamento dos dados");
            await new Promise((resolve) => setTimeout(resolve, 3000));
            demandGroups = Array.from(iframeDocument.querySelectorAll('.pdf-demand-group')) as HTMLElement[];
          }

          if (demandGroups.length === 0) {
            throw new Error('Nenhum grupo de demanda encontrado para exportação. Verifique se há conteúdos aprovados para o mês selecionado.');
          }

          const pdfWidth = pdf.internal.pageSize.getWidth();
          const pdfHeight = pdf.internal.pageSize.getHeight();
          const margin = 15;

          // Criar página de capa
          updateProgress(4, "Renderizando imagens");
          createCoverPage(pdf);

          for (let i = 0; i < demandGroups.length; i++) {
            updateProgress(4, `Renderizando página ${i + 1} de ${demandGroups.length}`);
            const group = demandGroups[i] as HTMLElement;
            pdf.addPage(); // Sempre adiciona uma nova página (capa já foi criada)

            // SEM cabeçalho nas páginas de conteúdo (apenas na capa para teste)
            const headerEndY = 2; // Reduzido para diminuir o espaçamento do topo

            // Verificar se este grupo contém vídeo e obter as URLs
            const videoUrls: string[] = [];
            const contentTypeElement = group.querySelector('.pdf-content-type');
            const urlMediaTypesElement = group.querySelector('.pdf-url-media-types');
            const urlStructuringFeedElement = group.querySelector('.pdf-url-structuring-feed');

            // Determinar se é carrossel com vídeo (definir no escopo principal)
            let isCarouselWithVideo = false;

            if (contentTypeElement) {
              const contentType = contentTypeElement.textContent?.toLowerCase() || '';
              let urlMediaTypes: string[] = [];
              let urlStructuringFeed: string[] = [];

              // Tentar obter urlMediaTypes
              if (urlMediaTypesElement) {
                try {
                  urlMediaTypes = JSON.parse(urlMediaTypesElement.textContent || '[]');
                } catch (e) {
                  console.warn('Erro ao parsear urlMediaTypes:', e);
                  urlMediaTypes = [];
                }
              }

              // Tentar obter urlStructuringFeed (URLs originais)
              if (urlStructuringFeedElement) {
                try {
                  urlStructuringFeed = JSON.parse(urlStructuringFeedElement.textContent || '[]');
                } catch (e) {
                  console.warn('Erro ao parsear urlStructuringFeed:', e);
                  urlStructuringFeed = [];
                }
              }

              // Verificar se é vídeo direto pelo contentType
              const isVideoContentType = ['vídeo', 'video', 'reel', 'reels', 'animação'].some(type =>
                contentType.includes(type.toLowerCase())
              );

              // Verificar se é carrossel com pelo menos um vídeo
              // Considerar como carrossel se:
              // 1. contentType explicitamente é "carrossel" OU
              // 2. há múltiplos vídeos (independente do contentType)
              const hasMultipleVideos = urlMediaTypes.filter(type => type === 'video').length > 1;
              isCarouselWithVideo = (contentType.includes('carrossel') || hasMultipleVideos) &&
                urlMediaTypes.some(mediaType => mediaType === 'video');

              if (isCarouselWithVideo) {
                // Para carrossel, usar as URLs originais dos vídeos
                urlMediaTypes.forEach((mediaType, idx) => {
                  if (mediaType === 'video' && idx < urlStructuringFeed.length) {
                    // Usar a URL original do vídeo, não a da imagem renderizada
                    const originalVideoUrl = urlStructuringFeed[idx];
                    if (originalVideoUrl) {
                      videoUrls.push(originalVideoUrl);
                    }
                  }
                });

              } else if (isVideoContentType) {
                // Para vídeo direto, usar a primeira URL original
                if (urlStructuringFeed.length > 0) {
                  const originalVideoUrl = urlStructuringFeed[0];
                  if (originalVideoUrl) {
                    videoUrls.push(originalVideoUrl);
                  }
                }
              } else {
                // Caso não seja nem carrossel nem vídeo direto, mas ainda tenha vídeos no urlMediaTypes
                const videosInMediaTypes = urlMediaTypes.filter(type => type === 'video').length;
                if (videosInMediaTypes > 0) {
                  // Usar as URLs originais dos vídeos
                  urlMediaTypes.forEach((mediaType, idx) => {
                    if (mediaType === 'video' && idx < urlStructuringFeed.length) {
                      const originalVideoUrl = urlStructuringFeed[idx];
                      if (originalVideoUrl) {
                        videoUrls.push(originalVideoUrl);
                      }
                    }
                  });
                }
              }
            }

            // Capturar o grupo normalmente
            const canvas = await html2canvas(group, {
              ...options,
              height: group.scrollHeight,
              width: group.scrollWidth,
            });

            const imgData = canvas.toDataURL("image/jpeg", 0.95);
            const contentWidth = pdfWidth - (margin * 2);
            const contentHeight = (canvas.height * contentWidth) / canvas.width;
            let finalHeight = contentHeight;
            const availableHeight = pdfHeight - headerEndY - margin;
            if (finalHeight > availableHeight) finalHeight = availableHeight;

            pdf.addImage(imgData, "JPEG", margin, headerEndY, contentWidth, finalHeight);

            // Adicionar botões para cada vídeo detectado
            const buttonWidth = 45;
            const buttonHeight = 6;

            // Adicionar informação sobre botões clicáveis se há vídeos
            if (videoUrls.length > 0) {
              const infoStartX = margin;
              const infoStartY = headerEndY + 1.5;

              // Adicionar texto informativo ao lado dos badges
              pdf.setTextColor(100, 100, 100); // Cor cinza
              pdf.setFontSize(8);
              pdf.setFont("helvetica", "normal");
              pdf.text('• Botões de vídeo são clicáveis', infoStartX + 85, infoStartY + 3);
            }

            // Tratar todos os vídeos encontrados, independente de ser carrossel ou não
            if (videoUrls.length > 0) {
              if (isCarouselWithVideo && videoUrls.length === 1) {
                // Carrossel com apenas um vídeo: botão no topo
                const infoStartX = margin;
                const infoStartY = headerEndY + -7;
                const singleVideoButtonWidth = 60; // Largura maior para este botão específico
                const buttonX = infoStartX + 205;
                const buttonY = infoStartY + 8; // Ajustado para não sobrepor o texto informativo
                const buttonText = 'CLIQUE AQUI PARA ASSISTIR O VÍDEO';
                pdf.link(buttonX, buttonY, singleVideoButtonWidth, buttonHeight, { url: videoUrls[0] });
                pdf.setDrawColor(229, 71, 41);
                pdf.setFillColor(229, 71, 41);
                pdf.setLineWidth(1);
                pdf.roundedRect(buttonX, buttonY, singleVideoButtonWidth, buttonHeight, 2, 2, 'FD');
                pdf.setTextColor(255, 255, 255);
                pdf.setFontSize(8);
                pdf.setFont("helvetica", "bold");
                pdf.text(buttonText, buttonX + singleVideoButtonWidth / 2, buttonY + buttonHeight / 2 + 1, { align: 'center' });
              } else {
                // Mantém como está para os demais casos
                videoUrls.forEach((videoUrl, idx) => {
                  // Se for carrossel com mais de um vídeo, todos vão para o topo
                  if (isCarouselWithVideo && videoUrls.length > 1) {
                    const infoStartX = margin;
                    const infoStartY = headerEndY + 1.5; // Ajustado para ficar abaixo do texto informativo

                    // Usar largura menor para múltiplos vídeos em carrossel
                    const carouselButtonWidth = 10; // Largura reduzida para carrosséis
                    const carouselButtonHeight = 5; // Altura reduzida para carrosséis

                    // Calcular posição com quebra de linha se necessário
                    const maxButtonsPerRow = Math.floor((pdfWidth - margin * 2 - 75) / (carouselButtonWidth + 3));
                    const row = Math.floor(idx / maxButtonsPerRow);
                    const col = idx % maxButtonsPerRow;

                    const buttonX = infoStartX + 126 + (col * (carouselButtonWidth + 3));
                    const buttonY = infoStartY + (row * (carouselButtonHeight + 2));
                    const buttonText = `V ${idx + 1}`;

                    pdf.link(buttonX, buttonY, carouselButtonWidth, carouselButtonHeight, { url: videoUrl });
                    pdf.setDrawColor(229, 71, 41);
                    pdf.setFillColor(229, 71, 41);
                    pdf.setLineWidth(1);
                    pdf.roundedRect(buttonX, buttonY, carouselButtonWidth, carouselButtonHeight, 2, 2, 'FD');
                    pdf.setTextColor(255, 255, 255);
                    pdf.setFontSize(7); // Fonte menor para caber no botão menor
                    pdf.setFont("helvetica", "bold");
                    pdf.text(buttonText, buttonX + carouselButtonWidth / 2, buttonY + carouselButtonHeight / 2 + 1, { align: 'center' });
                  } else {
                    // Não-carrossel: mantém como estava
                    const buttonX = (pdfWidth - margin - buttonWidth - 111.5) + (idx * (buttonWidth + 8));
                    const buttonY = headerEndY + 110;
                    const buttonText = videoUrls.length > 1
                      ? `ASSISTIR VÍDEO ${idx + 1}`
                      : 'CLIQUE AQUI PARA ASSISTIR';

                    pdf.link(buttonX, buttonY, buttonWidth, buttonHeight, { url: videoUrl });
                    pdf.setDrawColor(229, 71, 41);
                    pdf.setFillColor(229, 71, 41);
                    pdf.setLineWidth(1);
                    pdf.roundedRect(buttonX, buttonY, buttonWidth, buttonHeight, 2, 2, 'FD');
                    pdf.setTextColor(255, 255, 255);
                    pdf.setFontSize(8);
                    pdf.setFont("helvetica", "bold");
                    pdf.text(buttonText, buttonX + buttonWidth / 2, buttonY + buttonHeight / 2 + 1, { align: 'center' });
                  }
                });
              }
            }

            // Adicionar botão para pasta do Google Drive se existir
            const folderLinkElement = group.querySelector('.pdf-folder-button');
            if (folderLinkElement) {
              const folderUrl = folderLinkElement.getAttribute('href');
              if (folderUrl) {
                const buttonWidth = 70;
                const buttonHeight = 6;
                const buttonX = (pdfWidth - buttonWidth) / 2; // Centralizado horizontalmente
                const buttonY = headerEndY + 116.5;

                // Criar área clicável
                pdf.link(buttonX, buttonY, buttonWidth, buttonHeight, { url: folderUrl });

                // Desenhar botão com opacidade
                pdf.setDrawColor(66, 133, 244); // Azul do Google
                pdf.setFillColor(66, 133, 244);
                pdf.setLineWidth(1);
                if (typeof pdf.setGState === 'function') {
                  // Se jsPDF suporta setGState (versão mais recente)
                  const gState = pdf.GState({ opacity: 0.5 });
                  pdf.setGState(gState);
                  pdf.roundedRect(buttonX, buttonY, buttonWidth, buttonHeight, 2, 2, 'FD');
                  pdf.setGState(pdf.GState({ opacity: 1 })); // Reset para opacidade padrão após desenhar o botão
                } else {
                  // Fallback para versões sem setGState
                  pdf.roundedRect(buttonX, buttonY, buttonWidth, buttonHeight, 2, 2, 'FD');
                }

                // Adicionar texto do botão
                pdf.setTextColor(255, 255, 255);
                pdf.setFontSize(9);
                pdf.setFont("helvetica", "bold");
                pdf.text('CLIQUE PARA VER OS DEMAIS CRIATIVOS', buttonX + buttonWidth / 2, buttonY + buttonHeight / 2 + 1, { align: 'center' });
              }
            }
          }

          updateProgress(5, "Gerando PDF");
          await new Promise((resolve) => setTimeout(resolve, 500)); // Pequena pausa para mostrar o progresso

          updateProgress(6, "Finalizando");
          pdf.save(
            `entregas-${clientName.toLowerCase().replace(/\s+/g, "-")}.pdf`,
          );

          toast.success("PDF gerado com sucesso!");

          // Fechar modal após um breve delay
          setTimeout(() => {
            setShowProgressModal(false);
          }, 1500);
        } catch (error) {
          console.error("Erro ao gerar PDF:", error);
          setProgressError(error instanceof Error ? error.message : "Erro desconhecido ao gerar PDF");
          toast.error("Erro ao gerar PDF. Tente novamente.");
        } finally {
          document.body.removeChild(iframe);
          setIsGenerating(false);
        }
      };

      iframe.onerror = () => {
        document.body.removeChild(iframe);
        setIsGenerating(false);
        setProgressError("Erro ao carregar conteúdo para o PDF");
        toast.error("Erro ao carregar conteúdo para o PDF");
      };
    } catch (error) {
      console.error("Erro ao iniciar geração do PDF:", error);
      setProgressError(error instanceof Error ? error.message : "Erro ao iniciar geração do PDF");
      toast.error("Erro ao gerar PDF");
      setIsGenerating(false);
    }
  };

  return (
    <>
      <Button
        title="Exportar para PDF"
        className="w-full sm:w-auto"
        onClick={handleGeneratePDF}
        disabled={disabled || isGenerating}
      >
        {isGenerating ? (
          <Ellipsis />
        ) : (
          <Download />
        )}
        {selectedItems && selectedItems.length > 0
          ? `Exportar PDF (${selectedItems.length} itens)`
          : "Exportar PDF"
        }
      </Button>

      <PDFProgressModal
        isOpen={showProgressModal}
        onClose={() => setShowProgressModal(false)}
        currentStep={currentStep}
        totalSteps={6}
        currentStepName={currentStepName}
        isComplete={currentStep === 6 && !progressError}
        error={progressError}
      />
    </>
  );
};
