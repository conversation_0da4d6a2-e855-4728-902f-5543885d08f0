"use client";

import { useState, useEffect } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/app/components/ui/select";
import { toast } from "sonner";
import { Ellipsis, Plus, Trash2, TriangleAlert, Eye, Grid3X3, List } from "lucide-react"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter
} from "@/app/components/ui/dialog";
import Link from "next/link";
import { Card, CardContent, CardFooter } from "@/app/components/ui/card"
import { Badge } from "@/app/components/ui/badge"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/app/components/ui/tabs"

interface AddFeedUrlModalProps {
  contentId: string
  currentUrl?: string | string[]
  currentUrlTypes?: string[]
  currentMediaTypes?: string[]
  currentUrlThumbnails?: string[]
  currentUrlFolder?: string
  onUrlUpdated: (
    contentId: string,
    newUrls: string[],
    newUrlTypes: string[],
    newMediaTypes?: string[],
    newUrlFolder?: string,
    newUrlThumbnails?: string[]
  ) => void
  type?: "content" | "general"
}

interface UrlItem {
  id: string
  url: string
  type: "feed" | "story"
  mediaType: "foto" | "video"
  thumbnailUrl?: string
}

const extractGoogleDriveId = (url: string): string | null => {
  if (!url) return null
  const match1 = url.match(/\/d\/([^/]+)/)
  if (match1) return match1[1]
  const match2 = url.match(/id=([^&]+)/)
  if (match2) return match2[1]
  return null
}

export const AddFeedUrlModal = ({
  contentId,
  currentUrl,
  currentUrlTypes,
  currentMediaTypes,
  currentUrlThumbnails,
  currentUrlFolder,
  onUrlUpdated,
  type = "content",
}: AddFeedUrlModalProps) => {
  const [isOpen, setIsOpen] = useState(false)
  const [urls, setUrls] = useState<UrlItem[]>([])
  const [urlFolder, setUrlFolder] = useState<string | null>(null)
  const [isUpdating, setIsUpdating] = useState(false)
  const [hasInitialized, setHasInitialized] = useState(false)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [activeTab, setActiveTab] = useState("all")

  const feedCount = urls.filter((u) => u.type === "feed").length
  const storyCount = urls.filter((u) => u.type === "story").length

  useEffect(() => {
    const checkForUnsavedChanges = () => {
      if (!hasInitialized) return false
      const currentUrls = urls.map((item) => item.url)
      const currentTypes = urls.map((item) => item.type)
      const currentMediaTypes = urls.map((item) => item.mediaType)
      const originalUrls = Array.isArray(currentUrl) ? currentUrl : currentUrl ? [currentUrl] : []
      const originalTypes = currentUrlTypes || []
      const originalMediaTypes = currentMediaTypes || Array(originalUrls.length).fill("foto")
      const urlsChanged = JSON.stringify(currentUrls) !== JSON.stringify(originalUrls)
      const typesChanged = JSON.stringify(currentTypes) !== JSON.stringify(originalTypes)
      const mediaTypesChanged = JSON.stringify(currentMediaTypes) !== JSON.stringify(originalMediaTypes)
      return urlsChanged || typesChanged || mediaTypesChanged
    }
    setHasUnsavedChanges(checkForUnsavedChanges())
  }, [urls, hasInitialized, currentUrl, currentUrlTypes, currentMediaTypes])

  const handleCloseModal = () => {
    if (hasUnsavedChanges) {
      if (confirm("Você tem alterações não salvas. Deseja realmente fechar sem salvar?")) {
        setIsOpen(false)
      }
    } else {
      setIsOpen(false)
    }
  }

  useEffect(() => {
    if (isOpen && !hasInitialized) {
      if (currentUrl) {
        if (Array.isArray(currentUrl)) {
          const urlItems = currentUrl.map((url, index) => ({
            id: `url-${index}`,
            url,
            type: (currentUrlTypes?.[index] === "story" ? "story" : "feed") as "feed" | "story",
            mediaType: (currentMediaTypes?.[index] === "video" ? "video" : "foto") as "foto" | "video",
            thumbnailUrl: currentUrlThumbnails?.[index] || undefined,
          }))
          setUrls(urlItems)
        } else {
          setUrls([
            {
              id: "url-0",
              url: currentUrl,
              type: currentUrlTypes?.[0] === "story" ? "story" : "feed",
              mediaType: currentMediaTypes?.[0] === "video" ? "video" : "foto",
              thumbnailUrl: currentUrlThumbnails?.[0] || undefined,
            },
          ])
        }
      } else {
        setUrls([{ id: "url-0", url: "", type: "feed", mediaType: "foto", thumbnailUrl: undefined }])
      }
      setUrlFolder(currentUrlFolder || null)
      setHasInitialized(true)
    }
  }, [isOpen, currentUrl, currentUrlTypes, currentMediaTypes, currentUrlThumbnails, currentUrlFolder, hasInitialized])

  useEffect(() => {
    if (!isOpen) {
      setHasInitialized(false)
    }
  }, [isOpen])

  const handleOpenDialog = () => {
    setIsOpen(true)
  }

  const handleUpdateUrl = async () => {
    if (!contentId) return
    setIsUpdating(true)
    try {
      const validUrls = urls.filter((item) => item.url.trim() !== "")
      for (const urlItem of validUrls) {
        if (urlItem.url && !urlItem.url.includes("drive.google.com")) {
          toast.error("Por favor, insira apenas URLs válidas do Google Drive")
          setIsUpdating(false)
          return
        }
        if (urlItem.thumbnailUrl && !urlItem.thumbnailUrl.includes("drive.google.com")) {
          toast.error("Por favor, insira apenas URLs válidas do Google Drive para as capas")
          setIsUpdating(false)
          return
        }
      }

      const apiUrl =
        type === "general"
          ? `/api/general-demands/${contentId}/url-structuring`
          : `/api/contents/${contentId}/url-structuring`

      const urlsToSave = validUrls.map((item) => item.url)
      const urlsWithTypes = validUrls.map((item) => ({
        url: item.url,
        type: item.type,
        mediaType: item.mediaType,
        thumbnailUrl: item.thumbnailUrl,
      }))

      const response = await fetch(apiUrl, {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          urlStructuringFeed: urlsToSave,
          urlsWithTypes: urlsWithTypes,
          urlFolder: urlFolder,
        }),
      })

      if (!response.ok) {
        throw new Error(`Erro ao atualizar URLs: ${response.status}`)
      }

      const urlTypesToSave = validUrls.map((item) => item.type)
      const mediaTypesToSave = validUrls.map((item) => item.mediaType)
      const thumbnailsToSave = validUrls.map((item) => item.thumbnailUrl || '')

      toast.success("URLs atualizadas com sucesso")
      onUrlUpdated(contentId, urlsToSave, urlTypesToSave, mediaTypesToSave, urlFolder || undefined, thumbnailsToSave)
      setHasUnsavedChanges(false)
      setIsOpen(false)
    } catch (error) {
      console.error("Erro ao atualizar URLs:", error)
      toast.error("Erro ao atualizar URLs")
    } finally {
      setIsUpdating(false)
    }
  }

  const handleUrlChange = (index: number, newUrl: string) => {
    const newUrls = [...urls]
    newUrls[index].url = newUrl
    setUrls(newUrls)
  }

  const handleTypeChange = (index: number, newType: "feed" | "story") => {
    const newUrls = [...urls]
    newUrls[index].type = newType
    setUrls(newUrls)
  }

  const handleMediaTypeChange = (index: number, newMediaType: "foto" | "video") => {
    const newUrls = [...urls]
    newUrls[index].mediaType = newMediaType
    if (newMediaType === "foto") {
      newUrls[index].thumbnailUrl = undefined
    }
    setUrls(newUrls)
  }

  const handleThumbnailUrlChange = (index: number, newThumbnailUrl: string) => {
    const newUrls = [...urls]
    newUrls[index].thumbnailUrl = newThumbnailUrl
    setUrls(newUrls)
  }

  const addNewUrl = () => {
    const newId = `url-${Date.now()}`
    setUrls([...urls, { id: newId, url: "", type: "feed", mediaType: "foto", thumbnailUrl: undefined }])
  }

  const removeUrl = (index: number) => {
    const newUrls = urls.filter((_, i) => i !== index)
    setUrls(newUrls)
  }

  const validUrls = urls.filter((item) => item.url.trim() !== "" && extractGoogleDriveId(item.url))

  return (
    <>
      <Button variant="outline" size="icon" onClick={handleOpenDialog} title="Gerenciar URLs do Google Drive">
        <Image src="/googledrive.svg" alt="Google Drive" width={16} height={16} />
      </Button>

      <Dialog open={isOpen} onOpenChange={handleCloseModal}>
        <DialogContent className="max-w-7xl w-[95vw] max-h-[95vh] overflow-hidden flex flex-col">
          <DialogHeader className="flex-shrink-0">
            <DialogTitle className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                Editar URLs da demanda do dia {}
                {hasUnsavedChanges && (
                  <Badge
                    variant="secondary"
                    className="bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200"
                  >
                    Não salvo
                  </Badge>
                )}
              </div>
              <div className="flex items-center gap-2 mt-6">
                <Badge variant="outline">{feedCount} Feed</Badge>
                <Badge variant="outline">{storyCount} Story</Badge>
                <Badge variant="outline">{validUrls.length} Total</Badge>
              </div>
            </DialogTitle>
          </DialogHeader>

          <div className="flex-1 grid grid-cols-1 lg:grid-cols-2 gap-6 overflow-hidden">
            <div className="space-y-4 overflow-y-auto pr-2">
              <Card>
                <CardContent className="p-4">
                  <Label className="text-sm font-medium">Pasta no Google Drive</Label>
                  <p className="text-xs text-muted-foreground mt-1 mb-3">
                    Insira a pasta no Google Drive que contém todas as imagens e vídeos desse conteúdo.
                  </p>
                  <Input
                    value={urlFolder || ""}
                    onChange={(e) => setUrlFolder(e.target.value)}
                    placeholder="https://drive.google.com/drive/folders/..."
                    disabled={feedCount <= 6 && storyCount <= 6}
                  />
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-4">
                  <Label className="text-sm font-medium">URLs do Google Drive</Label>

                  <p className="text-xs text-muted-foreground mb-4">
                    Cada URL representa um criativo diferente. Configure o formato e tipo de mídia.
                  </p>

                  {(feedCount > 6 || storyCount > 6) && !urlFolder && (
                    <div className="text-xs text-white dark:text-white/90 p-3 mb-4 bg-orange-500 rounded-lg flex items-start gap-2">
                      <TriangleAlert size={16} className="flex-shrink-0 mt-0.5" />
                      <p>Para adicionar mais de 6 URLs de Feed ou 6 de Story, insira a URL da pasta do Google Drive!</p>
                    </div>
                  )}

                  <div className="space-y-3 max-h-96 overflow-y-auto">
                    {urls.map((urlItem, index) => (
                      <Card key={urlItem.id} className="border-2 hover:border-primary/50 transition-colors">
                        <CardContent className="p-3">
                          <div className="flex items-start gap-3">
                            <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-lg flex-shrink-0 overflow-hidden">
                              {extractGoogleDriveId(urlItem.url) ? (
                                <Image
                                  src={`/api/drive-proxy?id=${extractGoogleDriveId(urlItem.mediaType === "video" && urlItem.thumbnailUrl ? urlItem.thumbnailUrl : urlItem.url)}&quality=low&size=small`}
                                  alt={`Preview ${index + 1}`}
                                  width={64}
                                  height={64}
                                  className="w-full h-full object-cover"
                                />
                              ) : (
                                <div className="w-full h-full flex items-center justify-center text-gray-400">
                                  <Eye size={16} />
                                </div>
                              )}
                            </div>

                            <div className="flex-1 space-y-2">
                              <div className="flex items-center gap-2">
                                <Badge variant="secondary" className="text-xs border-none">
                                  URL {index + 1}
                                </Badge>
                                <Badge variant={urlItem.type === "feed" ? "default" : "secondary"} className="border-none">
                                  {urlItem.type === "feed" ? "Feed" : "Story"}
                                </Badge>
                                <Badge variant="outline">{urlItem.mediaType === "foto" ? "Foto" : "Vídeo"}</Badge>
                              </div>

                              <Input
                                value={urlItem.url}
                                onChange={(e) => handleUrlChange(index, e.target.value)}
                                placeholder="https://drive.google.com/file/d/..."
                                className="text-sm"
                                disabled={(feedCount > 6 || storyCount > 6) && !urlFolder}
                              />

                              <div className="flex gap-2">
                                <Select
                                  value={urlItem.type}
                                  onValueChange={(value: "feed" | "story") => handleTypeChange(index, value)}
                                  disabled={(feedCount > 6 || storyCount > 6) && !urlFolder}
                                >
                                  <SelectTrigger className="w-24 h-8">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="feed">Feed</SelectItem>
                                    <SelectItem value="story">Story</SelectItem>
                                  </SelectContent>
                                </Select>

                                <Select
                                  value={urlItem.mediaType}
                                  onValueChange={(value: "foto" | "video") => handleMediaTypeChange(index, value)}
                                  disabled={(feedCount > 6 || storyCount > 6) && !urlFolder}
                                >
                                  <SelectTrigger className="w-24 h-8">
                                    <SelectValue />
                                  </SelectTrigger>
                                  <SelectContent>
                                    <SelectItem value="foto">Foto</SelectItem>
                                    <SelectItem value="video">Vídeo</SelectItem>
                                  </SelectContent>
                                </Select>

                                <Button
                                  variant="destructive"
                                  size="icon"
                                  onClick={() => removeUrl(index)}
                                  className="h-8 w-8"
                                  title="Remover URL"
                                >
                                  <Trash2 size={14} />
                                </Button>
                              </div>

                              {urlItem.mediaType === "video" && urlItem.type === "feed" && (
                                <div className="mt-2">
                                  <Label className="text-xs text-muted-foreground">URL da capa</Label>
                                  <Input
                                    value={urlItem.thumbnailUrl || ""}
                                    onChange={(e) => handleThumbnailUrlChange(index, e.target.value)}
                                    placeholder="https://drive.google.com/file/d/..."
                                    className="text-sm mt-1"
                                    disabled={(feedCount > 6 || storyCount > 6) && !urlFolder}
                                  />
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </CardContent>
                <CardFooter className="flex justify-end">
                  <Button
                    variant="outline"
                    onClick={addNewUrl}
                    size="sm"
                    disabled={(feedCount > 6 || storyCount > 6) && !urlFolder}
                  >
                    <Plus size={16} />
                    Adicionar URL
                  </Button>
                </CardFooter>
              </Card>
            </div>

            <div className="flex flex-col overflow-hidden">
              <Card className="flex-1 overflow-hidden">
                <CardContent className="p-4 h-full flex flex-col">
                  <div className="flex items-center justify-between mb-4">
                    <Label className="text-sm font-medium">
                      Visualização de todos os criativos ({validUrls.length})
                    </Label>
                    <div className="flex items-center gap-2">
                      <Button
                        variant={viewMode === "grid" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("grid")}
                      >
                        <Grid3X3 size={16} />
                      </Button>
                      <Button
                        variant={viewMode === "list" ? "default" : "outline"}
                        size="sm"
                        onClick={() => setViewMode("list")}
                      >
                        <List size={16} />
                      </Button>
                    </div>
                  </div>

                  <div className="flex-1 overflow-y-auto">
                    {validUrls.length === 0 ? (
                      <div className="h-full flex items-center justify-center bg-gray-50 dark:bg-gray-900 rounded-lg">
                        <div className="text-center text-gray-400">
                          <Eye size={48} className="mx-auto mb-2" />
                          <p className="text-sm">Adicione URLs para visualizar os criativos</p>
                        </div>
                      </div>
                    ) : (
                      <Tabs value={activeTab} onValueChange={setActiveTab} className="h-full flex flex-col">
                        <TabsList className="grid w-full grid-cols-3">
                          <TabsTrigger value="all">Todos ({validUrls.length})</TabsTrigger>
                          <TabsTrigger value="feed">
                            Feed ({validUrls.filter((u) => u.type === "feed").length})
                          </TabsTrigger>
                          <TabsTrigger value="story">
                            Story ({validUrls.filter((u) => u.type === "story").length})
                          </TabsTrigger>
                        </TabsList>

                        <TabsContent value="all" className="flex-1 mt-4">
                          <div className={viewMode === "grid" ? "grid grid-cols-2 xl:grid-cols-3 gap-4" : "space-y-4"}>
                            {validUrls.map((urlItem, index) => (
                              <Card key={urlItem.id} className="overflow-hidden">
                                <CardContent className="p-0">
                                  <div className={viewMode === "grid" ? "aspect-square" : "aspect-video"}>
                                    <Image
                                      src={`/api/drive-proxy?id=${extractGoogleDriveId(urlItem.mediaType === "video" && urlItem.thumbnailUrl ? urlItem.thumbnailUrl : urlItem.url)}&quality=medium&size=medium`}
                                      alt={`Criativo ${index + 1}`}
                                      width={viewMode === "grid" ? 300 : 500}
                                      height={viewMode === "grid" ? 300 : 300}
                                      className="w-full h-full object-cover"
                                    />
                                  </div>
                                  <div className="p-3">
                                    <div className="flex items-center justify-between">
                                      <div className="flex items-center gap-2">
                                        <Badge
                                          variant={urlItem.type === "feed" ? "default" : "secondary"}
                                          className="text-xs border-none"
                                        >
                                          {urlItem.type === "feed" ? "Feed" : "Story"}
                                        </Badge>
                                        <Badge variant="outline" className="text-xs">
                                          {urlItem.mediaType === "foto" ? "Foto" : "Vídeo"}
                                        </Badge>
                                      </div>
                                      <Link
                                        href={urlItem.url}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="text-blue-500 hover:text-blue-700"
                                      >
                                        <Eye size={16} />
                                      </Link>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        </TabsContent>

                        <TabsContent value="feed" className="flex-1 mt-4">
                          <div className={viewMode === "grid" ? "grid grid-cols-2 xl:grid-cols-3 gap-4" : "space-y-4"}>
                            {validUrls
                              .filter((u) => u.type === "feed")
                              .map((urlItem, index) => (
                                <Card key={urlItem.id} className="overflow-hidden">
                                  <CardContent className="p-0">
                                    <div className={viewMode === "grid" ? "aspect-square" : "aspect-video"}>
                                      <Image
                                        src={`/api/drive-proxy?id=${extractGoogleDriveId(urlItem.mediaType === "video" && urlItem.thumbnailUrl ? urlItem.thumbnailUrl : urlItem.url)}&quality=medium&size=medium`}
                                        alt={`Feed ${index + 1}`}
                                        width={viewMode === "grid" ? 300 : 500}
                                        height={viewMode === "grid" ? 300 : 300}
                                        className="w-full h-full object-cover"
                                      />
                                    </div>
                                    <div className="p-3">
                                      <div className="flex items-center justify-between">
                                        <Badge variant="outline" className="text-xs">
                                          {urlItem.mediaType === "foto" ? "Foto" : "Vídeo"}
                                        </Badge>
                                        <Link
                                          href={urlItem.url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-blue-500 hover:text-blue-700"
                                        >
                                          <Eye size={16} />
                                        </Link>
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                          </div>
                        </TabsContent>

                        <TabsContent value="story" className="flex-1 mt-4">
                          <div className={viewMode === "grid" ? "grid grid-cols-2 xl:grid-cols-3 gap-4" : "space-y-4"}>
                            {validUrls
                              .filter((u) => u.type === "story")
                              .map((urlItem, index) => (
                                <Card key={urlItem.id} className="overflow-hidden">
                                  <CardContent className="p-0">
                                    <div className={viewMode === "grid" ? "aspect-[9/16]" : "aspect-[9/16]"}>
                                      <Image
                                        src={`/api/drive-proxy?id=${extractGoogleDriveId(urlItem.mediaType === "video" && urlItem.thumbnailUrl ? urlItem.thumbnailUrl : urlItem.url)}&quality=medium&size=medium`}
                                        alt={`Story ${index + 1}`}
                                        width={viewMode === "grid" ? 200 : 300}
                                        height={viewMode === "grid" ? 356 : 533}
                                        className="w-full h-full object-cover"
                                      />
                                    </div>
                                    <div className="p-3">
                                      <div className="flex items-center justify-between">
                                        <Badge variant="outline" className="text-xs">
                                          {urlItem.mediaType === "foto" ? "Foto" : "Vídeo"}
                                        </Badge>
                                        <Link
                                          href={urlItem.url}
                                          target="_blank"
                                          rel="noopener noreferrer"
                                          className="text-blue-500 hover:text-blue-700"
                                        >
                                          <Eye size={16} />
                                        </Link>
                                      </div>
                                    </div>
                                  </CardContent>
                                </Card>
                              ))}
                          </div>
                        </TabsContent>
                      </Tabs>
                    )}
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>

          <DialogFooter className="flex-shrink-0">
            <Button variant="secondary" onClick={handleCloseModal}>
              Cancelar
            </Button>
            <Button onClick={handleUpdateUrl} disabled={isUpdating}>
              {isUpdating ? (
                <>
                  <Ellipsis className="animate-pulse" />
                  Salvando
                </>
              ) : (
                <>Salvar URLs</>
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
