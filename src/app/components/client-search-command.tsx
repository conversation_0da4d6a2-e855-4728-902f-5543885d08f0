"use client"

import { useState, useEffect } from "react"
import { Search, History, User } from "lucide-react"
import { useRouter } from "next/navigation"
import { But<PERSON> } from "@/app/components/ui/button"
import {
    CommandDialog,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
    CommandSeparator,
} from "@/app/components/ui/command"

interface Client {
    id: string
    name: string
}

interface ClientSearchCommandProps {
    clients: Client[]
    onClientSelect?: (clientId: string, clientName: string) => void
}

export function ClientSearchCommand({ clients, onClientSelect }: ClientSearchCommandProps) {
    const [open, setOpen] = useState(false)
    const [searchQuery, setSearchQuery] = useState("")
    const [recentClients, setRecentClients] = useState<Client[]>([])
    const router = useRouter()

    useEffect(() => {
        const down = (e: KeyboardEvent) => {
            if (e.key === "k" && (e.metaKey || e.ctrlKey)) {
                e.preventDefault()
                setOpen((open) => !open)
            }
        }

        document.addEventListener("keydown", down)
        return () => document.removeEventListener("keydown", down)
    }, [])

    useEffect(() => {
        const viewedClients = JSON.parse(localStorage.getItem("viewedClients") || "[]");
        setRecentClients(viewedClients);
    }, [open]);

    const handleSelectClient = (clientId: string, clientName: string) => {
        const viewedClients = JSON.parse(localStorage.getItem("viewedClients") || "[]")

        const filteredClients = viewedClients.filter((client: { id: string }) => client.id !== clientId)

        const updatedClients = [{ id: clientId, name: clientName }, ...filteredClients].slice(0, 5)

        localStorage.setItem("viewedClients", JSON.stringify(updatedClients))
        setRecentClients(updatedClients)

        setOpen(false)

        if (onClientSelect) {
            onClientSelect(clientId, clientName)
        } else {
            router.push(`/clients/${clientId}`)
        }
    }

    const filteredClients = clients.filter((client) =>
        client.name.toLowerCase().includes(searchQuery.toLowerCase())
    )

    const handleOpenChange = (open: boolean) => {
        setOpen(open)
        if (!open) {
            setSearchQuery("")
        }
    }

    return (
        <div className="mt-6 w-full">
            <Button
                variant="outline"
                className="relative h-9 w-full sm:w-96 justify-start rounded-[0.5rem] text-sm text-muted-foreground sm:pr-12"
                onClick={() => setOpen(true)}
            >
                <Search />
                <span className="hidden lg:inline-flex">Buscar cliente...</span>
                <span className="inline-flex lg:hidden">Buscar...</span>
                <kbd className="pointer-events-none absolute right-1.5 top-1.5 hidden h-5 select-none items-center gap-1 rounded border bg-muted px-1.5 font-mono text-[10px] font-medium opacity-100 sm:flex">
                    <span className="text-xs">⌘</span>K
                </kbd>
            </Button>
            <CommandDialog open={open} onOpenChange={handleOpenChange}>
                <CommandInput
                    placeholder="Buscar clientes..."
                    value={searchQuery}
                    onValueChange={setSearchQuery}
                />
                <CommandList>
                    <CommandEmpty>Nenhum cliente encontrado.</CommandEmpty>

                    {recentClients.length > 0 && searchQuery === "" && (
                        <>
                            <CommandGroup heading="Recentes">
                                {recentClients.map((client) => (
                                    <CommandItem
                                        key={client.id}
                                        onSelect={() => handleSelectClient(client.id, client.name)}
                                    >
                                        <History className="!h-4 !w-4 text-muted-foreground" />
                                        {client.name}
                                    </CommandItem>
                                ))}
                            </CommandGroup>
                            <CommandSeparator />
                        </>
                    )}

                    <CommandGroup heading="Todos os clientes">
                        {filteredClients.map((client) => (
                            <CommandItem
                                key={client.id}
                                onSelect={() => handleSelectClient(client.id, client.name)}
                            >
                                <User className="!h-4 !w-4 text-muted-foreground" />
                                {client.name}
                            </CommandItem>
                        ))}
                    </CommandGroup>
                </CommandList>
            </CommandDialog>
        </div>
    )
}
