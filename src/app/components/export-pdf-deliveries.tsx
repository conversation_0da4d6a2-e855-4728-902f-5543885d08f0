"use client"

import { But<PERSON> } from './ui/button';
import { FileText } from 'lucide-react';
import { toast } from 'sonner';

const escapeHtml = (input?: string | null) => {
    if (!input) return '';
    return String(input)
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#039;');
};

const extractGoogleDriveId = (url?: string | null) => {
    if (!url) return null;
    const cleanUrl = String(url).trim();
    const patterns = [
        /\/file\/d\/([a-zA-Z0-9_-]+)/,
        /\/d\/([a-zA-Z0-9_-]+)/,
        /[?&]id=([a-zA-Z0-9_-]+)/,
        /open\?id=([a-zA-Z0-9_-]+)/,
        /thumbnail\?id=([a-zA-Z0-9_-]+)/,
        /uc\?.*id=([a-zA-Z0-9_-]+)/,
        /\/([a-zA-Z0-9_-]{25,})/
    ];

    for (let i = 0; i < patterns.length; i++) {
        const match = cleanUrl.match(patterns[i]);
        if (match && match[1] && match[1].length >= 25) return match[1];
    }

    return null;
};

export function ExportPDFDeliveries({ clientId, clientName, selectedIds }: { clientId: string; clientName?: string | null; selectedIds?: string[] }) {
    const handleExport = async () => {
        if (!clientId) {
            toast.error('Cliente inválido');
            return;
        }

        if (!selectedIds || selectedIds.length === 0) {
            toast.error('Nenhuma entrega selecionada para exportar.');
            return;
        }

        // open a preview window immediately (prevents popup blocking on Safari)
        let previewWindow: Window | null = null;
        try {
            try {
                previewWindow = window.open('', '_blank');
                if (previewWindow) {
                    previewWindow.document.write(`<!doctype html><html><head><meta charset="utf-8"><title>Preparando PDF</title></head><body style="font-family:Arial,Helvetica,sans-serif;padding:24px;"><h3>Preparando PDF</h3><p>Aguarde enquanto o arquivo é gerado.</p></body></html>`);
                    previewWindow.document.close();
                    previewWindow.focus();
                }
            } catch { }

            const res = await fetch(`/api/clients/${encodeURIComponent(clientId)}?include=monthlyPlannings.activities.contents`);
            if (!res.ok) {
                toast.error('Falha ao buscar entregas do cliente');
                return;
            }

            const data = await res.json();
            interface ContentShape {
                id: string;
                activityDate?: string | null;
                contentType?: string | null;
                destination?: string | null;
                details?: string | null;
                caption?: string | null;
                urlStructuringFeed?: string | string[] | null;
            }

            interface PlanningShape {
                id: string;
                month: number;
                year: number;
                status?: string | null;
                activities?: Array<{ contents?: ContentShape[] } | null> | null;
            }

            const allContents: Array<ContentShape & { planningMonth?: number; planningYear?: number; planningId?: string }> = [];

            if (data.monthlyPlannings && Array.isArray(data.monthlyPlannings)) {
                (data.monthlyPlannings as PlanningShape[]).forEach((planning) => {
                    if (planning.status === 'aprovado' && Array.isArray(planning.activities)) {
                        (planning.activities || []).forEach((activity) => {
                            if (activity && Array.isArray(activity.contents)) {
                                (activity.contents || []).forEach((content) => {
                                    if (content) {
                                        allContents.push({ ...content, planningMonth: planning.month, planningYear: planning.year, planningId: planning.id });
                                    }
                                });
                            }
                        });
                    }
                });
            }

            const filtered = allContents.filter(c => selectedIds!.includes(c.id));

            if (filtered.length === 0) {
                toast.error('Nenhuma das entregas selecionadas foi encontrada para exportação.');
                return;
            }

            const monthsPt = ['janeiro', 'fevereiro', 'março', 'abril', 'maio', 'junho', 'julho', 'agosto', 'setembro', 'outubro', 'novembro', 'dezembro'];
            const planningKeys = Array.from(new Set(filtered.map(f => `${f.planningMonth ?? 0}-${f.planningYear ?? 0}`)));
            let coverMonthLabel = '';
            if (planningKeys.length === 1) {
                const [pm, py] = planningKeys[0].split('-');
                const pmNum = Number(pm);
                if (pmNum >= 1 && pmNum <= 12) coverMonthLabel = `${monthsPt[pmNum - 1]} ${py}`;
                else coverMonthLabel = `${py}`;
            } else {
                coverMonthLabel = 'Vários meses';
            }

            const coverClientName = (clientName && String(clientName).trim()) ? String(clientName).trim() : ((data && (data.name || data.clientName)) ? String((data.name || data.clientName)).trim() : 'Cliente');

            const coverHtml = `
                <div style="page-break-after: always; position:relative; display:flex; flex-direction:column; align-items:center; justify-content:center; min-height:100vh; box-sizing:border-box; padding-top:0; margin-top:-20px;">
                    <div style="position:absolute; top:20px; left:20px; font-family: 'Quicksand', Arial, sans-serif; font-size:22px; color:#222; font-weight:700; border-bottom: 1px solid gray; width:100%;">
                        <div style="display:flex; gap:8px; align-items:center; margin-bottom:12px;">
                            <div style="border: 1px solid #db5743; padding: 6px 8px; border-radius: 16px; background: #e64729; background-color:#e64729; color: #fff; font-size:12px; -webkit-print-color-adjust:exact; print-color-adjust:exact; margin-bottom:4px;">
                                Etapa 07
                            </div>
                            <div style="border: 1px solid #db5743; padding: 6px 8px; border-radius: 16px; background: #fff7f5; background-color:#fff7f5; color: #e64729; font-size:12px; -webkit-print-color-adjust:exact; print-color-adjust:exact; margin-bottom:4px;">
                                Entregas
                            </div>
                        </div>
                        <h1 style="text-transform:uppercase; font-size:20px; color: #e64729; padding-bottom: 6px;">
                            Entregas de materiais
                        </h1>
                    </div>

                    <img src="/logo-rodapé-17-b4(1).png" alt="Logo" style="max-width:120px; width:30%; height:auto; margin-bottom:24px;"/>
                    <div style="font-family: 'Quicksand', Arial, sans-serif; font-size:28px; color:#e64729; font-weight:600; margin-bottom:8px;">
                        ${escapeHtml(coverClientName)}
                    </div>
                    <div style="font-family: 'Quicksand', Arial, sans-serif; font-size:18px; color:#444;">${escapeHtml(coverMonthLabel)}</div>
                    <div style="position:absolute; right:20px; bottom:12px; font-size:12px; color:#666; display:flex; align-items:center; gap:8px;">
                        <span style="font-size:12px; color:#666;">Feito com B4Desk</span>
                        <img src="/icon-b4desk.png" alt="B4Desk" style="width:18px; height:auto; display:block;" />
                    </div>
                </div>
            `;

            const rowsHtml = filtered.map(c => {
                const structUrls = Array.isArray(c.urlStructuringFeed) ? c.urlStructuringFeed.filter((u): u is string => !!u && typeof u === 'string') : (c.urlStructuringFeed ? [c.urlStructuringFeed] : []);
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
                const content = c as ContentShape & Record<string, any>;
                const thumbUrls = Array.isArray(content.urlThumbnails) ? (content.urlThumbnails as string[]).filter(u => !!u) : [];

                const urlTypesArr = Array.isArray(content.urlTypes) ? (content.urlTypes as string[]) : [];
                const mediaTypesArr = Array.isArray(content.urlMediaTypes) ? (content.urlMediaTypes as string[]) : [];

                type EntryItem = { url: string; thumbnail?: string | null; declaredType?: string | null; mediaType?: string | null; urlType?: string | null };
                const entries: EntryItem[] = [];

                for (let i = 0; i < structUrls.length; i++) {
                    const u = structUrls[i];
                    const thumb = thumbUrls[i] ?? null;
                    const media = mediaTypesArr[i] ?? 'foto';
                    const urlType = urlTypesArr[i] ?? 'feed';
                    entries.push({ url: u, thumbnail: thumb, declaredType: media, mediaType: media, urlType });
                }
                // include leftover thumbnails
                thumbUrls.forEach(t => { if (!structUrls.includes(t)) entries.push({ url: t, thumbnail: t, declaredType: 'foto', mediaType: 'foto', urlType: 'feed' }); });

                const detectVideo = (mediaType?: string | null, url?: string | null) => {
                    if (mediaType) {
                        const t = String(mediaType).toLowerCase();
                        if (t.includes('video') || t.includes('vídeo')) return true;
                    }
                    if (url) {
                        const u = String(url).toLowerCase();
                        if (u.endsWith('.mp4') || u.endsWith('.mov') || u.endsWith('.webm') || u.includes('youtube.com') || u.includes('vimeo.com')) return true;
                    }
                    return false;
                };

                const isImageUrl = (url?: string | null) => {
                    if (!url) return false;
                    const u = String(url).toLowerCase();
                    return u.endsWith('.jpg') || u.endsWith('.jpeg') || u.endsWith('.png') || u.endsWith('.webp') || u.endsWith('.gif') || u.endsWith('.svg');
                };

                const storyImagesParts: string[] = [];
                const feedImagesParts: string[] = [];
                const videoPartsArr: Array<{ url: string; group: 'story' | 'feed' }> = [];

                const STORY_LIMIT = 6;
                const FEED_LIMIT = 6;
                let storyDisplayed = 0;
                let feedDisplayed = 0;
                const usedStory = new Set<string>();
                const usedFeed = new Set<string>();

                const process = (entry: EntryItem, group: 'story' | 'feed') => {
                    const target = group === 'story' ? storyImagesParts : feedImagesParts;
                    const used = group === 'story' ? usedStory : usedFeed;

                    const orig = entry.url;
                    const thumb = entry.thumbnail ?? null;
                    const media = entry.mediaType ?? entry.declaredType ?? null;
                    const isVid = detectVideo(media, orig);

                    if (isVid) {
                        videoPartsArr.push({ url: orig, group });
                        // try thumbnail
                        if (thumb && !used.has(thumb)) {
                            if ((group === 'story' && storyDisplayed < STORY_LIMIT) || (group === 'feed' && feedDisplayed < FEED_LIMIT)) {
                                const driveId = extractGoogleDriveId(thumb);
                                if (driveId) {
                                    target.push(`<div style="position:relative;display:inline-block"><img class=\"pdf-img\" src=\"/api/drive-proxy?id=${encodeURIComponent(driveId)}&quality=high&size=large\" alt=\"Capa do vídeo\"/><img src=\"/play.png\" style=\"position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:70px;height:70px;pointer-events:none;\" alt=\"Play\"/></div>`);
                                } else if (isImageUrl(thumb)) {
                                    target.push(`<div style="position:relative;display:inline-block"><img class=\"pdf-img\" src=\"${escapeHtml(thumb)}\" alt=\"Capa do vídeo\"/><img src=\"/play.png\" style=\"position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:70px;height:70px;pointer-events:none;\" alt=\"Play\"/></div>`);
                                } else {
                                    target.push(`<a class=\"pdf-link\" href=\"${escapeHtml(thumb)}\" target=\"_blank\" rel=\"noopener noreferrer\">Abrir capa</a>`);
                                }
                                used.add(thumb);
                                if (group === 'story') storyDisplayed++; else feedDisplayed++;
                            }
                        } else {
                            // no thumb: try drive id from video
                            if (!used.has(orig)) {
                                const driveId = extractGoogleDriveId(orig);
                                if (driveId && ((group === 'story' && storyDisplayed < STORY_LIMIT) || (group === 'feed' && feedDisplayed < FEED_LIMIT))) {
                                    target.push(`<div style="position:relative;display:inline-block"><img class=\"pdf-img\" src=\"/api/drive-proxy?id=${encodeURIComponent(driveId)}&quality=high&size=large\" alt=\"Vídeo\"/><img src=\"/play.png\" style=\"position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:70px;height:70px;pointer-events:none;\" alt=\"Play\"/></div>`);
                                    used.add(orig);
                                    if (group === 'story') storyDisplayed++; else feedDisplayed++;
                                }
                            }
                        }
                        return;
                    }

                    const display = thumb || orig;
                    if (group === 'story' && storyDisplayed >= STORY_LIMIT) return;
                    if (group === 'feed' && feedDisplayed >= FEED_LIMIT) return;
                    if (used.has(display)) return;

                    const driveId = extractGoogleDriveId(display);
                    if (driveId) {
                        target.push(`<img class=\"pdf-img\" src=\"/api/drive-proxy?id=${encodeURIComponent(driveId)}&quality=high&size=large\" alt=\"Imagem\"/>`);
                        used.add(display);
                        if (group === 'story') storyDisplayed++; else feedDisplayed++;
                        return;
                    }

                    if (isImageUrl(display)) {
                        target.push(`<img class=\"pdf-img\" src=\"${escapeHtml(display)}\" alt=\"Imagem\"/>`);
                        used.add(display);
                        if (group === 'story') storyDisplayed++; else feedDisplayed++;
                        return;
                    }

                    // fallback link
                    target.push(`<a class=\"pdf-link\" href=\"${escapeHtml(display)}\" target=\"_blank\" rel=\"noopener noreferrer\">Abrir URL</a>`);
                    used.add(display);
                };

                const storyEntries = entries.filter(e => String(e.urlType || '').toLowerCase() === 'story');
                const feedEntries = entries.filter(e => String(e.urlType || '').toLowerCase() !== 'story');

                storyEntries.forEach(e => process(e, 'story'));
                feedEntries.forEach(e => process(e, 'feed'));

                // if overflow, add folder link per group
                const folder = content.urlFolder;
                if (storyEntries.length > STORY_LIMIT && folder) storyImagesParts.push(`<a class="pdf-link" href="${escapeHtml(folder)}" target="_blank" rel="noopener noreferrer">Ver mais imagens na pasta (story)</a>`);
                if (feedEntries.length > FEED_LIMIT && folder) feedImagesParts.push(`<a class="pdf-link" href="${escapeHtml(folder)}" target="_blank" rel="noopener noreferrer">Ver mais imagens na pasta (feed)</a>`);

                // always prepare a folder button for header (we'll place it in the header instead of below images)
                const headerFolderHtml = folder ? `<a class=\"pdf-link\" href=\"${escapeHtml(folder)}\" target=\"_blank\" rel=\"noopener noreferrer\" style=\"border:1px solid gray; border-radius:4px; padding:6px 10px; text-decoration:none;\">Abrir pasta</a>` : '';

                let imagesHtml = '';
                let captionPrinted = false;
                // if only one image displayed in total, render it centered and larger
                const totalDisplayed = storyDisplayed + feedDisplayed;
                if (totalDisplayed === 1) {
                    const single = storyImagesParts.length ? storyImagesParts.find(s => !!s) : (feedImagesParts.length ? feedImagesParts.find(s => !!s) : null);
                    if (single) {
                        // render single image smaller and centered with its caption below, keep on one page
                        const singleWithStyle = String(single).replace(/<img([^>]*)>/i, '<img$1 style="max-height:65vh;width:auto;max-width:100%;" />');
                        imagesHtml = `
                            <div style="page-break-inside:avoid; display:flex;flex-direction:column;align-items:center;justify-content:center;margin-top:12px;">
                                <div style="max-width:360px;">
                                    ${singleWithStyle}
                                </div>
                                <div style="margin-top:10px; font-size:12px; color:#444; text-align:center; max-width:48%;">${escapeHtml(c.caption ?? '')}</div>
                            </div>
                        `;
                        captionPrinted = true;
                    }
                } else {
                    if (storyImagesParts.length) imagesHtml += `<div class="images-container"><div class="images images-story">${storyImagesParts.join('')}</div></div>`;
                    if (feedImagesParts.length) imagesHtml += `<div class="images-container"><div class="images images-feed">${feedImagesParts.join('')}</div></div>`;
                }

                // video links grouped: keep all buttons inline (same row)
                let videoLinksHtml = '';
                const storyVideos = videoPartsArr.filter(v => v.group === 'story');
                const feedVideos = videoPartsArr.filter(v => v.group === 'feed');
                if (storyVideos.length) videoLinksHtml += storyVideos.map((v, i) => `<a class=\"video-link\" href=\"${escapeHtml(v.url)}\" target=\"_blank\" rel=\"noopener noreferrer\">${storyVideos.length > 1 ? `Abrir vídeo ${i + 1}` : 'Abrir vídeo'}</a>`).join('');
                if (feedVideos.length) videoLinksHtml += feedVideos.map((v, i) => `<a class=\"video-link\" href=\"${escapeHtml(v.url)}\" target=\"_blank\" rel=\"noopener noreferrer\">${feedVideos.length > 1 ? `Abrir vídeo ${i + 1}` : 'Abrir vídeo'}</a>`).join('');

                const date = c.activityDate ? new Date(c.activityDate).toLocaleDateString() : '';

                return `
                    <div style="page-break-inside: avoid; margin-bottom: 22px; font-family: Arial, Helvetica, sans-serif; color: #222;">
                        <div style="display:flex;justify-content:space-between;align-items:center;">
                            <div style="margin:0; font-weight:bold; font-size:14px; border: 1px solid #db5743; padding: 6px 8px; margin: 4px 0; border-radius: 16px; background: #fff7f5; color: #e64729;">
                                ${escapeHtml((date || ''))}
                            </div>
                            <div>
                                ${headerFolderHtml}
                            </div>
                        </div>
                        ${videoLinksHtml ? `<div style="display:flex;gap:6px;align-items:center;flex-wrap:nowrap;">${videoLinksHtml}</div>` : ''}
                        <div style="display:block; margin-top:4px;">
                            ${imagesHtml || `<div style='width:240px;height:160px;background:#f3f4f6;display:flex;align-items:center;justify-content:center;color:#666'>Sem imagem</div>`}
                        </div>
                        ${!captionPrinted && c.caption ? `<div style="margin-top:8px; font-size:14px; color:#444;">${escapeHtml(c.caption ?? '')}</div>` : ''}
                    </div>
                `;
            }).join('\n');

            const title = coverClientName ? `${escapeHtml(coverClientName)} - entregas - ${coverMonthLabel}` : `entregas`;

            const html = `<!doctype html>
                <html>
                    <head>
                        <meta charset="utf-8">
                        <title>${escapeHtml(title)}</title>
                        <link href="https://fonts.googleapis.com/css2?family=Quicksand:wght@300;400;500;700&display=swap" rel="stylesheet">
                        <style>
                            @page { margin: 12mm; }
                            /* print-friendly image grid: force a single row per group (no wrap) and fit 6 images per line */
                            
                            * {
                                margin: 0;
                                padding: 0;
                                box-sizing: border-box;
                            }
                            
                            .images { display:flex; gap:8px; flex-wrap:nowrap; align-items:flex-start; overflow:hidden; width:100%; }
                            /* make each direct child occupy 1/6 of the container (accounting for gaps) */

                            .images > * { flex: 0 0 calc((100% - 5 * 8px) / 6); max-width: calc((100% - 5 * 8px) / 6); box-sizing: border-box; }
                            .pdf-img { width:100%; height:auto; object-fit:contain; display:block; margin:0; }
                            .images-container { margin-bottom: 8px; }
                            .images-container + .images-container { margin-top: 8px; }
                            .pdf-link { display:inline-block; padding:6px 8px; background:#f3f4f6; background-color:#f3f4f6; color:#333; margin:4px; text-decoration:none; font-size:12px; box-sizing: border-box; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
                            .video-link { display:inline-block; padding:8px 10px; background:#db5743; background-color:#db5743; color:#fff; text-decoration:none; font-size:12px; border-radius:4px; -webkit-print-color-adjust: exact; print-color-adjust: exact; }
                            @media print { .pdf-img, .pdf-link, .video-link { -webkit-print-color-adjust: exact; print-color-adjust: exact; } }
                            body { font-family: 'Quicksand', Arial, sans-serif; color: #222; padding: 20px; }
                            .header { text-align: center; margin-bottom: 18px; }
                            .content-item { border-bottom: 1px solid #eee; padding-bottom: 12px; margin-bottom: 12px; }
                        </style>
                    </head>
                    <body>
                        ${coverHtml}
                        
                        <div>
                            ${rowsHtml || '<p>Nenhuma entrega encontrada.</p>'}
                        </div>
                    </body>
                    <script>
                        (function(){
                            const timeoutMs = 3000;
                            function waitForImages(ms){
                                return new Promise(function(resolve){
                                    try{
                                        const imgs = Array.from(document.images || []);
                                        if (!imgs || imgs.length === 0) return resolve();
                                        let remaining = imgs.length;
                                        const check = function(){ remaining--; if (remaining <= 0) resolve(); };
                                        imgs.forEach(function(img){
                                            if (img.complete) { check(); return; }
                                            img.addEventListener('load', check, { once: true });
                                            img.addEventListener('error', check, { once: true });
                                        });
                                        setTimeout(resolve, ms);
                                    }catch(e){ resolve(); }
                                });
                            }

                            waitForImages(timeoutMs).then(function(){
                                try { window.focus(); window.print(); } catch(e) { /* ignore */ }
                            });
                        })();
                    </script>
                </html>`;

            const w = previewWindow ?? window.open('', '_blank');
            if (w) {
                try {
                    w.document.open();
                    w.document.write(html);
                    w.document.close();
                } catch { try { /* ignore cross-window write errors */ } catch { } }

                    try {
                        try { w.focus(); } catch { }
                        // printing is handled by the script injected into the preview window which waits for images to load
                    } catch { }
            }

            toast.success('PDF preparado — use a função de impressão do navegador para salvar como PDF');
        } catch (err) {
            console.error(err);
            toast.error('Erro ao preparar PDF');
        }
    };

    return (
        <Button title="Exportar entregas para PDF" onClick={handleExport} className='w-full sm:w-auto'>
            <FileText size={16} />
            Exportar PDF {selectedIds && selectedIds.length > 0 ? `(${selectedIds.length} itens)` : ''}
        </Button>
    );
}

export default ExportPDFDeliveries;
