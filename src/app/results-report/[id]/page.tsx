"use client";

import { use<PERSON><PERSON>back, useEffect, useMemo, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { useParams } from 'next/navigation';
import { Header } from '../../components/header';
import { Footer } from '../../components/footer';
import { Button } from '../../components/ui/button';
import { FileChartColumn, InfoIcon, MoveLeft, CirclePlus, Calendar1, Edit, Ellipsis, Settings2, AppWindowMac } from 'lucide-react';
import Loading from '../../components/ui/loading';
import { NotAllowed } from '../../components/not-allowed';
import Link from 'next/link';
import Image from 'next/image';
import { Badge } from '@/app/components/ui/badge';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/app/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import { Separator } from '@/app/components/ui/separator';
import { FollowersComparisonChart } from '@/app/components/results/followers-comparison-chart';
import { TotalInteractions } from '@/app/components/results/total-interactions';
import { FollowersBarChart } from '@/app/components/results/followers-bar-chart';
import { TotalMetricChart } from '@/app/components/results/total-metric-chart';
import { AboutResults } from '@/app/components/about-results';
import CreateResultsReportForm from '@/app/components/results/create-results-report-form';
import { PlatformGrowthChart } from '@/app/components/results/platform-growth-chart';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/app/components/ui/card';
import generateResultsPDF from '@/app/components/generate-results-pdf';
import { Checkbox } from '@/app/components/ui/checkbox';
import { Label } from '@/app/components/ui/label';
import DetailedReachMetrics from '@/app/components/results/detailed-reach-metrics';
import PostMetricsChart from '@/app/components/results/post-metrics-chart';
import ReelMetricsChart from '@/app/components/results/reel-metrics-chart';
import ContentInteractionRateChart from '@/app/components/results/content-interaction-rate-chart';
import InteractionPercentageChart from '@/app/components/results/interaction-percentage-chart';
import VideoMetricsChart from '@/app/components/results/video-metrics-chart';
import ContentInteractionRatesChart from '@/app/components/results/content-interaction-rates-chart';
import { VisualizationMetrics } from '@/app/components/results/visualization-metrics';
import { ClientNavigationModal } from '@/app/components/client-navigation-modal';
import FollowersDataSection from '@/app/components/results/followers-data-section';

interface Client {
    id: string;
    name: string;
    instagramUsername?: string;
    phone?: string;
    monthlyPostsLimit?: number;
    monthlyStoriesLimit?: number;
    additionalContent?: number;
    resultsReport?: ResultsReport[];
}

interface ResultsReport {
    id: string;
    clientId: string;
    month: number;
    year: number;
    createdAt: string;
    updatedAt: string;
    results: Result[];
}

interface Result {
    id: string;
    resultsReportId: string;
    createdAt: string;

    // Métricas básicas
    newFollowers?: number;
    totalPosts?: number;
    totalInteractions?: number;
    totalStories?: number;
    totalViews?: number;



    // Métricas de alcance e perfil
    reachNonFollowers: number;
    profileActivity: number;
    websiteClicks: number;
    profileVisits: number;
    percentageProfileActivity: number;
    reachFollowers: number;
    accountsReached?: number; // Antes era totalReach

    // Aba Visualizações - novos campos
    percentageAds?: number;
    percentageFollowers?: number;
    percentageNonFollowers?: number;
    percentageAccountsReached?: number;
    percentageProfileVisits?: number;
    externalLinkTaps?: number;
    percentageExternalLinkTaps?: number;
    businessAddressTaps?: number;
    percentageBusinessAddressTaps?: number;

    // Aba Interações - novos campos
    interactionsPercentageAds?: number;
    interactionsPercentageFollowers?: number;
    interactionsPercentageNonFollowers?: number;

    // Reel
    reelInteractionPercentage?: number;

    // Publicações
    postInteractionPercentage?: number;

    // Story
    storyInteractionPercentage?: number;

    // Vídeos ao vivo
    liveInteractionPercentage?: number;

    // Vídeos
    videoInteractionPercentage?: number;
    videoLikes?: number;
    videoComments?: number;
    videoSaves?: number;
    videoShares?: number;

    // Métricas de distribuição - novos campos
    liveComments?: number;

    // Métricas de Posts - novos campos
    postLikes?: number;
    postComments?: number;
    postSaves?: number;
    postShares?: number;

    // Métricas de Stories - novos campos
    storyReplies?: number;

    // Métricas de Reels - novos campos
    reelLikes?: number;
    reelComments?: number;
    reelSaves?: number;
    reelShares?: number;

    // Dados da série histórica e outros
    reachByPostType?: Record<string, number>;
    interactionsDistribution?: Record<string, number>;
    averageInteractionsPerPost?: number;
    averageStoryViews?: number;

    // Aba 4 - Seguidores (novos campos adicionados)
    totalFollowers?: number;
    unfollowCount?: number;
    followersLocation?: Record<string, number>;
    followersAgeRange?: Record<string, number>;
    followersGender?: Record<string, number>;
}

export default function ResultsReportPage() {
    const { data: session, status } = useSession();
    const [isAdmin, setIsAdmin] = useState(false);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [client, setClient] = useState<Client | null>(null);
    const [selectedMonth, setSelectedMonth] = useState<string | null>(() => {
        return new Date().toLocaleString("pt-BR", { month: "long" }).toLowerCase();
    });
    const [isPdfLoading, setIsPdfLoading] = useState(false);
    const [selectedCharts, setSelectedCharts] = useState<{ [key: string]: boolean }>({
        followers: true,
        content: true,
        interactions: true,
        detailedInteractions: true,
        visualizations: true
    });
    const [showChartSelector, setShowChartSelector] = useState(false);
    const [compareMode, setCompareMode] = useState<'previous' | 'custom'>('previous');
    const [customCompareMonth, setCustomCompareMonth] = useState<string | null>(null);
    const [clientNavigationOpen, setClientNavigationOpen] = useState(false);
    const [selectedClientId, setSelectedClientId] = useState<string | null>(null);
    const [viewedClients, setViewedClients] = useState<{ id: string; name: string }[]>([]);
    const monthNames = useMemo(() => [
        "Janeiro", "Fevereiro", "Março", "Abril",
        "Maio", "Junho", "Julho", "Agosto",
        "Setembro", "Outubro", "Novembro", "Dezembro",
    ], []);
    const monthName = useCallback((month: number) => monthNames[month - 1], [monthNames]);

    const router = useRouter();
    const params = useParams();
    const id = params?.id as string;

    const [isFormOpen, setIsFormOpen] = useState(false);
    const [editingReport, setEditingReport] = useState<ResultsReport | null>(null);

    const refreshClientData = useCallback(() => {
        if (id) {
            fetch(`/api/clients/${id}?include=resultsReport.results`)
                .then(res => res.json())
                .then(data => {
                    setClient(data);
                    setIsFormOpen(false);
                })
                .catch(error => console.error("Erro ao buscar dados do cliente:", error));
        }
    }, [id]);

    const handleOpenClientNavigation = (clientId: string) => {
        setSelectedClientId(clientId);
        setClientNavigationOpen(true);
    };

    useEffect(() => {
        const recentClients = JSON.parse(localStorage.getItem('viewedClients') || '[]');
        setViewedClients(recentClients);
    }, []);

    useEffect(() => {
        if (status === "authenticated") {
            if (id) {
                fetch(`/api/clients/${id}?include=resultsReport.results`)
                    .then(res => res.json())
                    .then(data => {
                        setClient(data);
                    })
                    .catch(error => console.error("Erro ao buscar dados do cliente:", error))
                    .finally(() => setIsFetchingRole(false));
            }

            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const response = await fetch(`/api/users/${session.user.email}`);
                        if (response.ok) {
                            const user = await response.json();
                            setIsAdmin(["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"].includes(user?.role || ""));
                        }
                    }
                } catch (error) {
                    console.error("Erro ao buscar função do usuário:", error);
                } finally {
                    setIsFetchingRole(false);
                }
            };

            fetchUserRole();
        } else if (status === 'unauthenticated') {
            router.push('/');
        }
    }, [status, session, router, id]);

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:px-8 xs:pt-4 xs:pb-8 flex-grow">
                {isFetchingRole || !client ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <>
                        <div className="flex justify-between items-center">
                            <div className="flex items-start xs:items-center justify-between w-full gap-2">
                                <div className="flex items-center gap-2">
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => router.push('/clients')}
                                    >
                                        <MoveLeft className="h-4 w-4" />
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="icon"
                                        onClick={() => handleOpenClientNavigation(client.id)}
                                    >
                                        <AppWindowMac className="h-4 w-4" />
                                    </Button>
                                    <ClientNavigationModal
                                        open={clientNavigationOpen}
                                        onOpenChange={setClientNavigationOpen}
                                        clientId={selectedClientId}
                                        clientName={viewedClients.find(client => client.id === selectedClientId)?.name}
                                    />
                                </div>
                                <div className="flex flex-col xs:flex-row items-end xs:items-center gap-2 group">
                                    <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                        Relatórios de resultados
                                    </h1>
                                    <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                        <FileChartColumn size={24} color="#db5743" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div>
                            <h2 className="mt-4 text-lg font-semibold">{client.name}</h2>
                            <Link
                                href={`https://www.instagram.com/${client.instagramUsername}`}
                                target="_blank"
                                className="text-sm text-blue-500 hover:underline underline-offset-4 cursor-pointer inline-flex items-center gap-1"
                            >
                                <Image src="/instagram.svg" width={14} height={14} alt="Instagram" />
                                {client.instagramUsername}
                            </Link>
                            <div className="border-t pt-1 border-zinc-200 dark:border-zinc-800 mt-2">
                                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-1 mb-2">
                                    <h3 className="flex items-center gap-1 text-sm font-semibold">
                                        <FileChartColumn size={16} />
                                        Relatório de resultados
                                    </h3>
                                    <div className="flex items-center gap-1">
                                        <Badge variant="secondary">
                                            etapa 10
                                        </Badge>
                                        <Badge variant="secondary">
                                            ajustes estratégicos
                                        </Badge>
                                        <Dialog>
                                            <DialogTrigger asChild>
                                                <Button variant="link" size="icon">
                                                    <InfoIcon />
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent
                                                className="h-[85vh] p-0 overflow-y-auto"
                                                style={{ maxWidth: "550px !important", width: "550px !important" }}
                                            >
                                                <div className="p-6">
                                                    <DialogHeader className="mb-4">
                                                        <DialogTitle className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                                                            Saiba mais
                                                        </DialogTitle>
                                                        <DialogDescription className="font-medium text-gray-700 dark:text-gray-200">
                                                            Etapa 10 — Ajustes Estratégicos
                                                            <span className="block mt-1 text-sm font-normal">Relatório Mensal de Resultados</span>
                                                        </DialogDescription>
                                                    </DialogHeader>
                                                    <AboutResults />
                                                </div>
                                            </DialogContent>
                                        </Dialog>
                                    </div>
                                </div>

                                <div className="flex flex-col md:flex-row justify-between gap-2">
                                    <Select
                                        value={selectedMonth || undefined}
                                        onValueChange={(value) => {
                                            setIsFormOpen(false)
                                            setEditingReport(null)
                                            setSelectedMonth(value)
                                        }}
                                    >
                                        <SelectTrigger className="md:w-[180px]">
                                            <SelectValue placeholder="Escolha o mês" />
                                        </SelectTrigger>
                                        <SelectContent>
                                            {monthNames.map((name, index) => (
                                                <SelectItem key={index} value={name.toLowerCase()}>
                                                    {name}
                                                </SelectItem>
                                            ))}
                                        </SelectContent>
                                    </Select>

                                    {(client?.resultsReport || []).filter(report =>
                                        selectedMonth ? monthName(Number(report.month)).toLowerCase() === selectedMonth : true
                                    ).length > 0 && (
                                            <div>
                                                <div className="flex flex-col gap-2 w-full md:w-auto">
                                                    <div className='flex flex-col sm:flex-row sm:justify-end gap-2'>
                                                        <Button
                                                            variant="outline"
                                                            className='w-full md:w-auto'
                                                            onClick={() => setCompareMode(compareMode === 'previous' ? 'custom' : 'previous')}
                                                        >
                                                            <Calendar1 />
                                                            {compareMode === 'previous' ? 'Comparar com outro mês' : 'Comparar com mês anterior'}
                                                        </Button>

                                                        <Button
                                                            variant="outline"
                                                            className='w-full md:w-auto'
                                                            onClick={() => setShowChartSelector(!showChartSelector)}
                                                        >
                                                            <Settings2 />
                                                            Personalizar
                                                        </Button>

                                                        <Button
                                                            disabled={isPdfLoading}
                                                            className='w-full md:w-auto hidden lg:flex'
                                                            onClick={async () => {
                                                                if (!client || !client.resultsReport) return;

                                                                const selectedReport = client.resultsReport.find(report =>
                                                                    monthName(Number(report.month)).toLowerCase() === selectedMonth
                                                                );

                                                                if (selectedReport) {
                                                                    try {
                                                                        setIsPdfLoading(true);
                                                                        await generateResultsPDF(
                                                                            client,
                                                                            selectedReport,
                                                                            monthName,
                                                                            selectedCharts,
                                                                            {
                                                                                mode: compareMode,
                                                                                customCompareMonth: compareMode === 'custom' ? (customCompareMonth || undefined) : undefined
                                                                            }
                                                                        );
                                                                    } finally {
                                                                        setIsPdfLoading(false);
                                                                    }
                                                                }
                                                            }}
                                                        >
                                                            {isPdfLoading ? (
                                                                <Ellipsis />
                                                            ) : (
                                                                <>
                                                                    <FileChartColumn />
                                                                    Exportar PDF
                                                                </>
                                                            )}
                                                        </Button>
                                                    </div>

                                                    <div className="space-y-2">
                                                        {compareMode === 'custom' && (
                                                            <><span className='text-xs text-gray-500'>
                                                                Essa funcionalidade está em período de testes.
                                                                <br />
                                                                Certifique-se de que os dados estão corretos antes de exportar o PDF.
                                                            </span>
                                                                <Card className="w-full p-4">
                                                                    <div className="text-sm font-medium mb-2">Selecione o mês para comparação:</div>
                                                                    <Select
                                                                        value={customCompareMonth || undefined}
                                                                        onValueChange={setCustomCompareMonth}
                                                                    >
                                                                        <SelectTrigger className="w-full">
                                                                            <SelectValue placeholder="Escolha o mês" />
                                                                        </SelectTrigger>
                                                                        <SelectContent>
                                                                            {selectedMonth && selectedMonth.length > 0 ? (
                                                                                client && client.resultsReport && client.resultsReport.length > 0 ? (
                                                                                    client.resultsReport
                                                                                        .filter(report => monthName(Number(report.month)).toLowerCase() !== selectedMonth)
                                                                                        .length > 0 ? (
                                                                                        client.resultsReport
                                                                                            .filter(report => monthName(Number(report.month)).toLowerCase() !== selectedMonth)
                                                                                            .map((report) => (
                                                                                                <SelectItem
                                                                                                    key={`${report.month}-${report.year}`}
                                                                                                    value={`${report.month}-${report.year}`}
                                                                                                >
                                                                                                    {monthName(report.month)} de {report.year}
                                                                                                </SelectItem>
                                                                                            ))
                                                                                    ) : (
                                                                                        <SelectItem value="no-months" disabled>
                                                                                            Nenhum outro mês disponível para comparação
                                                                                        </SelectItem>
                                                                                    )
                                                                                ) : (
                                                                                    <SelectItem value="no-reports" disabled>
                                                                                        Nenhum relatório encontrado
                                                                                    </SelectItem>
                                                                                )
                                                                            ) : (
                                                                                <SelectItem value="no-months" disabled>
                                                                                    Selecione primeiro um mês principal
                                                                                </SelectItem>
                                                                            )}
                                                                        </SelectContent>
                                                                    </Select>
                                                                </Card></>
                                                        )}

                                                        {showChartSelector && (
                                                            <Card className="w-full p-4">
                                                                <div className="text-sm font-medium mb-2">Selecione os gráficos a incluir:</div>
                                                                <div className="space-y-2">                                                    <div className="flex items-center space-x-2">
                                                                    <Checkbox
                                                                        id="charts-followers"
                                                                        checked={selectedCharts.followers}
                                                                        onCheckedChange={(checked) =>
                                                                            setSelectedCharts(prev => ({ ...prev, followers: checked === true }))
                                                                        }
                                                                    />
                                                                    <Label htmlFor="charts-followers">
                                                                        Seguidores e crescimento
                                                                    </Label>
                                                                </div>
                                                                    <div className="flex items-center space-x-2">
                                                                        <Checkbox
                                                                            id="charts-visualizations"
                                                                            checked={selectedCharts.visualizations}
                                                                            onCheckedChange={(checked) =>
                                                                                setSelectedCharts(prev => ({ ...prev, visualizations: checked === true }))
                                                                            }
                                                                        />
                                                                        <Label htmlFor="charts-visualizations">Visualizações e alcance</Label>
                                                                    </div>
                                                                    <div className="flex items-center space-x-2">
                                                                        <Checkbox
                                                                            id="charts-content"
                                                                            checked={selectedCharts.content}
                                                                            onCheckedChange={(checked) =>
                                                                                setSelectedCharts(prev => ({ ...prev, content: checked === true }))
                                                                            }
                                                                        />
                                                                        <Label htmlFor="charts-content">Conteúdo e publicações</Label>
                                                                    </div>
                                                                    <div className="flex items-center space-x-2">
                                                                        <Checkbox
                                                                            id="charts-interactions"
                                                                            checked={selectedCharts.interactions}
                                                                            onCheckedChange={(checked) =>
                                                                                setSelectedCharts(prev => ({
                                                                                    ...prev,
                                                                                    interactions: checked === true,
                                                                                    detailedInteractions: checked === true ? prev.detailedInteractions : false
                                                                                }))
                                                                            }
                                                                        />
                                                                        <Label htmlFor="charts-interactions">Interações e alcance</Label>
                                                                    </div>
                                                                    <div className="flex items-center space-x-2 pl-6">
                                                                        <Checkbox
                                                                            id="charts-detailed-interactions"
                                                                            checked={selectedCharts.detailedInteractions}
                                                                            disabled={!selectedCharts.interactions}
                                                                            onCheckedChange={(checked) =>
                                                                                setSelectedCharts(prev => ({ ...prev, detailedInteractions: checked === true }))
                                                                            }
                                                                        />
                                                                        <Label
                                                                            htmlFor="charts-detailed-interactions"
                                                                            className={!selectedCharts.interactions ? "text-muted-foreground" : ""}
                                                                        >
                                                                            Métricas detalhadas de interação
                                                                        </Label>
                                                                    </div>
                                                                </div>
                                                            </Card>
                                                        )}
                                                    </div>

                                                    <span className='hidden lg:block text-xs text-gray-500 text-end mt-1'>
                                                        Certifique-se de que você está no modo claro para exportar o PDF corretamente.
                                                    </span>
                                                    <span className='lg:hidden text-xs text-gray-500 text-end mt-1'>
                                                        A exportação do PDF só funciona em telas maiores.
                                                        <br />
                                                        Certifique-se se está em um computador.
                                                    </span>
                                                </div>
                                            </div>
                                        )}
                                </div>

                            </div>
                        </div>

                        <div className="mt-4">
                            {client?.resultsReport
                                ?.filter((report) =>
                                    selectedMonth ? monthName(Number(report.month)).toLowerCase() === selectedMonth : true
                                )
                                .length === 0 ? (
                                <div>
                                    <p className="text-sm text-gray-500 mb-4">Nenhum relatório encontrado para o mês selecionado</p>
                                    <Separator className="mb-6" />
                                    <div className="flex flex-col items-center justify-center">
                                        {!isFormOpen ? (
                                            <Button
                                                variant="outline"
                                                onClick={() => setIsFormOpen(true)}
                                                className='mt-16'
                                            >
                                                <CirclePlus />
                                                Criar relatório para {selectedMonth ? selectedMonth.charAt(0).toUpperCase() + selectedMonth.slice(1) : "o mês atual"}
                                            </Button>
                                        ) : null}
                                    </div>
                                </div>
                            ) : (
                                <div className="mt-4">
                                    {client?.resultsReport
                                        ?.filter((report) =>
                                            selectedMonth ? monthName(Number(report.month)).toLowerCase() === selectedMonth : true
                                        )
                                        .map((report) => (
                                            <div key={report.id} className="text-sm flex items-center justify-between">
                                                <div className="flex items-center gap-1 font-semibold">
                                                    <Calendar1 size={16} />
                                                    <span>{monthName(Number(report.month))} de {report.year}</span>
                                                </div>
                                                <Button
                                                    variant="outline"
                                                    onClick={() => {
                                                        setEditingReport(report);
                                                        setIsFormOpen(true);
                                                    }}
                                                >
                                                    <Edit size={16} /> Editar
                                                </Button>
                                            </div>
                                        ))}
                                </div>
                            )}
                        </div>

                        {(isFormOpen || editingReport) && (
                            <Card className="w-full mt-6">
                                <CardHeader>
                                    <CardTitle className="text-xl font-semibold mb-2">
                                        {editingReport ? "Editar relatório" : "Criar novo relatório"}
                                    </CardTitle>
                                    <CardDescription className="text-sm text-muted-foreground">
                                        {editingReport
                                            ? `Editando relatório de ${monthName(editingReport.month)} de ${editingReport.year}`
                                            : `Preencha os dados do relatório para ${selectedMonth ? selectedMonth.charAt(0).toUpperCase() + selectedMonth.slice(1) : "o mês atual"}`
                                        }
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className='px-3'>
                                    <CreateResultsReportForm
                                        clientId={id}
                                        selectedMonth={selectedMonth}
                                        monthNames={monthNames}
                                        existingReport={editingReport ? {
                                            ...editingReport,
                                            createdAt: new Date(editingReport.createdAt),
                                            updatedAt: new Date(editingReport.updatedAt),
                                            results: editingReport.results?.map(result => ({
                                                id: result.id,
                                                resultsReportId: result.resultsReportId,
                                                createdAt: new Date(result.createdAt),

                                                // Métricas básicas
                                                newFollowers: result.newFollowers ?? null,
                                                totalPosts: result.totalPosts ?? null,
                                                totalInteractions: result.totalInteractions ?? null,
                                                totalStories: result.totalStories ?? null,
                                                totalViews: result.totalViews ?? null,

                                                // Aba Visualizações - novos campos
                                                percentageAds: result.percentageAds ?? null,
                                                percentageFollowers: result.percentageFollowers ?? null,
                                                percentageNonFollowers: result.percentageNonFollowers ?? null,
                                                percentageAccountsReached: result.percentageAccountsReached ?? null,
                                                percentageProfileVisits: result.percentageProfileVisits ?? null,
                                                externalLinkTaps: result.externalLinkTaps ?? null,
                                                percentageExternalLinkTaps: result.percentageExternalLinkTaps ?? null,
                                                businessAddressTaps: result.businessAddressTaps ?? null,
                                                percentageBusinessAddressTaps: result.percentageBusinessAddressTaps ?? null,

                                                // Aba Interações - novos campos
                                                interactionsPercentageAds: result.interactionsPercentageAds ?? null,
                                                interactionsPercentageFollowers: result.interactionsPercentageFollowers ?? null,
                                                interactionsPercentageNonFollowers: result.interactionsPercentageNonFollowers ?? null,

                                                // Reel
                                                reelInteractionPercentage: result.reelInteractionPercentage ?? null,

                                                // Publicações
                                                postInteractionPercentage: result.postInteractionPercentage ?? null,

                                                // Story
                                                storyInteractionPercentage: result.storyInteractionPercentage ?? null,

                                                // Vídeos ao vivo
                                                liveInteractionPercentage: result.liveInteractionPercentage ?? null,

                                                // Vídeos
                                                videoInteractionPercentage: result.videoInteractionPercentage ?? null,
                                                videoLikes: result.videoLikes ?? null,
                                                videoComments: result.videoComments ?? null,
                                                videoSaves: result.videoSaves ?? null,
                                                videoShares: result.videoShares ?? null,

                                                // Métricas de alcance
                                                accountsReached: result.accountsReached ?? null,
                                                reachFollowers: result.reachFollowers ?? 0,
                                                reachNonFollowers: result.reachNonFollowers ?? 0,
                                                percentageProfileActivity: result.percentageProfileActivity ?? 0,
                                                profileVisits: result.profileVisits ?? 0,
                                                websiteClicks: result.websiteClicks ?? 0,
                                                profileActivity: result.profileActivity ?? 0,

                                                // Campos de distribuição de interações - novos
                                                liveComments: result.liveComments ?? 0,

                                                // Métricas de Posts - novos
                                                postLikes: result.postLikes ?? 0,
                                                postComments: result.postComments ?? 0,
                                                postSaves: result.postSaves ?? 0,
                                                postShares: result.postShares ?? 0,

                                                // Métricas de Stories - novos
                                                storyReplies: result.storyReplies ?? 0,

                                                // Métricas de Reels - novos
                                                reelLikes: result.reelLikes ?? 0,
                                                reelComments: result.reelComments ?? 0,
                                                reelSaves: result.reelSaves ?? 0,
                                                reelShares: result.reelShares ?? 0,

                                                // Outros campos ainda ativos
                                                averageInteractionsPerPost: result.averageInteractionsPerPost ?? null,
                                                reachByPostType: result.reachByPostType ?? null,
                                                averageStoryViews: result.averageStoryViews ?? null,

                                                // Aba 4 - Seguidores - campos obrigatórios que estavam faltando
                                                totalFollowers: result.totalFollowers ?? 0,
                                                unfollowCount: result.unfollowCount ?? 0,
                                                followersLocation: result.followersLocation ?? {},
                                                followersAgeRange: result.followersAgeRange ?? {},
                                                followersGender: result.followersGender ?? {},

                                                // Campos obsoletos mas ainda esperados pelo form (valores padrão)
                                                interactionsByTypeContent: null,
                                                staticStories: null,
                                                animatedStories: null,
                                                staticFeed: null,
                                                reel: null,
                                                extraMaterials: null,
                                                interactionTotal: result.totalInteractions ?? 0 // Usar totalInteractions como fallback
                                            }))
                                        } : null}
                                        onSuccess={() => {
                                            refreshClientData();
                                            setEditingReport(null);
                                        }}
                                        onCancel={() => {
                                            setIsFormOpen(false);
                                            setEditingReport(null);
                                        }}
                                    />
                                </CardContent>
                            </Card>
                        )}

                        {client?.resultsReport
                            ?.filter((report) =>
                                selectedMonth ? monthName(Number(report.month)).toLowerCase() === selectedMonth : true
                            )
                            .map((currentReport) => {
                                let comparisonReport;
                                let comparisonMonthName;

                                if (compareMode === 'previous') {
                                    const previousMonth = currentReport.month === 1 ? 12 : currentReport.month - 1;
                                    const previousYear = currentReport.month === 1 ? currentReport.year - 1 : currentReport.year;

                                    comparisonReport = client.resultsReport?.find(
                                        (r) => r.month === previousMonth && (
                                            currentReport.month === 1 ? r.year === previousYear : r.year === currentReport.year
                                        )
                                    );
                                    comparisonMonthName = monthName(previousMonth);
                                } else {
                                    if (customCompareMonth) {
                                        const [compareMonth, compareYear] = customCompareMonth.split('-').map(Number);
                                        comparisonReport = client.resultsReport?.find(
                                            (r) => r.month === compareMonth && r.year === compareYear
                                        );
                                        comparisonMonthName = `${monthName(compareMonth)} de ${compareYear}`;
                                    } else {
                                        const previousMonth = currentReport.month === 1 ? 12 : currentReport.month - 1;
                                        const previousYear = currentReport.month === 1 ? currentReport.year - 1 : currentReport.year;

                                        comparisonReport = client.resultsReport?.find(
                                            (r) => r.month === previousMonth && (
                                                currentReport.month === 1 ? r.year === previousYear : r.year === currentReport.year
                                            )
                                        );
                                        comparisonMonthName = monthName(previousMonth);
                                    }
                                }

                                return (
                                    <div key={`chart-${currentReport.id}`} className="mt-8">
                                        {selectedCharts.followers && (
                                            <div className="mb-6">
                                                <h3 className="text-lg font-semibold mb-4 flex items-center">
                                                    <span className="w-2 h-6 bg-primary mr-2 rounded-sm"></span>
                                                    Seguidores e crescimento
                                                </h3>

                                                <div className="grid grid-cols-1 gap-4">
                                                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                                        <div className="relative z-10">
                                                            <div className="card-wrapper h-[400px] relative">
                                                                <FollowersComparisonChart
                                                                    data-testid="followers-comparison-chart"
                                                                    currentMonth={monthName(currentReport.month)}
                                                                    currentFollowers={currentReport?.results?.[0]?.newFollowers || 0}
                                                                    previousMonth={comparisonMonthName}
                                                                    previousFollowers={comparisonReport?.results?.[0]?.newFollowers || null}
                                                                />
                                                            </div>
                                                        </div>

                                                        <div className="relative z-10">
                                                            <div className="card-wrapper h-[400px] relative">
                                                                <FollowersBarChart
                                                                    data-testid="followers-bar-chart"
                                                                    currentMonth={monthName(currentReport.month)}
                                                                    currentFollowers={currentReport?.results?.[0]?.totalFollowers || 0}
                                                                    previousMonth={comparisonMonthName}
                                                                    previousFollowers={comparisonReport?.results?.[0]?.totalFollowers || null}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <div className="relative z-10">
                                                        <div className="card-wrapper h-[400px] relative">
                                                            <PlatformGrowthChart
                                                                currentMonth={monthName(currentReport.month)}
                                                                currentFollowers={currentReport?.results?.[0]?.newFollowers || 0}
                                                                totalFollowers={currentReport?.results?.[0]?.totalFollowers || 0}
                                                                unfollowCount={currentReport?.results?.[0]?.unfollowCount || 0}
                                                                previousMonth={comparisonMonthName}
                                                                previousTotalFollowers={comparisonReport?.results?.[0]?.totalFollowers || 0}
                                                                month={monthName(currentReport.month)}
                                                                year={currentReport.year}
                                                            />
                                                        </div>
                                                    </div>

                                                    <FollowersDataSection
                                                        followersLocation={currentReport?.results?.[0]?.followersLocation as Record<string, number> || null}
                                                        followersAgeRange={currentReport?.results?.[0]?.followersAgeRange as Record<string, number> || null}
                                                        followersGender={currentReport?.results?.[0]?.followersGender as Record<string, number> || null}
                                                    />
                                                </div>
                                            </div>
                                        )}

                                        {selectedCharts.visualizations && (
                                            <div className="mb-11">
                                                <h3 className="text-lg font-semibold mb-4 flex items-center">
                                                    <span className="w-2 h-6 bg-purple-500 mr-2 rounded-sm"></span>
                                                    Visualizações e alcance
                                                </h3>

                                                <div className="relative z-10">
                                                    <div className="card-wrapper relative">
                                                        <VisualizationMetrics
                                                            percentageAds={currentReport?.results?.[0]?.percentageAds}
                                                            percentageFollowers={currentReport?.results?.[0]?.percentageFollowers}
                                                            percentageNonFollowers={currentReport?.results?.[0]?.percentageNonFollowers}
                                                            accountsReached={currentReport?.results?.[0]?.accountsReached}
                                                            percentageAccountsReached={currentReport?.results?.[0]?.percentageAccountsReached}
                                                            profileActivity={currentReport?.results?.[0]?.profileActivity}
                                                            percentageProfileActivity={currentReport?.results?.[0]?.percentageProfileActivity}
                                                            profileVisits={currentReport?.results?.[0]?.profileVisits}
                                                            percentageProfileVisits={currentReport?.results?.[0]?.percentageProfileVisits}
                                                            externalLinkTaps={currentReport?.results?.[0]?.externalLinkTaps}
                                                            percentageExternalLinkTaps={currentReport?.results?.[0]?.percentageExternalLinkTaps}
                                                            businessAddressTaps={currentReport?.results?.[0]?.businessAddressTaps}
                                                            percentageBusinessAddressTaps={currentReport?.results?.[0]?.percentageBusinessAddressTaps}
                                                            month={monthName(currentReport.month)}
                                                            year={currentReport.year}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                        {selectedCharts.interactions && (
                                            <>
                                                <div className="mb-11">
                                                    <h3 className="text-lg font-semibold mb-4 flex items-center">
                                                        <span className="w-2 h-6 bg-emerald-500 mr-2 rounded-sm"></span>
                                                        Interações e alcance
                                                    </h3>
                                                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                                                        <div className="lg:col-span-1 relative z-10">
                                                            <div className="card-wrapper h-[400px] relative">
                                                                <TotalInteractions
                                                                    data-testid="total-interactions"
                                                                    interactionTotal={currentReport?.results?.[0]?.totalInteractions || 0}
                                                                    totalInteractions={currentReport?.results?.[0]?.totalInteractions || 0}
                                                                    month={monthName(currentReport.month)}
                                                                    year={currentReport.year}
                                                                    dailyData={[
                                                                        { day: "Semana 1", value: Math.round((currentReport?.results?.[0]?.totalInteractions || 0) * 0.2) },
                                                                        { day: "Semana 2", value: Math.round((currentReport?.results?.[0]?.totalInteractions || 0) * 0.22) },
                                                                        { day: "Semana 3", value: Math.round((currentReport?.results?.[0]?.totalInteractions || 0) * 0.28) },
                                                                        { day: "Semana 4", value: Math.round((currentReport?.results?.[0]?.totalInteractions || 0) * 0.3) }
                                                                    ]}
                                                                />
                                                            </div>
                                                        </div>

                                                        <div className="lg:col-span-1 relative z-10">
                                                            <div className="card-wrapper h-[400px] relative">
                                                                <DetailedReachMetrics
                                                                    data-testid="detailed-reach-metrics"
                                                                    reachFollowers={currentReport?.results?.[0]?.reachFollowers || 0}
                                                                    reachNonFollowers={currentReport?.results?.[0]?.reachNonFollowers || 0}
                                                                    percentageProfileActivity={currentReport?.results?.[0]?.percentageProfileActivity || 0}
                                                                    profileVisits={currentReport?.results?.[0]?.profileVisits || 0}
                                                                    websiteClicks={currentReport?.results?.[0]?.websiteClicks || 0}
                                                                    profileActivity={currentReport?.results?.[0]?.profileActivity || 0}
                                                                    month={monthName(currentReport.month)}
                                                                    year={currentReport.year}
                                                                />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                                {selectedCharts.detailedInteractions && (
                                                    <div className="mb-11">
                                                        <h3 className="text-lg font-semibold mb-4 flex items-center">
                                                            <span className="w-2 h-6 bg-indigo-500 mr-2 rounded-sm"></span>
                                                            Métricas detalhadas de interação
                                                        </h3>
                                                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                                            <div className="relative z-10">
                                                                <div className="card-wrapper h-[400px] relative">
                                                                    <PostMetricsChart
                                                                        postLikes={currentReport?.results?.[0]?.postLikes || 0}
                                                                        postComments={currentReport?.results?.[0]?.postComments || 0}
                                                                        postSaves={currentReport?.results?.[0]?.postSaves || 0}
                                                                        postShares={currentReport?.results?.[0]?.postShares || 0}
                                                                        postInteractionRate={currentReport?.results?.[0]?.postInteractionPercentage || 0}
                                                                        month={monthName(currentReport.month)}
                                                                        year={currentReport.year}
                                                                    />
                                                                </div>
                                                            </div>

                                                            <div className="relative z-10">
                                                                <div className="card-wrapper h-[400px] relative">
                                                                    <ReelMetricsChart
                                                                        reelLikes={currentReport?.results?.[0]?.reelLikes || 0}
                                                                        reelComments={currentReport?.results?.[0]?.reelComments || 0}
                                                                        reelSaves={currentReport?.results?.[0]?.reelSaves || 0}
                                                                        reelShares={currentReport?.results?.[0]?.reelShares || 0}
                                                                        reelInteractionRate={currentReport?.results?.[0]?.reelInteractionPercentage || 0}
                                                                        month={monthName(currentReport.month)}
                                                                        year={currentReport.year}
                                                                    />
                                                                </div>
                                                            </div>

                                                            <div className="relative z-10">
                                                                <div className="card-wrapper h-[400px] relative">
                                                                    <VideoMetricsChart
                                                                        videoLikes={currentReport?.results?.[0]?.videoLikes || 0}
                                                                        videoComments={currentReport?.results?.[0]?.videoComments || 0}
                                                                        videoSaves={currentReport?.results?.[0]?.videoSaves || 0}
                                                                        videoShares={currentReport?.results?.[0]?.videoShares || 0}
                                                                        videoInteractionRate={currentReport?.results?.[0]?.videoInteractionPercentage || 0}
                                                                        month={monthName(currentReport.month)}
                                                                        year={currentReport.year}
                                                                    />
                                                                </div>
                                                            </div>

                                                            <div className="relative z-10">
                                                                <div className="card-wrapper h-[400px] relative">
                                                                    <ContentInteractionRateChart
                                                                        postInteractionRate={currentReport?.results?.[0]?.postInteractionPercentage || 0}
                                                                        reelInteractionRate={currentReport?.results?.[0]?.reelInteractionPercentage || 0}
                                                                        storyReplies={currentReport?.results?.[0]?.storyReplies || 0}
                                                                        liveComments={currentReport?.results?.[0]?.liveComments || 0}
                                                                        month={monthName(currentReport.month)}
                                                                        year={currentReport.year}
                                                                    />
                                                                </div>
                                                            </div>

                                                            <div className="relative z-10">
                                                                <div className="card-wrapper h-[400px] relative">
                                                                    <InteractionPercentageChart
                                                                        interactionsPercentageAds={currentReport?.results?.[0]?.interactionsPercentageAds || 0}
                                                                        interactionsPercentageFollowers={currentReport?.results?.[0]?.interactionsPercentageFollowers || 0}
                                                                        interactionsPercentageNonFollowers={currentReport?.results?.[0]?.interactionsPercentageNonFollowers || 0}
                                                                        month={monthName(currentReport.month)}
                                                                        year={currentReport.year}
                                                                    />
                                                                </div>
                                                            </div>

                                                            <div className="relative z-10">
                                                                <div className="card-wrapper h-[400px] relative">
                                                                    <ContentInteractionRatesChart
                                                                        postInteractionRate={currentReport?.results?.[0]?.postInteractionPercentage || 0}
                                                                        reelInteractionRate={currentReport?.results?.[0]?.reelInteractionPercentage || 0}
                                                                        storyInteractionRate={currentReport?.results?.[0]?.storyInteractionPercentage || 0}
                                                                        liveInteractionRate={currentReport?.results?.[0]?.liveInteractionPercentage || 0}
                                                                        videoInteractionRate={currentReport?.results?.[0]?.videoInteractionPercentage || 0}
                                                                        month={monthName(currentReport.month)}
                                                                        year={currentReport.year}
                                                                    />
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                )}
                                            </>
                                        )}

                                        {selectedCharts.content && (
                                            <div className="mb-11">
                                                <h3 className="text-lg font-semibold mb-4 flex items-center">
                                                    <span className="w-2 h-6 bg-blue-500 mr-2 rounded-sm"></span>
                                                    Conteúdo e publicações
                                                </h3>
                                                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                                                    <div className="relative z-10">
                                                        <div className="card-wrapper h-[400px] relative">
                                                            <TotalMetricChart
                                                                data-title="Total de posts"
                                                                value={currentReport?.results?.[0]?.totalPosts || 0}
                                                                title="Total de posts"
                                                                labelText="posts"
                                                                month={monthName(currentReport.month)}
                                                                year={currentReport.year}
                                                                colorIndex={1}
                                                            />
                                                        </div>
                                                    </div>

                                                    <div className="relative z-10">
                                                        <div className="card-wrapper h-[400px] relative">
                                                            <TotalMetricChart
                                                                data-title="Total de Stories"
                                                                value={currentReport?.results?.[0]?.totalStories || 0}
                                                                title="Total de Stories"
                                                                labelText="stories"
                                                                month={monthName(currentReport.month)}
                                                                year={currentReport.year}
                                                                colorIndex={2}
                                                            />
                                                        </div>
                                                    </div>

                                                    <div className="relative z-10">
                                                        <div className="card-wrapper h-[400px] relative">
                                                            <TotalMetricChart
                                                                data-title="Total de visualizações"
                                                                value={currentReport?.results?.[0]?.totalViews || 0}
                                                                title="Total de visualizações"
                                                                labelText="visualizações"
                                                                month={monthName(currentReport.month)}
                                                                year={currentReport.year}
                                                                colorIndex={3}
                                                            />
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )}

                                    </div>
                                );
                            })
                        }
                    </>
                ) : (
                    <NotAllowed
                        page='clients'
                    />
                )}
            </div>
            <Footer />
        </div>
    );
}