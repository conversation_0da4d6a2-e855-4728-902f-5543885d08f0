"use client"

import { MessageSquareMore, Users, ListTodo, Bell, FileClock, UserCog, TrendingUp, AlertCircle, CheckCircle, Clock } from "lucide-react"
import { Footer } from "../components/footer"
import Link from 'next/link'
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { NotAllowed } from "../components/not-allowed"
import Loading from "../components/ui/loading"
import { Button } from "../components/ui/button"
import { User } from "@prisma/client"
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarInset,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarProvider,
    SidebarTrigger,
} from "../components/ui/sidebar"
import { Separator } from "../components/ui/separator"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Header } from "../components/header"
import Image from "next/image"
import { Badge } from "../components/ui/badge"

const generalItens = [
    {
        title: "Demandas",
        url: "/admin/demands",
        icon: ListTodo,
        description: "Gerenciar todas as demandas e atribuições da equipe"
    },
    {
        title: "Orçamentos",
        url: "/panel",
        icon: TrendingUp,
        description: "Criar orçamentos"
    },
    {
        title: "Notificações",
        url: "/notifications",
        icon: Bell,
        description: "Gerenciar todas as notificações do sistema"
    },
];

const timeItens = [
    {
        title: "Perfis de funcionários",
        url: "/admin/employee-profile",
        icon: UserCog,
        description: "Acompanhar e gerenciar os perfis dos funcionários"
    },
    {
        title: "Controle de ponto",
        url: "/admin/point-record",
        icon: FileClock,
        description: "Acompanhar e gerenciar o registro de ponto do time"
    },
];

const systemSettings = [
    {
        title: "Usuários",
        url: "/admin/users",
        icon: Users,
        description: "Gerenciar usuários do sistema"
    },
    {
        title: "Feedbacks",
        url: "/admin/feedbacks",
        icon: MessageSquareMore,
        description: "Visualizar feedbacks dos usuários"
    }
];

interface DashboardStats {
    pendingDemands: number;
    totalUsers: number;
    unreadNotifications: number;
    totalFeedbacks: number;
    pointRecordsToday: number;
    activeEmployees: number;
}

export default function PanelPage() {
    const { data: session, status } = useSession();
    const [isAdmin, setIsAdmin] = useState(false);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [user, setUser] = useState<User | null>(null);
    const [stats, setStats] = useState<DashboardStats>({
        pendingDemands: 0,
        totalUsers: 0,
        unreadNotifications: 0,
        totalFeedbacks: 0,
        pointRecordsToday: 0,
        activeEmployees: 0,
    });
    const [loadingStats, setLoadingStats] = useState(true);
    const router = useRouter();

    const fetchDashboardStats = async () => {
        try {
            setLoadingStats(true);

            // Buscar estatísticas em paralelo
            const [
                demandsResponse,
                usersResponse,
                notificationsResponse,
                feedbacksResponse
            ] = await Promise.all([
                fetch('/api/demands/pending-count'),
                fetch('/api/users'),
                fetch('/api/notifications?unread=true'),
                fetch('/api/feedback')
            ]);

            const demandsData = demandsResponse.ok ? await demandsResponse.json() : { count: 0 };
            const usersData = usersResponse.ok ? await usersResponse.json() : [];
            const notificationsData = notificationsResponse.ok ? await notificationsResponse.json() : [];
            const feedbacksData = feedbacksResponse.ok ? await feedbacksResponse.json() : [];

            const activeUsers = usersData.filter((user: { archived?: boolean }) => !user.archived);

            setStats({
                pendingDemands: demandsData.count || 0,
                totalUsers: usersData.length || 0,
                unreadNotifications: notificationsData.length || 0,
                totalFeedbacks: feedbacksData.length || 0,
                pointRecordsToday: 0, // Implementar se necessário
                activeEmployees: activeUsers.length || 0,
            });
        } catch (error) {
            console.error("Erro ao buscar estatísticas:", error);
        } finally {
            setLoadingStats(false);
        }
    };

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();

                        const hasPermission =
                            user?.role === "ADMIN" ||
                            user?.role === "DEVELOPER" ||
                            user?.role === "GENERAL_ASSISTANT";

                        setIsAdmin(hasPermission);
                        setUser(user);

                        // Buscar estatísticas apenas se for admin
                        if (hasPermission) {
                            fetchDashboardStats();
                        }
                    } else {
                        console.error("Falha ao buscar dados do usuário:", response.status);
                        setIsAdmin(false);
                    }
                } else {
                    setIsAdmin(false);
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
                setIsAdmin(false);
            } finally {
                setIsFetchingRole(false);
            }
        };

        if (status === "authenticated") {
            fetchUserRole();
        } else if (status === "unauthenticated") {
            router.push("/auth/signin");
        }
    }, [status, session, router]);

    if (isFetchingRole) {
        return (
            <div className="min-h-screen flex justify-center items-center">
                <Loading />
            </div>
        );
    }

    if (!isAdmin) {
        return <NotAllowed page="/" />;
    }

    return (
        <><Header /><SidebarProvider>
            <div className="min-h-screen flex w-full">
                <Sidebar>
                    <SidebarHeader>
                        <div className="flex items-center gap-2 px-2 py-2">
                            <Link href="/">
                                <Image src='/icon-b4desk.svg' width={25} height={25} alt="B4Desk" />
                            </Link>
                            <div className="flex flex-col">
                                <span className="font-semibold text-sm">Painel administrativo</span>
                                <span className="text-xs text-muted-foreground">B4Desk</span>
                            </div>
                        </div>
                    </SidebarHeader>

                    <SidebarContent>
                        <SidebarGroup>
                            <SidebarGroupLabel>Funcionalidades gerais</SidebarGroupLabel>
                            <SidebarGroupContent>
                                <SidebarMenu>
                                    {generalItens.map((item) => (
                                        <SidebarMenuItem key={item.url}>
                                            <SidebarMenuButton asChild>
                                                <Link href={item.url}>
                                                    <item.icon className="h-4 w-4" />
                                                    <span>{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </SidebarGroupContent>
                        </SidebarGroup>

                        <SidebarGroup>
                            <SidebarGroupLabel>Equipe</SidebarGroupLabel>
                            <SidebarGroupContent>
                                <SidebarMenu>
                                    {timeItens.map((item) => (
                                        <SidebarMenuItem key={item.url}>
                                            <SidebarMenuButton asChild>
                                                <Link href={item.url}>
                                                    <item.icon className="h-4 w-4" />
                                                    <span>{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </SidebarGroupContent>
                        </SidebarGroup>

                        {(user?.role === "ADMIN" || user?.role === "DEVELOPER") && (
                            <SidebarGroup>
                                <SidebarGroupLabel>Sistema</SidebarGroupLabel>
                                <SidebarGroupContent>
                                    <SidebarMenu>
                                        {systemSettings.map((item) => (
                                            <SidebarMenuItem key={item.url}>
                                                <SidebarMenuButton asChild>
                                                    <Link href={item.url}>
                                                        <item.icon className="h-4 w-4" />
                                                        <span>{item.title}</span>
                                                    </Link>
                                                </SidebarMenuButton>
                                            </SidebarMenuItem>
                                        ))}
                                    </SidebarMenu>
                                </SidebarGroupContent>
                            </SidebarGroup>
                        )}
                    </SidebarContent>

                    <SidebarFooter>
                        <div className="px-2 py-2">
                            <p className="text-xs text-muted-foreground">
                                Logado como: {session?.user?.name}
                            </p>
                        </div>
                    </SidebarFooter>
                </Sidebar>

                <SidebarInset>
                    <div className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
                        <SidebarTrigger className="ml-4" />
                        <Separator orientation="vertical" className="mr-2 h-4" />
                        <div className="flex items-center gap-2">
                            <h1 className="text-lg font-semibold">Painel administrativo</h1>
                        </div>
                    </div>

                    <div className="flex-1 p-6">
                        <div className="max-w-6xl mx-auto">
                            <p className="text-muted-foreground mb-4 text-sm">
                                Bem-vindo ao painel de controle do sistema. Você pode gerenciar as funcionalides administrativas no menu lateral.
                                Tenha uma visão geral das principais métricas e estatísticas abaixo.
                            </p>

                            {loadingStats ? (
                                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                    {[...Array(6)].map((_, i) => (
                                        <Card key={i} className="animate-pulse">
                                            <CardHeader className="pb-3">
                                                <div className="h-4 bg-muted rounded w-3/4"></div>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
                                                <div className="h-3 bg-muted rounded w-full"></div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                    <Card className="hover:shadow-md transition-shadow">
                                        <CardHeader className="pb-3">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2">
                                                    <AlertCircle className="h-5 w-5 text-primary2" />
                                                    <CardTitle className="text-lg">Demandas</CardTitle>
                                                </div>
                                                <Button asChild variant="secondary" size="sm">
                                                    <Link href="/admin/demands">Ver todas</Link>
                                                </Button>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-3xl font-bold mb-2">
                                                {stats.pendingDemands}
                                            </div>
                                            <CardDescription>
                                                Demandas pendentes no mês atual
                                            </CardDescription>
                                        </CardContent>
                                    </Card>

                                    <Card className="hover:shadow-md transition-shadow">
                                        <CardHeader className="pb-3">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2">
                                                    <CheckCircle className="h-5 w-5 text-primary2" />
                                                    <CardTitle className="text-lg">Funcionários</CardTitle>
                                                </div>
                                                <Button asChild variant="secondary" size="sm">
                                                    <Link href="/admin/employee-profile">Ver perfis</Link>
                                                </Button>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-3xl font-bold mb-2">
                                                {stats.activeEmployees}
                                            </div>
                                            <CardDescription>
                                                Funcionários cadastrados e ativos
                                            </CardDescription>
                                        </CardContent>
                                    </Card>

                                    <Card className="hover:shadow-md transition-shadow">
                                        <CardHeader className="pb-3">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2">
                                                    <Bell className="h-5 w-5 text-primary2" />
                                                    <CardTitle className="text-lg">Notificações</CardTitle>
                                                </div>
                                                <Button asChild variant="secondary" size="sm">
                                                    <Link href="/notifications">Ver todas</Link>
                                                </Button>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-3xl font-bold mb-2">
                                                {stats.unreadNotifications}
                                            </div>
                                            <CardDescription>
                                                Notificações não lidas
                                            </CardDescription>
                                        </CardContent>
                                    </Card>

                                    <Card className="hover:shadow-md transition-shadow opacity-50">
                                        <CardHeader className="pb-3 relative">
                                            <Badge
                                                variant="secondary"
                                                className="text-xs absolute left-0 top-0 rounded-tl-xl rounded-tr-none rounded-br-none rounded-bl-none"
                                            >
                                                Em breve
                                            </Badge>
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-center gap-2">
                                                    <MessageSquareMore className="h-5 w-5 text-primary2" />
                                                    <CardTitle className="text-lg">Orçamentos</CardTitle>
                                                </div>
                                                <Link href="/admin/quotes" className="cursor-not-allowed">
                                                    <Button variant="secondary" disabled={true} size="sm">
                                                        Ver todos
                                                    </Button>
                                                </Link>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-3xl font-bold mb-2">
                                                0
                                            </div>
                                            <CardDescription>
                                                Orçamentos criados
                                            </CardDescription>
                                        </CardContent>
                                    </Card>

                                    {(user?.role === "ADMIN" || user?.role === "DEVELOPER") && (
                                        <>
                                            <Card className="hover:shadow-md transition-shadow border-primary/20">
                                                <CardHeader className="pb-3">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-2">
                                                            <Users className="h-5 w-5 text-primary2" />
                                                            <CardTitle className="text-lg">Usuários</CardTitle>
                                                        </div>
                                                        <Button asChild variant="secondary" size="sm">
                                                            <Link href="/admin/users">Gerenciar</Link>
                                                        </Button>
                                                    </div>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="text-3xl font-bold mb-2">
                                                        {stats.totalUsers}
                                                    </div>
                                                    <CardDescription>
                                                        Total de usuários cadastrados
                                                    </CardDescription>
                                                </CardContent>
                                            </Card>

                                            <Card className="hover:shadow-md transition-shadow border-primary/20">
                                                <CardHeader className="pb-3">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-center gap-2">
                                                            <Clock className="h-5 w-5 text-primary2" />
                                                            <CardTitle className="text-lg">Controle de ponto</CardTitle>
                                                        </div>
                                                        <Button asChild variant="secondary" size="sm">
                                                            <Link href="/admin/point-record">Ver registros</Link>
                                                        </Button>
                                                    </div>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="text-3xl font-bold mb-2">
                                                        <TrendingUp className="h-8 w-8" />
                                                    </div>
                                                    <CardDescription>
                                                        Sistema de registro de ponto
                                                    </CardDescription>
                                                </CardContent>
                                            </Card>
                                        </>
                                    )}
                                </div>
                            )}
                        </div>
                    </div>

                    <Footer />
                </SidebarInset>
            </div>
        </SidebarProvider></>
    );
}
