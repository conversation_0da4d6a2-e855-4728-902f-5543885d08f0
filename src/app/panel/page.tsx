"use client"

import { MessageSquareMore, Users, ListTodo, Bell, FileClock, UserCog, TrendingUp, AlertCircle, CheckCircle, Clock, UserPlus, Layers2, CalendarPlus } from "lucide-react"
import { Footer } from "../components/footer"
import Link from 'next/link'
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { NotAllowed } from "../components/not-allowed"
import Loading from "../components/ui/loading"
import { Button } from "../components/ui/button"
import { Client, User } from "@prisma/client"
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarInset,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarProvider,
    SidebarTrigger,
} from "../components/ui/sidebar"
import { Separator } from "../components/ui/separator"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Header } from "../components/header"
import Image from "next/image"
import { Badge } from "../components/ui/badge"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "../components/ui/dialog"
import { CreatePlanning } from "../components/create-planning"
import { GeneralDemandForm } from "../components/general-demand-form"
import { Avatar, AvatarFallback, AvatarImage } from "../components/ui/avatar"

const generalItens = [
    {
        title: "Demandas",
        url: "/admin/demands",
        icon: ListTodo,
        description: "Gerenciar todas as demandas e atribuições da equipe"
    },
    {
        title: "Orçamentos",
        url: "/admin/quotes",
        icon: TrendingUp,
        description: "Criar orçamentos",
        new: true
    },
    {
        title: "Notificações",
        url: "/notifications",
        icon: Bell,
        description: "Gerenciar todas as notificações do sistema"
    },
];

const timeItens = [
    {
        title: "Perfis de funcionários",
        url: "/admin/employee-profile",
        icon: UserCog,
        description: "Acompanhar e gerenciar os perfis dos funcionários"
    },
    {
        title: "Controle de ponto",
        url: "/admin/point-record",
        icon: FileClock,
        description: "Acompanhar e gerenciar o registro de ponto do time"
    },
];

const systemSettings = [
    {
        title: "Usuários",
        url: "/admin/users",
        icon: Users,
        description: "Gerenciar usuários do sistema"
    },
    {
        title: "Feedbacks",
        url: "/admin/feedbacks",
        icon: MessageSquareMore,
        description: "Visualizar feedbacks dos usuários"
    }
];

interface DashboardStats {
    pendingDemands: number;
    totalUsers: number;
    unreadNotifications: number;
    totalFeedbacks: number;
    pointRecordsToday: number;
    activeEmployees: number;
    quotes: number;
}

export default function PanelPage() {
    const { data: session, status } = useSession();
    const [isAdmin, setIsAdmin] = useState(false);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [user, setUser] = useState<User | null>(null);
    const [stats, setStats] = useState<DashboardStats>({
        pendingDemands: 0,
        totalUsers: 0,
        unreadNotifications: 0,
        totalFeedbacks: 0,
        pointRecordsToday: 0,
        activeEmployees: 0,
        quotes: 0,
    });
    const [loadingStats, setLoadingStats] = useState(true);
    const [dialogOpen, setDialogOpen] = useState(false);
    const [clients, setClients] = useState<Client[]>([]);
    const router = useRouter();

    const fetchDashboardStats = async () => {
        try {
            setLoadingStats(true);

            const currentMonth = new Date().getMonth() + 1;
            const currentYear = new Date().getFullYear();

            const [
                demandsResponse,
                usersResponse,
                notificationsResponse,
                feedbacksResponse,
                pointRecordResponse,
                quotesResponse
            ] = await Promise.all([
                fetch(`/api/demands/pending-count?month=${currentMonth}&year=${currentYear}`),
                fetch('/api/users'),
                fetch('/api/notifications?unread=true'),
                fetch('/api/feedback'),
                fetch('/api/point-record/today'),
                fetch('/api/quotes')
            ]);

            const demandsData = demandsResponse.ok ? await demandsResponse.json() : { count: 0 };
            const usersData = usersResponse.ok ? await usersResponse.json() : [];
            const notificationsData = notificationsResponse.ok ? await notificationsResponse.json() : [];
            const feedbacksData = feedbacksResponse.ok ? await feedbacksResponse.json() : [];
            const pointRecordData = pointRecordResponse.ok ? await pointRecordResponse.json() : {};
            const quotesData = quotesResponse.ok ? await quotesResponse.json() : [];

            const activeUsers = usersData.filter((user: { archived?: boolean }) => !user.archived);

            setStats({
                pendingDemands: demandsData.count || 0,
                totalUsers: usersData.length || 0,
                unreadNotifications: notificationsData.length || 0,
                totalFeedbacks: feedbacksData.length || 0,
                pointRecordsToday: pointRecordData?.usersWithRecordsCount || 0,
                activeEmployees: activeUsers.length || 0,
                quotes: quotesData.length || 0,
            });
        } catch (error) {
            console.error("Erro ao buscar estatísticas:", error);
        } finally {
            setLoadingStats(false);
        }
    };

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();

                        const hasPermission =
                            user?.role === "ADMIN" ||
                            user?.role === "DEVELOPER" ||
                            user?.role === "GENERAL_ASSISTANT";

                        setIsAdmin(hasPermission);
                        setUser(user);

                        if (hasPermission) {
                            fetchDashboardStats();
                        }
                    } else {
                        console.error("Falha ao buscar dados do usuário:", response.status);
                        setIsAdmin(false);
                    }
                } else {
                    setIsAdmin(false);
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
                setIsAdmin(false);
            } finally {
                setIsFetchingRole(false);
            }
        };

        if (status === "authenticated") {
            fetchUserRole();
        } else if (status === "unauthenticated") {
            router.push("/auth/signin");
        }
    }, [status, session, router]);

    useEffect(() => {
        const fetchClients = async () => {
            try {
                const response = await fetch("/api/clients");
                if (response.ok) {
                    const data = await response.json();
                    setClients(data);
                }
            } catch (error) {
                console.error("Erro ao buscar clientes:", error);
            }
        };

        fetchClients();
    }, []);

    if (isFetchingRole) {
        return (
            <div className="min-h-screen flex justify-center items-center">
                <Loading />
            </div>
        );
    }

    if (!isAdmin) {
        return <NotAllowed page="/" />;
    }

    const roles = {
        'ADMIN': 'Administrador',
        'VIEWER': 'Usuário',
        'DEVELOPER': 'Desenvolvedor',
        'COPY': 'Copy',
        'DESIGNER': 'Designer',
        'DESIGNER_SENIOR': 'Designer Sênior',
        'DESIGNER_JUNIOR': 'Designer Junior',
        'GENERAL_ASSISTANT': 'Assistente Geral'
    };

    return (
        <><Header /><SidebarProvider>
            <div className="min-h-screen flex w-full">
                <Sidebar>
                    <SidebarHeader>
                        <div className="flex items-center gap-2 px-2 py-2">
                            <Link href="/">
                                <Image src='/icon-b4desk.svg' width={25} height={25} alt="B4Desk" />
                            </Link>
                            <div className="flex flex-col">
                                <span className="font-semibold text-sm">Painel administrativo</span>
                                <span className="text-xs text-muted-foreground">B4Desk</span>
                            </div>
                        </div>
                    </SidebarHeader>

                    <SidebarContent>
                        <SidebarGroup>
                            <SidebarGroupLabel>Funcionalidades gerais</SidebarGroupLabel>
                            <SidebarGroupContent>
                                <SidebarMenu>
                                    {generalItens.map((item) => (
                                        <SidebarMenuItem key={item.url}>
                                            <SidebarMenuButton asChild>
                                                <Link href={item.url}>
                                                    <item.icon className="h-4 w-4" />
                                                    <span>{item.title}</span>
                                                    {item.new && <Badge variant="secondary" className="rounded-full">Novo</Badge>}
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </SidebarGroupContent>
                        </SidebarGroup>

                        <SidebarGroup>
                            <SidebarGroupLabel>Equipe</SidebarGroupLabel>
                            <SidebarGroupContent>
                                <SidebarMenu>
                                    {timeItens.map((item) => (
                                        <SidebarMenuItem key={item.url}>
                                            <SidebarMenuButton asChild>
                                                <Link href={item.url}>
                                                    <item.icon className="h-4 w-4" />
                                                    <span>{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </SidebarGroupContent>
                        </SidebarGroup>

                        {(user?.role === "ADMIN" || user?.role === "DEVELOPER") && (
                            <SidebarGroup>
                                <SidebarGroupLabel>Sistema</SidebarGroupLabel>
                                <SidebarGroupContent>
                                    <SidebarMenu>
                                        {systemSettings.map((item) => (
                                            <SidebarMenuItem key={item.url}>
                                                <SidebarMenuButton asChild>
                                                    <Link href={item.url}>
                                                        <item.icon className="h-4 w-4" />
                                                        <span>{item.title}</span>
                                                    </Link>
                                                </SidebarMenuButton>
                                            </SidebarMenuItem>
                                        ))}
                                    </SidebarMenu>
                                </SidebarGroupContent>
                            </SidebarGroup>
                        )}
                    </SidebarContent>

                    <SidebarFooter>
                        <div className="px-2 py-2 flex items-center gap-2">
                            <Avatar className="w-8 h-8 border-2 border-zinc-200 rounded-lg">
                                <AvatarImage src={session?.user?.image ?? "https://github.com/shadcn.png"} alt={session?.user?.name ?? "Usuário"} />
                                <AvatarFallback>{session?.user?.name ? session?.user?.name[0] : "U"}</AvatarFallback>
                            </Avatar>
                            <div>
                                <p className="text-sm font-semibold">{session?.user?.name}</p>
                                <p className="text-xs text-muted-foreground">
                                    {session?.user?.role ? roles[session.user.role as keyof typeof roles] : "Usuário"}
                                </p>
                            </div>
                        </div>
                    </SidebarFooter>
                </Sidebar>

                <SidebarInset>
                    <div className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
                        <SidebarTrigger className="m-0 xs:ml-4" />
                        <Separator orientation="vertical" className="mr-2 h-4" />
                        <div className="flex items-center gap-2">
                            <h1 className="text-lg font-semibold">Painel administrativo</h1>
                        </div>
                    </div>

                    <div className="flex-1 p-6">
                        <div className="max-w-6xl mx-auto">
                            <p className="text-muted-foreground mb-4 text-sm">
                                Bem-vindo ao painel de controle do sistema. Você pode gerenciar as funcionalides administrativas no menu lateral.
                                Tenha uma visão geral das principais métricas e estatísticas abaixo.
                            </p>

                            {loadingStats ? (
                                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                    {[...Array(6)].map((_, i) => (
                                        <Card key={i} className="animate-pulse">
                                            <CardHeader className="pb-3">
                                                <div className="h-4 bg-muted rounded w-3/4"></div>
                                            </CardHeader>
                                            <CardContent>
                                                <div className="h-8 bg-muted rounded w-1/2 mb-2"></div>
                                                <div className="h-3 bg-muted rounded w-full"></div>
                                            </CardContent>
                                        </Card>
                                    ))}
                                </div>
                            ) : (
                                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                    <Card className="hover:shadow-md transition-shadow">
                                        <CardHeader className="pb-3">
                                            <div className="flex items-center gap-2 justify-between flex-wrap">
                                                <div className="flex items-center gap-2">
                                                    <AlertCircle className="h-5 w-5 text-primary2" />
                                                    <CardTitle className="text-lg">Demandas</CardTitle>
                                                </div>
                                                <Button asChild variant="secondary" size="sm">
                                                    <Link href="/admin/demands">Ver todas</Link>
                                                </Button>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-3xl font-bold mb-2">
                                                {stats.pendingDemands}
                                            </div>
                                            <CardDescription>
                                                Demandas pendentes no mês atual
                                            </CardDescription>
                                        </CardContent>
                                    </Card>

                                    <Card className="hover:shadow-md transition-shadow">
                                        <CardHeader className="pb-3">
                                            <div className="flex items-center gap-2 justify-between flex-wrap">
                                                <div className="flex items-center gap-2">
                                                    <CheckCircle className="h-5 w-5 text-primary2" />
                                                    <CardTitle className="text-lg">Funcionários</CardTitle>
                                                </div>
                                                <Button asChild variant="secondary" size="sm">
                                                    <Link href="/admin/employee-profile">Ver perfis</Link>
                                                </Button>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-3xl font-bold mb-2">
                                                {stats.activeEmployees}
                                            </div>
                                            <CardDescription>
                                                Funcionários cadastrados e ativos
                                            </CardDescription>
                                        </CardContent>
                                    </Card>

                                    <Card className="hover:shadow-md transition-shadow">
                                        <CardHeader className="pb-3">
                                            <div className="flex items-center gap-2 justify-between flex-wrap">
                                                <div className="flex items-center gap-2">
                                                    <Bell className="h-5 w-5 text-primary2" />
                                                    <CardTitle className="text-lg">Notificações</CardTitle>
                                                </div>
                                                <Button asChild variant="secondary" size="sm">
                                                    <Link href="/notifications">Ver todas</Link>
                                                </Button>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-3xl font-bold mb-2">
                                                {stats.unreadNotifications}
                                            </div>
                                            <CardDescription>
                                                Notificações não lidas
                                            </CardDescription>
                                        </CardContent>
                                    </Card>

                                    <Card className="hover:shadow-md transition-shadow">
                                        <CardHeader className="pb-3 relative">
                                            <Badge
                                                className="text-xs absolute left-0 top-0 rounded-tl-xl rounded-tr-none rounded-br-none rounded-bl-none"
                                            >
                                                Novo
                                            </Badge>
                                            <div className="flex items-center gap-2 justify-between flex-wrap">
                                                <div className="flex items-center gap-2">
                                                    <TrendingUp className="h-5 w-5 text-primary2" />
                                                    <CardTitle className="text-lg">Orçamentos</CardTitle>
                                                </div>
                                                <Link href="/admin/quotes">
                                                    <Button variant="secondary" size="sm">
                                                        Ver todos
                                                    </Button>
                                                </Link>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <div className="text-3xl font-bold mb-2">
                                                {stats.quotes}
                                            </div>
                                            <CardDescription>
                                                Orçamentos criados
                                            </CardDescription>
                                        </CardContent>
                                    </Card>

                                    {(user?.role === "ADMIN" || user?.role === "DEVELOPER") && (
                                        <>
                                            <Card className="hover:shadow-md transition-shadow border-primary/20">
                                                <CardHeader className="pb-3">
                                                    <div className="flex items-center gap-2 justify-between flex-wrap">
                                                        <div className="flex items-center gap-2">
                                                            <Users className="h-5 w-5 text-primary2" />
                                                            <CardTitle className="text-lg">Usuários</CardTitle>
                                                        </div>
                                                        <Button asChild variant="secondary" size="sm">
                                                            <Link href="/admin/users">Gerenciar</Link>
                                                        </Button>
                                                    </div>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="text-3xl font-bold mb-2">
                                                        {stats.totalUsers}
                                                    </div>
                                                    <CardDescription>
                                                        Total de usuários cadastrados
                                                    </CardDescription>
                                                </CardContent>
                                            </Card>

                                            <Card className="hover:shadow-md transition-shadow border-primary/20">
                                                <CardHeader className="pb-3">
                                                    <div className="flex items-center gap-2 justify-between flex-wrap">
                                                        <div className="flex items-center gap-2">
                                                            <Clock className="h-5 w-5 text-primary2" />
                                                            <CardTitle className="text-lg">Controle de ponto</CardTitle>
                                                        </div>
                                                        <Button asChild variant="secondary" size="sm">
                                                            <Link href="/admin/point-record">Ver registros</Link>
                                                        </Button>
                                                    </div>
                                                </CardHeader>
                                                <CardContent>
                                                    <div className="text-3xl font-bold mb-2">
                                                        {stats.pointRecordsToday}
                                                    </div>
                                                    <CardDescription>
                                                        Usuários com registro de ponto hoje
                                                    </CardDescription>
                                                </CardContent>
                                            </Card>
                                        </>
                                    )}
                                </div>
                            )}

                            <Separator className="my-8" />

                            <div>
                                <div className="flex items-center gap-2 mb-4">
                                    <Layers2 size={18} />
                                    <h4 className="text-lg font-semibold">
                                        Atalhos
                                    </h4>
                                </div>
                                <div className="grid gap-6 md:grid-cols-2 xl:grid-cols-4">
                                    <Link href="/create-client">
                                        <Card className="hover:shadow-md transition-shadow dark:hover:opacity-80">
                                            <CardHeader className="pb-3">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-start gap-2">
                                                        <UserPlus className="h-5 w-5 min-w-5 mt-1 text-primary2" />
                                                        <CardTitle className="text-lg">Cadastrar novo cliente</CardTitle>
                                                    </div>
                                                </div>
                                            </CardHeader>
                                            <CardContent>
                                                <Separator className="my-4" />
                                                <CardDescription>
                                                    Clique pata ir à página com formulário para cadastrar um novo cliente no sistema
                                                </CardDescription>
                                            </CardContent>
                                        </Card>
                                    </Link>
                                    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
                                        <DialogTrigger asChild>
                                            <Card className="hover:shadow-md transition-shadow cursor-pointer dark:hover:opacity-80">
                                                <CardHeader className="pb-3">
                                                    <div className="flex items-center justify-between">
                                                        <div className="flex items-start gap-2">
                                                            <CalendarPlus className="h-5 w-5 mt-1 min-w-5 text-primary2" />
                                                            <CardTitle className="text-lg">
                                                                Criar planejamento
                                                            </CardTitle>
                                                        </div>
                                                    </div>
                                                </CardHeader>
                                                <CardContent>
                                                    <Separator className="my-4" />
                                                    <CardDescription>
                                                        Clique para escolher um cliente e criar um novo planejamento
                                                    </CardDescription>
                                                </CardContent>
                                            </Card>
                                        </DialogTrigger>
                                        <DialogContent>
                                            <DialogHeader>
                                                <DialogTitle>Novo planejamento</DialogTitle>
                                            </DialogHeader>
                                            <div onFocus={(e) => e.stopPropagation()}>
                                                <CreatePlanning isShowTitle={false} onClose={() => setDialogOpen(false)} />
                                            </div>
                                        </DialogContent>
                                    </Dialog>
                                    <Card className="hover:shadow-md transition-shadow">
                                        <CardHeader className="pb-3">
                                            <div className="flex items-center justify-between">
                                                <div className="flex items-start gap-2">
                                                    <ListTodo className="h-5 w-5 min-w-5 mt-1 text-primary2" />
                                                    <CardTitle className="text-lg">Criar demanda pontual</CardTitle>
                                                </div>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <CardDescription>
                                                Crie uma nova demanda pontual
                                            </CardDescription>
                                            <Separator className="my-4" />
                                            <GeneralDemandForm
                                                clients={clients}
                                                onSuccess={() => { }}
                                                buttonLabel="Nova demanda pontual"
                                            />
                                        </CardContent>
                                    </Card>
                                </div>
                            </div>
                        </div>
                    </div>

                    <Footer />
                </SidebarInset>
            </div>
        </SidebarProvider></>
    );
}
