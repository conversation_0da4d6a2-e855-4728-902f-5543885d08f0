"use client"

import { MessageSquareMore, Users, ListTodo, Bell, FileClock, UserCog, Home, Settings, PanelTop } from "lucide-react"
import { Footer } from "../components/footer"
import Link from 'next/link'
import { useSession } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { NotAllowed } from "../components/not-allowed"
import Loading from "../components/ui/loading"
import { Button } from "../components/ui/button"
import { User } from "@prisma/client"
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarInset,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarProvider,
    SidebarTrigger,
} from "../components/ui/sidebar"
import { Separator } from "../components/ui/separator"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "../components/ui/card"
import { Header } from "../components/header"

// Dados dos itens do menu
const menuItems = [
    {
        title: "Demandas",
        url: "/admin/demands",
        icon: ListTodo,
        description: "Gerenciar todas as demandas e atribuições da equipe"
    },
    {
        title: "Perfis de funcionários",
        url: "/admin/employee-profile",
        icon: UserCog,
        description: "Acompanhar e gerenciar os perfis dos funcionários"
    },
    {
        title: "Notificações",
        url: "/notifications",
        icon: Bell,
        description: "Gerenciar todas as notificações do sistema"
    },
    {
        title: "Feedbacks",
        url: "/admin/feedbacks",
        icon: MessageSquareMore,
        description: "Visualizar feedbacks dos usuários"
    }
];

const adminOnlyItems = [
    {
        title: "Controle de ponto",
        url: "/admin/point-record",
        icon: FileClock,
        description: "Acompanhar e gerenciar o registro de ponto do time"
    },
    {
        title: "Usuários",
        url: "/admin/users",
        icon: Users,
        description: "Gerenciar usuários do sistema"
    }
];

export default function PanelPage() {
    const { data: session, status } = useSession();
    const [isAdmin, setIsAdmin] = useState(false);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [user, setUser] = useState<User | null>(null);
    const router = useRouter();

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();

                        const hasPermission =
                            user?.role === "ADMIN" ||
                            user?.role === "DEVELOPER" ||
                            user?.role === "GENERAL_ASSISTANT";

                        setIsAdmin(hasPermission);
                        setUser(user);
                    } else {
                        console.error("Falha ao buscar dados do usuário:", response.status);
                        setIsAdmin(false);
                    }
                } else {
                    setIsAdmin(false);
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
                setIsAdmin(false);
            } finally {
                setIsFetchingRole(false);
            }
        };

        if (status === "authenticated") {
            fetchUserRole();
        } else if (status === "unauthenticated") {
            router.push("/auth/signin");
        }
    }, [status, session, router]);

    if (isFetchingRole) {
        return (
            <div className="min-h-screen flex justify-center items-center">
                <Loading />
            </div>
        );
    }

    if (!isAdmin) {
        return <NotAllowed page="/" />;
    }

    return (
        <SidebarProvider>
            <div className="min-h-screen flex w-full">
                <Sidebar>
                    <SidebarHeader>
                        <div className="flex items-center gap-2 px-2 py-2">
                            <PanelTop className="h-6 w-6" />
                            <div className="flex flex-col">
                                <span className="font-semibold text-sm">Painel administrativo</span>
                                <span className="text-xs text-muted-foreground">B4Desk</span>
                            </div>
                        </div>
                    </SidebarHeader>

                    <SidebarContent>
                        <SidebarGroup>
                            <SidebarGroupLabel>Navegação</SidebarGroupLabel>
                            <SidebarGroupContent>
                                <SidebarMenu>
                                    <SidebarMenuItem>
                                        <SidebarMenuButton asChild>
                                            <Link href="/dashboard">
                                                <Home className="h-4 w-4" />
                                                <span>Dashboard</span>
                                            </Link>
                                        </SidebarMenuButton>
                                    </SidebarMenuItem>
                                </SidebarMenu>
                            </SidebarGroupContent>
                        </SidebarGroup>

                        <SidebarGroup>
                            <SidebarGroupLabel>Funcionalidades Gerais</SidebarGroupLabel>
                            <SidebarGroupContent>
                                <SidebarMenu>
                                    {menuItems.map((item) => (
                                        <SidebarMenuItem key={item.url}>
                                            <SidebarMenuButton asChild>
                                                <Link href={item.url}>
                                                    <item.icon className="h-4 w-4" />
                                                    <span>{item.title}</span>
                                                </Link>
                                            </SidebarMenuButton>
                                        </SidebarMenuItem>
                                    ))}
                                </SidebarMenu>
                            </SidebarGroupContent>
                        </SidebarGroup>

                        {(user?.role === "ADMIN" || user?.role === "DEVELOPER") && (
                            <SidebarGroup>
                                <SidebarGroupLabel>Administração</SidebarGroupLabel>
                                <SidebarGroupContent>
                                    <SidebarMenu>
                                        {adminOnlyItems.map((item) => (
                                            <SidebarMenuItem key={item.url}>
                                                <SidebarMenuButton asChild>
                                                    <Link href={item.url}>
                                                        <item.icon className="h-4 w-4" />
                                                        <span>{item.title}</span>
                                                    </Link>
                                                </SidebarMenuButton>
                                            </SidebarMenuItem>
                                        ))}
                                    </SidebarMenu>
                                </SidebarGroupContent>
                            </SidebarGroup>
                        )}
                    </SidebarContent>

                    <SidebarFooter>
                        <div className="px-2 py-2">
                            <p className="text-xs text-muted-foreground">
                                Logado como: {session?.user?.name}
                            </p>
                        </div>
                    </SidebarFooter>
                </Sidebar>

                <SidebarInset>
                    <Header />
                    <div className="flex h-16 shrink-0 items-center gap-2 border-b px-4">
                        <SidebarTrigger className="ml-5" />
                        <Separator orientation="vertical" className="mr-2 h-4" />
                        <div className="flex items-center gap-2">
                            <h1 className="text-lg font-semibold">Painel administrativo</h1>
                        </div>
                    </div>

                    <div className="flex-1 p-6">
                        <div className="max-w-4xl mx-auto">
                                <p className="text-muted-foreground">
                                    Gerencie as funcionalidades administrativas do sistema através do menu lateral.
                                </p>

                            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                                {menuItems.map((item) => (
                                    <Card key={item.url} className="hover:shadow-md transition-shadow">
                                        <CardHeader className="pb-3">
                                            <div className="flex items-center gap-2">
                                                <item.icon className="h-5 w-5 text-primary" />
                                                <CardTitle className="text-lg">{item.title}</CardTitle>
                                            </div>
                                        </CardHeader>
                                        <CardContent>
                                            <CardDescription className="mb-4">
                                                {item.description}
                                            </CardDescription>
                                            <Button asChild variant="outline" className="w-full">
                                                <Link href={item.url}>
                                                    Acessar
                                                </Link>
                                            </Button>
                                        </CardContent>
                                    </Card>
                                ))}

                                {(user?.role === "ADMIN" || user?.role === "DEVELOPER") &&
                                    adminOnlyItems.map((item) => (
                                        <Card key={item.url} className="hover:shadow-md transition-shadow border-primary/20">
                                            <CardHeader className="pb-3">
                                                <div className="flex items-center gap-2">
                                                    <item.icon className="h-5 w-5 text-primary" />
                                                    <CardTitle className="text-lg">{item.title}</CardTitle>
                                                </div>
                                            </CardHeader>
                                            <CardContent>
                                                <CardDescription className="mb-4">
                                                    {item.description}
                                                </CardDescription>
                                                <Button asChild variant="outline" className="w-full">
                                                    <Link href={item.url}>
                                                        Acessar
                                                    </Link>
                                                </Button>
                                            </CardContent>
                                        </Card>
                                    ))
                                }
                            </div>
                        </div>
                    </div>

                    <Footer />
                </SidebarInset>
            </div>
        </SidebarProvider>
    );
}
