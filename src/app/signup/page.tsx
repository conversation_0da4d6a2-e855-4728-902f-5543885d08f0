"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { Card, CardContent, CardDescription, CardTitle } from "../components/ui/card";
import { Input } from "../components/ui/input";
import { Button } from "../components/ui/button";
import { Ellipsis } from "lucide-react";

export default function SignUpPage() {
    const [name, setName] = useState("");
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState("");
    const router = useRouter();

    const currentYear = new Date().getFullYear();

    const handleSignUp = async () => {
        setIsLoading(true);
        setError("");
        try {
            const res = await fetch('/api/signup', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ name, email, password })
            });
            const data = await res.json();
            if (!res.ok) {
                setError(data.message || 'Erro ao criar conta');
                setIsLoading(false);
            } else {
                router.push('/login');
            }
        } catch (err) {
            setError(err as string);
            setIsLoading(false);
        }
    };

    return (
        <div className="flex min-h-[100dvh] bg-white text-black login-page">
            <div className="flex-1 sm:mt-24 md:m-0 flex justify-center bg-white">
                <div className="flex flex-col items-center justify-center">
                    <h2 className="text-lg mb-2">
                        Bem-vindo ao <span className="font-bold">B4Desk</span>
                    </h2>
                    <Image src="/icon-b4desk.svg" alt="B4Desk" width={50} height={50} />
                    <Card className="w-full sm:w-96 p-8 mx-4 mt-8 lg:mt-8 bg-white border-zinc-200">
                        <CardTitle className="text-black mb-2">Crie sua conta</CardTitle>
                        <CardDescription className="text-zinc-600">Preencha os dados abaixo para criar sua conta</CardDescription>
                        <CardContent className="p-0">
                            {error && <p className="text-sm text-red-500">{error}</p>}
                            <div className="space-y-2 mt-2">
                                <Input
                                    type="text"
                                    placeholder="Nome"
                                    value={name}
                                    onChange={(e) => setName(e.target.value)}
                                    required
                                    className="placeholder:text-sm"
                                />
                                <Input
                                    type="email"
                                    placeholder="E-mail"
                                    value={email}
                                    onChange={(e) => setEmail(e.target.value)}
                                    required
                                    className="placeholder:text-sm"
                                />
                                <Input
                                    type="password"
                                    placeholder="Senha"
                                    value={password}
                                    onChange={(e) => setPassword(e.target.value)}
                                    required
                                    className="placeholder:text-sm"
                                />
                            </div>
                            <Button
                                className="w-full mt-4 font-bold bg-primary text-primary-foreground border"
                                onClick={handleSignUp}
                                disabled={isLoading}
                            >
                                {isLoading ? <Ellipsis /> : 'Cadastrar'}
                            </Button>
                        </CardContent>
                        <div className="flex justify-center my-3 text-center">
                            <span className="text-zinc-500 text-sm">Já tem uma conta? <Link href="/login" className="text-blue-600 hover:underline">Faça login</Link></span>
                        </div>
                    </Card>
                </div>
                <p className="absolute bottom-4 text-center w-full text-sm text-zinc-500">
                    © {currentYear} B4Desk - B4 Comunicação. Todos os direitos reservados.
                </p>
            </div>
            <div className="hidden md:block flex-1 bg-gradient-to-bl from-[#db5643] to-[#70aa87]">
            </div>
        </div>
    );
}
