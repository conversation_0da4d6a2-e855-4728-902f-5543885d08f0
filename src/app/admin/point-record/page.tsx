"use client"

import { useEffect, useState, useRef } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { Clock, Edit, MoveLeft, SortAsc, SortDesc, Trash, ChevronLeft, ChevronRight, History } from 'lucide-react';
import { Header } from '../../components/header';
import { Button } from '../../components/ui/button';
import { Input } from '../../components/ui/input';
import { Table, TableBody, TableCaption, TableCell, TableHead, TableHeader, TableRow } from '../../components/ui/table';
import { Footer } from '../../components/footer';
import Loading from '../../components/ui/loading';
import { NotAllowed } from '../../components/not-allowed';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '../../components/ui/dropdown-menu';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { CreatePointRecord } from '../../components/create-point-record';
import { EditPointRecord } from '../../components/edit-point-record';
import { PointRecordAudit } from '../../components/point-record-audit';
import { ExportPDFPointRecord } from '../../components/export-pdf-point-record';
import { ExcusedDaysManager } from '../../components/excused-days-manager';
import { toast } from 'sonner';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/app/components/ui/alert-dialog';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/app/components/ui/dialog';

interface PointRecord {
    id: string;
    userId: string;
    userName: string;
    userEmail: string;
    clockIn?: string;
    clockOut?: string;
    date: string;
    totalHours?: number;
    timeBank?: number;
    createdAt: string;
    createdBy?: string;
    createdByName?: string;
}

interface UserDailyHours {
    userId: string;
    userName: string;
    userEmail: string;
    totalHours: number;
    recordCount: number;
}

export default function PointRecordPage() {
    const { data: session, status } = useSession();
    const [records, setRecords] = useState<PointRecord[]>([]);
    const [filteredRecords, setFilteredRecords] = useState<PointRecord[]>([]);
    const [searchQuery, setSearchQuery] = useState('');
    const [sortOption, setSortOption] = useState<'date_desc' | 'date_asc' | 'name_asc' | 'name_desc'>('date_desc');
    const [isAdmin, setIsAdmin] = useState(false);
    const [isLoading, setIsLoading] = useState(true);
    const [editingRecord, setEditingRecord] = useState<PointRecord | null>(null);
    const [viewingAudit, setViewingAudit] = useState<PointRecord | null>(null);
    const [timeBankData, setTimeBankData] = useState<{ userId: string, userName: string, userEmail: string, totalTimeBank: number }[]>([]);
    const [excusedDays, setExcusedDays] = useState<{ id: string, userId: string, date: string, reason: string }[]>([]);
    const [selectedDate, setSelectedDate] = useState<string>('');
    const [availableDates, setAvailableDates] = useState<string[]>([]);
    const [canScrollLeft, setCanScrollLeft] = useState(false);
    const [canScrollRight, setCanScrollRight] = useState(false);
    const scrollContainerRef = useRef<HTMLDivElement>(null);
    const router = useRouter();

    useEffect(() => {
        if (status === "authenticated") {
            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const response = await fetch(`/api/users/${session.user.email}`);
                        if (response.ok) {
                            const user = await response.json();
                            const adminRoles = ["ADMIN", "DEVELOPER"];
                            setIsAdmin(user?.role ? adminRoles.includes(user.role) : false);
                        }
                    }
                } catch (error) {
                    console.error("Error fetching user role:", error);
                }
            };

            fetchUserRole();
        } else if (status === 'unauthenticated') {
            router.push('/');
        }
    }, [status, session, router]);

    useEffect(() => {
        if (status === "authenticated" && isAdmin) {
            setIsLoading(true);
            Promise.all([
                fetch('/api/admin/point-record').then(res => res.json()),
                fetch('/api/admin/time-bank').then(res => res.json()),
                fetch('/api/admin/excused-days').then(res => res.json())
            ])
                .then(([recordsData, timeBankData, excusedDaysData]) => {
                    setRecords(recordsData);
                    setTimeBankData(timeBankData);
                    setExcusedDays(excusedDaysData);

                    const uniqueDates = [...new Set(recordsData.map((record: PointRecord) => record.date))] as string[];
                    const sortedDates = uniqueDates.sort((a: string, b: string) => new Date(b).getTime() - new Date(a).getTime());
                    setAvailableDates(sortedDates);

                    const today = format(new Date(), 'yyyy-MM-dd');
                    const defaultDate = sortedDates.includes(today) ? today : sortedDates[0] || '';
                    setSelectedDate(defaultDate);
                })
                .catch((error) => {
                    console.error("Erro ao buscar dados:", error);
                })
                .finally(() => setIsLoading(false));
        }
    }, [status, isAdmin]);

    useEffect(() => {
        let result = [...records];

        if (selectedDate) {
            result = result.filter(record => record.date === selectedDate);
        }

        if (searchQuery.trim() !== '') {
            const query = searchQuery.toLowerCase();
            result = result.filter(record =>
                record.userName?.toLowerCase().includes(query) ||
                record.userEmail?.toLowerCase().includes(query)
            );
        }

        switch (sortOption) {
            case 'date_desc':
                result.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
                break;
            case 'date_asc':
                result.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());
                break;
            case 'name_asc':
                result.sort((a, b) => a.userName.localeCompare(b.userName));
                break;
            case 'name_desc':
                result.sort((a, b) => b.userName.localeCompare(a.userName));
                break;
        }

        setFilteredRecords(result);
    }, [records, searchQuery, sortOption, selectedDate]);

    useEffect(() => {
        checkScrollButtons();
    }, [availableDates]);

    const handleDeleteRecord = async (recordId: string) => {
        const response = await fetch(`/api/admin/point-record/${recordId}`, {
            method: 'DELETE'
        });

        if (!response.ok) {
            throw new Error('Erro ao excluir registro');
        }
        toast.success('Registro excluído com sucesso!');
        refreshData();
    };

    const refreshData = () => {
        if (isAdmin) {
            setIsLoading(true);
            Promise.all([
                fetch('/api/admin/point-record').then(res => res.json()),
                fetch('/api/admin/time-bank').then(res => res.json()),
                fetch('/api/admin/excused-days').then(res => res.json())
            ])
                .then(([recordsData, timeBankData, excusedDaysData]) => {
                    setRecords(recordsData);
                    setTimeBankData(timeBankData);
                    setExcusedDays(excusedDaysData);

                    const uniqueDates = [...new Set(recordsData.map((record: PointRecord) => record.date))] as string[];
                    const sortedDates = uniqueDates.sort((a: string, b: string) => new Date(b).getTime() - new Date(a).getTime());
                    setAvailableDates(sortedDates);

                    const today = format(new Date(), 'yyyy-MM-dd');
                    if (!sortedDates.includes(selectedDate)) {
                        const defaultDate = sortedDates.includes(today) ? today : sortedDates[0] || '';
                        setSelectedDate(defaultDate);
                    }
                })
                .catch((error) => {
                    console.error("Erro ao buscar dados:", error);
                })
                .finally(() => setIsLoading(false));
        }
    };

    const formatTime = (timeString?: string) => {
        if (!timeString) return '-';
        return format(new Date(timeString), 'HH:mm', { locale: ptBR });
    };

    const formatDate = (dateString: string) => {
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day);
        return format(date, 'dd/MM/yyyy', { locale: ptBR });
    };

    const formatHours = (hours?: number) => {
        if (!hours) return '-';
        const h = Math.floor(hours);
        const m = Math.round((hours - h) * 60);
        return `${h}h ${m}m`;
    };

    const getDailyHoursByUser = (): UserDailyHours[] => {
        if (!selectedDate) return [];

        const dayRecords = records.filter(record => record.date === selectedDate);
        const userHours = dayRecords.reduce((acc: Record<string, UserDailyHours>, record) => {
            if (!acc[record.userId]) {
                acc[record.userId] = {
                    userId: record.userId,
                    userName: record.userName,
                    userEmail: record.userEmail,
                    totalHours: 0,
                    recordCount: 0
                };
            }
            if (record.totalHours) {
                acc[record.userId].totalHours += record.totalHours;
                acc[record.userId].recordCount += 1;
            }
            return acc;
        }, {});

        return Object.values(userHours).sort((a, b) => b.totalHours - a.totalHours);
    };

    const formatDateButton = (dateString: string) => {
        const [year, month, day] = dateString.split('-').map(Number);
        const date = new Date(year, month - 1, day);
        const today = new Date();
        const weekDays = ['Domingo', 'Segunda', 'Terça', 'Quarta', 'Quinta', 'Sexta', 'Sábado'];

        if (date.toDateString() === today.toDateString()) {
            return `Hoje (${weekDays[date.getDay()]})`;
        }

        const yesterday = new Date(today);
        yesterday.setDate(today.getDate() - 1);
        if (date.toDateString() === yesterday.toDateString()) {
            return `Ontem (${weekDays[date.getDay()]})`;
        }

        return `${format(date, 'dd/MM', { locale: ptBR })} (${weekDays[date.getDay()]})`;
    };

    const checkScrollButtons = () => {
        if (scrollContainerRef.current) {
            const { scrollLeft, scrollWidth, clientWidth } = scrollContainerRef.current;
            setCanScrollLeft(scrollLeft > 0);
            setCanScrollRight(scrollLeft < scrollWidth - clientWidth);
        }
    };

    const scrollLeft = () => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollBy({ left: -200, behavior: 'smooth' });
            setTimeout(checkScrollButtons, 300);
        }
    };

    const scrollRight = () => {
        if (scrollContainerRef.current) {
            scrollContainerRef.current.scrollBy({ left: 200, behavior: 'smooth' });
            setTimeout(checkScrollButtons, 300);
        }
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                {isLoading ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <>
                        <div className="flex flex-row justify-between items-start gap-2 mb-2">
                            <Button variant="outline" onClick={() => router.push("/panel")} size="icon">
                                <MoveLeft />
                            </Button>
                            <div className="flex flex-col items-end gap-2 group">
                                <div className="flex items-center gap-2">
                                    <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                        Controle de ponto
                                    </h1>
                                    <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                        <Clock size={24} color="#db5743" />
                                    </div>
                                </div>
                                <CreatePointRecord onSuccess={refreshData} />
                            </div>
                        </div>

                        <div className="flex flex-col lg:flex-row gap-4 justify-between mb-4">
                            <div className='w-full'>
                                <div className="w-full max-w-lg">
                                    <Input
                                        type="text"
                                        placeholder="Pesquisar por nome ou email"
                                        className="placeholder:text-sm"
                                        value={searchQuery}
                                        onChange={(e) => setSearchQuery(e.target.value)}
                                    />
                                </div>
                                {availableDates.length > 0 && (
                                    <div className="mt-8">
                                        <h3 className="text-sm font-medium mb-3">Selecionar dia:</h3>
                                        <div className="flex items-center gap-2">
                                            {canScrollLeft && (
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    className="h-8 w-8 bg-background shadow-md flex-shrink-0"
                                                    onClick={scrollLeft}
                                                >
                                                    <ChevronLeft size={16} />
                                                </Button>
                                            )}

                                            <div
                                                ref={scrollContainerRef}
                                                className="flex gap-2 overflow-x-hidden"
                                                style={{
                                                    maxWidth: '600px',
                                                    scrollbarWidth: 'none',
                                                    msOverflowStyle: 'none'
                                                }}
                                                onScroll={checkScrollButtons}
                                            >
                                                {availableDates.map((date) => (
                                                    <Button
                                                        key={date}
                                                        variant={selectedDate === date ? "default" : "outline"}
                                                        size="sm"
                                                        onClick={() => setSelectedDate(date)}
                                                        className="flex-shrink-0 min-w-[80px]"
                                                    >
                                                        {formatDateButton(date)}
                                                    </Button>
                                                ))}
                                            </div>

                                            {canScrollRight && (
                                                <Button
                                                    variant="outline"
                                                    size="icon"
                                                    className="h-8 w-8 bg-background shadow-md flex-shrink-0"
                                                    onClick={scrollRight}
                                                >
                                                    <ChevronRight size={16} />
                                                </Button>
                                            )}
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className='flex gap-2 flex-col'>
                                <Dialog>
                                    <DialogTrigger asChild>
                                        <Button>
                                            Ver banco de horas ({
                                                timeBankData.length === 0 ? 'nenhum colaborador' :
                                                    timeBankData.length === 1 ? `${timeBankData.length} colaborador` : `${timeBankData.length} colaboradores`
                                            })
                                        </Button>
                                    </DialogTrigger>
                                    <DialogContent className="max-w-2xl">
                                        <DialogHeader>
                                            <DialogTitle>Banco de horas por colaborador</DialogTitle>
                                        </DialogHeader>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 max-h-96 overflow-y-auto">
                                            {timeBankData
                                                .sort((a, b) => b.totalTimeBank - a.totalTimeBank)
                                                .map((user) => (
                                                    <div key={user.userId} className="p-3 border rounded">
                                                        <div className="font-medium">{user.userName}</div>
                                                        <div className="text-sm text-muted-foreground">{user.userEmail}</div>
                                                        <div className={`text-lg font-bold mt-1 ${
                                                            user.totalTimeBank >= 0 ? 'text-green-600' : 'text-red-600'
                                                        }`}>
                                                            {user.totalTimeBank >= 0 ? '+' : ''}{formatHours(Math.abs(user.totalTimeBank))}
                                                        </div>
                                                    </div>
                                                ))
                                            }
                                        </div>
                                        {timeBankData.length === 0 && (
                                            <p className="text-center text-muted-foreground mb-4">Nenhum colaborador com saldo de horas</p>
                                        )}
                                    </DialogContent>
                                </Dialog>
                                
                                <ExcusedDaysManager />
                                
                                <ExportPDFPointRecord records={records} timeBankData={timeBankData} excusedDays={excusedDays} />

                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="outline" className="flex gap-2 w-full sm:w-auto">
                                            {sortOption.includes('date')
                                                ? <span>Ordenar por data</span>
                                                : <span>Ordenar por nome</span>
                                            }
                                            {sortOption.includes('asc') ? <SortAsc size={16} /> : <SortDesc size={16} />}
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem
                                            onClick={() => setSortOption('date_desc')}
                                            className={sortOption === 'date_desc' ? 'bg-muted' : ''}
                                        >
                                            <span className="pr-2">Mais recentes</span>
                                            <SortDesc size={16} />
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => setSortOption('date_asc')}>
                                            <span className="pr-2">Mais antigos</span>
                                            <SortAsc size={16} />
                                        </DropdownMenuItem>
                                        <DropdownMenuSeparator />
                                        <DropdownMenuItem onClick={() => setSortOption('name_asc')}>
                                            <span className="pr-2">Nome (A-Z)</span>
                                            <SortAsc size={16} />
                                        </DropdownMenuItem>
                                        <DropdownMenuItem onClick={() => setSortOption('name_desc')}>
                                            <span className="pr-2">Nome (Z-A)</span>
                                            <SortDesc size={16} />
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>

                        </div>

                        {selectedDate && getDailyHoursByUser().length > 0 && (
                            <div className="mb-6 p-4 bg-muted rounded-lg">
                                <h3 className="text-lg font-semibold mb-3">
                                    Horas trabalhadas em {formatDate(selectedDate)}
                                </h3>
                                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                    {getDailyHoursByUser().map((user) => (
                                        <div key={user.userId} className="bg-background p-3 rounded border">
                                            <div className="font-medium">{user.userName}</div>
                                            <div className="text-sm text-muted-foreground">{user.userEmail}</div>
                                            <div className="flex justify-between items-center mt-2">
                                                <div className="text-lg font-bold text-blue-600">
                                                    {formatHours(user.totalHours)}
                                                </div>
                                                <div className="text-xs text-muted-foreground">
                                                    {user.recordCount} registro{user.recordCount !== 1 ? 's' : ''}
                                                </div>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        )}

                        <Table>
                            <TableCaption>Total de registros: {filteredRecords.length}</TableCaption>
                            <TableHeader>
                                <TableRow>
                                    <TableHead>Data</TableHead>
                                    <TableHead>Usuário</TableHead>
                                    <TableHead>Entrada</TableHead>
                                    <TableHead>Saída</TableHead>
                                    <TableHead>Total</TableHead>
                                    <TableHead>Criado por</TableHead>
                                    <TableHead>Ações</TableHead>
                                </TableRow>
                            </TableHeader>
                            <TableBody>
                                {filteredRecords.length > 0 ? (
                                    filteredRecords.map((record) => (
                                        <TableRow key={record.id}>
                                            <TableCell>{formatDate(record.date)}</TableCell>
                                            <TableCell>
                                                <div>
                                                    <div className="font-medium">{record.userName}</div>
                                                    <div className="text-sm text-muted-foreground">{record.userEmail}</div>
                                                </div>
                                            </TableCell>
                                            <TableCell>{formatTime(record.clockIn)}</TableCell>
                                            <TableCell>{formatTime(record.clockOut)}</TableCell>
                                            <TableCell>{formatHours(record.totalHours)}</TableCell>
                                            <TableCell>
                                                <div className="text-sm cursor-pointer">
                                                    {record.createdBy ? (
                                                        <span className="text-orange-600 font-medium hover:underline">Admin</span>
                                                    ) : (
                                                        <span className="text-blue-600 font-medium hover:underline">Usuário</span>
                                                    )}
                                                    {record.createdByName && (
                                                        <div className="text-xs text-muted-foreground">{record.createdByName}</div>
                                                    )}
                                                </div>
                                            </TableCell>
                                            <TableCell>
                                                <div className="flex gap-1">
                                                    <Button
                                                        variant="outline"
                                                        size="icon"
                                                        onClick={() => setViewingAudit(record)}
                                                        title='Ver histórico de alterações'
                                                    >
                                                        <History size={16} />
                                                    </Button>
                                                    <Dialog open={editingRecord?.id === record.id} onOpenChange={(open) => !open && setEditingRecord(null)}>
                                                        <DialogTrigger asChild>
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                onClick={() => setEditingRecord(record)}
                                                                title='Editar registro'
                                                            >
                                                                <Edit size={16} />
                                                            </Button>
                                                        </DialogTrigger>
                                                        {editingRecord && (
                                                            <EditPointRecord
                                                                record={editingRecord}
                                                                onSuccess={refreshData}
                                                                onClose={() => setEditingRecord(null)}
                                                            />
                                                        )}
                                                    </Dialog>
                                                    <AlertDialog>
                                                        <AlertDialogTrigger asChild>
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                title='Excluir registro'
                                                            >
                                                                <Trash size={16} color="red" />
                                                            </Button>
                                                        </AlertDialogTrigger>
                                                        <AlertDialogContent>
                                                            <AlertDialogHeader>
                                                                <AlertDialogTitle>Tem certeza que deseja excluir este registro?</AlertDialogTitle>
                                                                <AlertDialogDescription>
                                                                    Esta ação não pode ser desfeita.
                                                                </AlertDialogDescription>
                                                            </AlertDialogHeader>
                                                            <AlertDialogFooter>
                                                                <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                                                <AlertDialogAction onClick={() => handleDeleteRecord(record.id)}>Continuar</AlertDialogAction>
                                                            </AlertDialogFooter>
                                                        </AlertDialogContent>
                                                    </AlertDialog>
                                                </div>
                                                <Dialog open={viewingAudit?.id === record.id} onOpenChange={(open) => !open && setViewingAudit(null)}>
                                                    {viewingAudit && (
                                                        <PointRecordAudit
                                                            recordId={viewingAudit.id}
                                                        />
                                                    )}
                                                </Dialog>
                                            </TableCell>
                                        </TableRow>
                                    ))
                                ) : (
                                    <TableRow>
                                        <TableCell colSpan={7} className="text-center py-6">
                                            Nenhum registro encontrado
                                        </TableCell>
                                    </TableRow>
                                )}
                            </TableBody>
                        </Table>
                    </>
                ) : (
                    <NotAllowed page='/panel' />
                )}
            </div>
            <Footer />
        </div>
    );
}