"use client";

import { useEffect, useState } from "react";
import { useSession } from "next-auth/react";
import { Feedback } from "@/types/feedback";
import { toast } from "sonner";
import { NotAllowed } from "@/app/components/not-allowed";
import { AlertCircle, Bug, HelpCircle, Lightbulb, MessageSquarePlusIcon, MoveLeft } from "lucide-react";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Badge } from "@/app/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import Loading from "@/app/components/ui/loading";
import { Header } from "@/app/components/header";
import { Footer } from "@/app/components/footer";
import { Button } from "@/app/components/ui/button";
import { useRouter } from "next/navigation";

export default function FeedbacksPage() {
    const { data: session, status } = useSession();
    const [feedbacks, setFeedbacks] = useState<Feedback[]>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [isAdmin, setIsAdmin] = useState(false);
    const router = useRouter();

    useEffect(() => {
        const checkUserRole = async () => {
            if (session?.user?.email) {
                try {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();
                        setIsAdmin(["ADMIN", "DEVELOPER", "GENERAL_ASSISTANT"].includes(user?.role || ""));
                    }
                } catch (error) {
                    console.error("Erro ao verificar função do usuário:", error);
                }
            }
        };

        const fetchFeedbacks = async () => {
            try {
                const response = await fetch("/api/feedback");
                if (response.ok) {
                    const data = await response.json();
                    setFeedbacks(data);
                } else {
                    toast.error("Erro ao carregar feedbacks");
                }
            } catch (error) {
                console.error("Erro ao buscar feedbacks:", error);
                toast.error("Erro ao carregar feedbacks");
            } finally {
                setIsLoading(false);
            }
        };

        if (status === "authenticated") {
            checkUserRole().then(() => {
                fetchFeedbacks();
            });
        } else if (status === "unauthenticated") {
            setIsLoading(false);
        }
    }, [status, session]);

    const updateFeedbackStatus = async (id: string, status: string) => {
        try {
            const response = await fetch(`/api/feedback/${id}`, {
                method: "PATCH",
                headers: {
                    "Content-Type": "application/json",
                },
                body: JSON.stringify({ status }),
            });

            if (response.ok) {
                setFeedbacks(
                    feedbacks.map((feedback) =>
                        feedback.id === id ? { ...feedback, status: status as Feedback['status'] } : feedback
                    )
                );
                toast.success("Status atualizado com sucesso");
            } else {
                toast.error("Erro ao atualizar status");
            }
        } catch (error) {
            console.error("Erro ao atualizar status:", error);
            toast.error("Erro ao atualizar status");
        }
    };

    const getTypeIcon = (type: string) => {
        switch (type) {
            case "BUG":
                return <Bug className="h-4 w-4 text-red-500" />;
            case "QUESTION":
                return <HelpCircle className="h-4 w-4 text-blue-500" />;
            case "SUGGESTION":
                return <Lightbulb className="h-4 w-4 text-yellow-500" />;
            default:
                return <AlertCircle className="h-4 w-4" />;
        }
    };

    const getStatusColor = (status: string) => {
        switch (status) {
            case "OPEN":
                return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200";
            case "IN_PROGRESS":
                return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200";
            case "CLOSED":
                return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200";
            default:
                return "";
        }
    };

    if (isLoading) {
        return (
            <>
                <Header />
                <div className="min-h-[80vh] flex flex-col justify-center items-center">
                    <Loading />
                </div>
                <Footer />
            </>
        )
    }

    if (!isAdmin) {
        return <NotAllowed page="/" />;
    }

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:p-8 flex-grow">
                <div className="flex items-center justify-between">
                    <Button
                        variant="outline"
                        onClick={() => router.push('/panel')}
                    >
                        <MoveLeft className="h-4 w-4" />
                        Voltar
                    </Button>
                    <div className="flex items-center gap-2 group">
                        <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                            Gerenciar feedbacks
                        </h1>
                        <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                            <MessageSquarePlusIcon size={24} color="#db5743" />
                        </div>
                    </div>
                </div>

                <div className="grid gap-6 mt-6">
                    {feedbacks.length === 0 ? (
                        <div className="text-center p-12 border rounded-lg">
                            <p className="text-muted-foreground">Nenhum feedback encontrado.</p>
                        </div>
                    ) : (
                        feedbacks.map((feedback) => (
                            <Card key={feedback.id}>
                                <CardHeader>
                                    <div className="flex items-center justify-between">
                                        <div className="flex items-center gap-2">
                                            {getTypeIcon(feedback.type)}
                                            <CardTitle>{feedback.title}</CardTitle>
                                        </div>
                                        <Badge className={`${getStatusColor(feedback.status)} pointer-events-none`}>
                                            {feedback.status === "OPEN" && "Aberto"}
                                            {feedback.status === "IN_PROGRESS" && "Em andamento"}
                                            {feedback.status === "CLOSED" && "Fechado"}
                                        </Badge>
                                    </div>
                                    <CardDescription>
                                        Por {(feedback.userName)?.split(' ')[0] || feedback.userEmail || "Usuário anônimo"} •{" "}
                                        {new Date(feedback.createdAt).toLocaleDateString("pt-BR", {
                                            day: "2-digit",
                                            month: "2-digit",
                                            year: "numeric",
                                            hour: "2-digit",
                                            minute: "2-digit",
                                        })}
                                    </CardDescription>
                                </CardHeader>
                                <CardContent>
                                    <p className="whitespace-pre-wrap">{feedback.description}</p>
                                </CardContent>
                                <CardFooter className="flex justify-between">
                                    <div className="text-sm text-muted-foreground">
                                        {feedback.type === "BUG" && "Bug reportado"}
                                        {feedback.type === "QUESTION" && "Dúvida"}
                                        {feedback.type === "SUGGESTION" && "Sugestão"}
                                    </div>
                                    <div className="flex items-center gap-2">
                                        <Select
                                            defaultValue={feedback.status}
                                            onValueChange={(value) => updateFeedbackStatus(feedback.id, value)}
                                        >
                                            <SelectTrigger className="w-36">
                                                <SelectValue placeholder="Status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="OPEN">Aberto</SelectItem>
                                                <SelectItem value="IN_PROGRESS">Em andamento</SelectItem>
                                                <SelectItem value="CLOSED">Fechado</SelectItem>
                                            </SelectContent>
                                        </Select>
                                    </div>
                                </CardFooter>
                            </Card>
                        ))
                    )}
                </div>
            </div>
            <Footer />
        </div>
    );
}