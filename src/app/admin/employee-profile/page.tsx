"use client"

import { <PERSON><PERSON> } from "@/app/components/footer";
import { Header } from "@/app/components/header";
import Loading from "@/app/components/ui/loading";
import { useRouter } from "next/navigation";
import { useSession } from "next-auth/react";
import { useEffect, useState } from "react";
import { User, Role, AccessLevel, WorkArrangement } from "@prisma/client";
import { Edit, MoveLeft, UserCog, Save } from "lucide-react";
import { Button } from "@/app/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/app/components/ui/dialog";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { toast } from "sonner";
import { formatSalary } from "@/lib/formatters";
import { Ava<PERSON>, AvatarFallback, AvatarImage } from "@/app/components/ui/avatar";
import { Separator } from "@/app/components/ui/separator";

export default function EmployeeProfilePage() {
    const { data: session, status } = useSession();
    const [isAdmin, setIsAdmin] = useState(false);
    const [isFetchingRole, setIsFetchingRole] = useState(true);
    const [employees, setEmployees] = useState<User[]>([]);
    const [editingEmployee, setEditingEmployee] = useState<User | null>(null);
    const [isDialogOpen, setIsDialogOpen] = useState(false);
    const router = useRouter();

    useEffect(() => {
        const fetchUserRole = async () => {
            try {
                if (session?.user?.email) {
                    const response = await fetch(`/api/users/${session.user.email}`);
                    if (response.ok) {
                        const user = await response.json();

                        const hasPermission =
                            user?.role === "ADMIN" ||
                            user?.role === "DEVELOPER" ||
                            user?.role === "GENERAL_ASSISTANT";

                        setIsAdmin(hasPermission);
                    } else {
                        console.error("Falha ao buscar dados do usuário:", response.status);
                        setIsAdmin(false);
                    }
                } else {
                    setIsAdmin(false);
                }
            } catch (error) {
                console.error("Erro ao buscar função do usuário:", error);
                setIsAdmin(false);
            } finally {
                setIsFetchingRole(false);
            }
        };

        if (status === "authenticated") {
            fetchUserRole();
        } else if (status === "unauthenticated") {
            router.push("/auth/signin");
        }
    }, [status, session, router]);

    useEffect(() => {
        if (isAdmin) {
            fetch("/api/users")
                .then(async (res) => {
                    if (!res.ok) {
                        throw new Error(`Erro ao buscar usuários: ${res.statusText}`);
                    }
                    return res.json();
                })
                .then((data) => {
                    let users = data.filter((user: User) => !user.archived) && data.filter((user: User) => user.role !== "ADMIN");
                    users = users.sort((a: User, b: User) => {
                        if (!a.name) return 1;
                        if (!b.name) return -1;
                        return a.name.localeCompare(b.name, 'pt-BR', { sensitivity: 'base' });
                    });
                    setEmployees(users || []);
                })
                .catch((error) => console.error("Failed to fetch users:", error));
        }
    }, [isAdmin]);
    if (status === "loading") {
        return <div className="min-h-[70vh] flex justify-center items-center">
            <Loading />
        </div>;
    }

    const roles = [
        { value: 'ADMIN', label: 'Administrador' },
        { value: 'VIEWER', label: 'Usuário' },
        { value: 'DEVELOPER', label: 'Desenvolvedor' },
        { value: 'COPY', label: 'Copy' },
        { value: 'DESIGNER', label: 'Designer' },
        { value: 'DESIGNER_SENIOR', label: 'Designer Sênior' },
        { value: 'DESIGNER_JUNIOR', label: 'Designer Júnior' },
        { value: 'GENERAL_ASSISTANT', label: 'Assistente Geral' }
    ];

    const accessLevels = [
        { value: 'VIEWER', label: 'Visualizador' },
        { value: 'EDITOR', label: 'Editor' }
    ];

    const workArrangements = [
        { value: 'REMOTE', label: 'Remoto' },
        { value: 'HYBRID', label: 'Híbrido' },
        { value: 'ON_SITE', label: 'Presencial' }
    ];

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div>
                {isFetchingRole ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <div className="p-4 xs:p-8 flex-grow mb-4">
                        <div className="flex justify-between items-center mb-8">
                            <div className="flex items-center gap-2">
                                <Button variant="outline" size="icon" onClick={() => router.push("/panel")}>
                                    <MoveLeft />
                                </Button>
                            </div>

                            <div className="flex items-center gap-3 group">
                                <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                                    Perfis de funcionários
                                </h1>
                                <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                    <UserCog size={24} color="#db5743" />
                                </div>
                            </div>
                        </div>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                            {employees.map((employee) => (
                                <div key={employee.id} className="p-4 rounded-lg border border-zinc-200 dark:border-zinc-800 hover:shadow-md transition-shadow">
                                    <div className="flex justify-between items-start gap-4">
                                        <div className="flex items-start gap-4 mb-2">
                                            <Avatar className="w-16 h-16 xs:w-24 xs:h-24 border-2 border-zinc-200 dark:border-zinc-700 rounded-lg">
                                                <AvatarImage src={employee.image ?? "https://github.com/shadcn.png"} alt={(employee.fullName || employee.name) ?? "Usuário"} />
                                                <AvatarFallback>{(employee.fullName || employee.name) ? String(employee.fullName || employee.name)[0] : "U"}</AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <h3 className="font-semibold text-lg mb-2">{employee.fullName || employee.name}</h3>
                                                <p className="text-sm text-gray-600 dark:text-gray-400">
                                                    {roles.find(r => r.value === employee.role)?.label || employee.role}
                                                </p>
                                            </div>
                                        </div>
                                        <Dialog open={isDialogOpen && editingEmployee?.id === employee.id} onOpenChange={(open) => {
                                            if (!open) {
                                                setIsDialogOpen(false);
                                                setEditingEmployee(null);
                                            }
                                        }}>
                                            <DialogTrigger asChild>
                                                <Button
                                                    variant="outline"
                                                    title="Editar perfil"
                                                    onClick={() => {
                                                        setEditingEmployee(employee);
                                                        setIsDialogOpen(true);
                                                    }}
                                                    className="w-9 h-9 min-w-9 min-h-9"
                                                >
                                                    <Edit />
                                                </Button>
                                            </DialogTrigger>
                                            <DialogContent className="h-[90vh] overflow-y-auto">
                                                <DialogHeader>
                                                    <DialogTitle>
                                                        Editar perfil de {employee.name}
                                                    </DialogTitle>
                                                </DialogHeader>
                                                <EmployeeEditForm
                                                    employee={employee}
                                                    onSave={(updatedEmployee) => {
                                                        setEmployees(prev =>
                                                            prev.map(emp =>
                                                                emp.id === updatedEmployee.id ? updatedEmployee : emp
                                                            )
                                                        );
                                                        setIsDialogOpen(false);
                                                        setEditingEmployee(null);
                                                    }}
                                                />
                                            </DialogContent>
                                        </Dialog>
                                    </div>

                                    <Separator className="my-2" />

                                    <div className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                                        <p className="flex items-center gap-1">
                                            <span className="font-extrabold">Data de nascimento:</span>
                                            {employee.birthDate ? new Date(employee.birthDate).toLocaleDateString('pt-BR', { timeZone: 'UTC' }) : 'Não informado'}
                                        </p>
                                        <p className="flex items-start gap-1">
                                            <span className="font-extrabold">Endereço:</span>
                                            {employee.address || 'Não informado'}
                                        </p>
                                        <p className="flex items-center gap-1">
                                            <span className="font-extrabold">CPF:</span>
                                            {employee.CPF || 'Não informado'}
                                        </p>
                                        <p className="flex items-center gap-1">
                                            <span className="font-extrabold">RG:</span>
                                            {employee.RG || 'Não informado'}
                                        </p>
                                        <p className="flex items-center gap-1">
                                            <span className="font-extrabold">Carteira de trabalho:</span>
                                            {employee.workCard || 'Não informado'}
                                        </p>
                                        <p className="flex items-center gap-1">
                                            <span className="font-extrabold">Filhos:</span>
                                            {employee.children || 'Não informado'}
                                        </p>

                                        <Separator className="my-2" />

                                        <p className="flex items-center gap-1">
                                            <span className="font-extrabold">Data de contratação:</span>
                                            {employee.hireDate ? new Date(employee.hireDate).toLocaleDateString('pt-BR', { timeZone: 'UTC' }) : 'Não informado'}
                                        </p>
                                        <p className="flex items-center gap-1">
                                            <span className="font-extrabold">Modalidade de trabalho:</span>
                                            {workArrangements.find(r => r.value === employee.workArrangement)?.label || employee.workArrangement}
                                        </p>
                                        <p className="flex items-center gap-1">
                                            <span className="font-extrabold">Salário:</span>
                                            {employee.salary ? formatSalary(employee.salary) : 'Não informado'}
                                        </p>

                                        <Separator className="my-2" />

                                        <p className="flex items-center gap-1">
                                            <span className="font-extrabold">Nível de acesso no sistema:</span>
                                            {accessLevels.find(r => r.value === employee.accessLevel)?.label || employee.accessLevel}
                                        </p>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                ) : (
                    <div className="p-4 xs:p-8 flex-grow mb-4">
                        <h1 className="text-2xl font-bold mb-4">Acesso negado</h1>
                        <p>Você não tem permissão para acessar esta página.</p>
                    </div>
                )}
            </div>
            <Footer />
        </div>
    );
}

interface EmployeeEditFormProps {
    employee: User;
    onSave: (employee: User) => void;
}

function EmployeeEditForm({ employee, onSave }: EmployeeEditFormProps) {
    const [formData, setFormData] = useState({
        fullName: employee.fullName || '',
        address: employee.address || '',
        birthDate: employee.birthDate ? new Date(employee.birthDate).toISOString().split('T')[0] : '',
        hireDate: employee.hireDate ? new Date(employee.hireDate).toISOString().split('T')[0] : '',
        CPF: employee.CPF || '',
        RG: employee.RG || '',
        workCard: employee.workCard || '',
        children: employee.children || '',
        role: employee.role,
        accessLevel: employee.accessLevel,
        workArrangement: employee.workArrangement,
        salary: employee.salary || ''
    });
    const [isLoading, setIsLoading] = useState(false);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        setIsLoading(true);

        try {
            const response = await fetch('/api/users', {
                method: 'PATCH',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    id: employee.id,
                    ...formData,
                    birthDate: formData.birthDate ? new Date(formData.birthDate) : null,
                    hireDate: formData.hireDate ? new Date(formData.hireDate) : null
                })
            });

            if (response.ok) {
                const updatedEmployee = await response.json();
                onSave(updatedEmployee);
                toast.success('Colaborador atualizado com sucesso!');
            } else {
                throw new Error('Erro ao atualizar colaborador');
            }
        } catch (error) {
            toast.error('Erro ao atualizar colaborador');
            console.error('Erro ao atualizar colaborador:', error);
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <form onSubmit={handleSubmit} className="space-y-4">
            <div className="space-y-2">
                <Label htmlFor="fullName">Nome completo</Label>
                <Input
                    id="fullName"
                    value={formData.fullName}
                    onChange={(e) => setFormData(prev => ({ ...prev, fullName: e.target.value }))}
                    required
                />
            </div>

            <div className="space-y-2">
                <Label htmlFor="address">Endereço</Label>
                <Input
                    id="address"
                    value={formData.address}
                    onChange={(e) => setFormData(prev => ({ ...prev, address: e.target.value }))}
                />
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="birthDate">Data de nascimento</Label>
                    <Input
                        id="birthDate"
                        type="date"
                        value={formData.birthDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, birthDate: e.target.value }))}
                    />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="hireDate">Data de contratação</Label>
                    <Input
                        id="hireDate"
                        type="date"
                        value={formData.hireDate}
                        onChange={(e) => setFormData(prev => ({ ...prev, hireDate: e.target.value }))}
                    />
                </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="CPF">CPF</Label>
                    <Input
                        id="CPF"
                        value={formData.CPF}
                        onChange={(e) => setFormData(prev => ({ ...prev, CPF: e.target.value }))}
                    />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="RG">RG</Label>
                    <Input
                        id="RG"
                        value={formData.RG}
                        onChange={(e) => setFormData(prev => ({ ...prev, RG: e.target.value }))}
                    />
                </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="workCard">Carteira de trabalho</Label>
                    <Input
                        id="workCard"
                        value={formData.workCard}
                        onChange={(e) => setFormData(prev => ({ ...prev, workCard: e.target.value }))}
                    />
                </div>

                <div className="space-y-2">
                    <Label htmlFor="children">Filhos</Label>
                    <Input
                        id="children"
                        value={formData.children}
                        onChange={(e) => setFormData(prev => ({ ...prev, children: e.target.value }))}
                    />
                </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="role">Cargo</Label>
                    <Select value={formData.role} onValueChange={(value) => setFormData(prev => ({ ...prev, role: value as Role }))}>
                        <SelectTrigger>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ADMIN">Admin</SelectItem>
                            <SelectItem value="DEVELOPER">Desenvolvedor</SelectItem>
                            <SelectItem value="COPY">Copywriter</SelectItem>
                            <SelectItem value="DESIGNER">Designer</SelectItem>
                            <SelectItem value="DESIGNER_SENIOR">Designer Sênior</SelectItem>
                            <SelectItem value="DESIGNER_JUNIOR">Designer Júnior</SelectItem>
                            <SelectItem value="GENERAL_ASSISTANT">Assistente Geral</SelectItem>
                            <SelectItem value="VIEWER">Visualizador</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="accessLevel">Nível de acesso no sistema</Label>
                    <Select value={formData.accessLevel} onValueChange={(value) => setFormData(prev => ({ ...prev, accessLevel: value as AccessLevel }))}>
                        <SelectTrigger>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="VIEWER">Visualizador</SelectItem>
                            <SelectItem value="EDITOR">Editor</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                    <Label htmlFor="workArrangement">Modalidade de trabalho</Label>
                    <Select value={formData.workArrangement} onValueChange={(value) => setFormData(prev => ({ ...prev, workArrangement: value as WorkArrangement }))}>
                        <SelectTrigger>
                            <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="ON_SITE">Presencial</SelectItem>
                            <SelectItem value="REMOTE">Remoto</SelectItem>
                            <SelectItem value="HYBRID">Híbrido</SelectItem>
                        </SelectContent>
                    </Select>
                </div>

                <div className="space-y-2">
                    <Label htmlFor="salary">Salário</Label>
                    <Input
                        id="salary"
                        value={formData.salary}
                        onChange={(e) => setFormData(prev => ({ ...prev, salary: e.target.value }))}
                    />
                </div>
            </div>

            <Button type="submit" disabled={isLoading} className="w-full">
                <Save className="w-4 h-4" />
                {isLoading ? 'Salvando' : 'Salvar alterações'}
            </Button>
        </form>
    );
}