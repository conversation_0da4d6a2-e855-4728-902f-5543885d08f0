"use client"

import { User } from "@prisma/client";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, LockKeyhole, MoveLeft, Trash, User<PERSON>en, UserPlus } from "lucide-react";
import { useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Header } from "../../components/header";
import { Card, CardContent } from "../../components/ui/card";
import { Avatar, AvatarFallback, AvatarImage } from "../../components/ui/avatar";
import { Badge } from "../../components/ui/badge";
import { Button } from "../../components/ui/button";
import { Footer } from "../../components/footer";
import Loading from "../../components/ui/loading";
import { NotAllowed } from "../../components/not-allowed";
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "../../components/ui/alert-dialog";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter, DialogDescription } from "../../components/ui/dialog";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "../../components/ui/select";
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "../../components/ui/dropdown-menu";

export default function UsersPage() {
    const { data: session, status } = useSession();
    const [isAdmin, setIsAdmin] = useState(false);
    const [users, setUsers] = useState<Array<User & { client?: { id: string; name?: string } }>>([]);
    const [isLoading, setIsLoading] = useState(true);
    const [assignModalOpenFor, setAssignModalOpenFor] = useState<string | null>(null);
    const [clientsForAssign, setClientsForAssign] = useState<Array<{ id: string; name: string }>>([]);
    const [selectedClientForAssign, setSelectedClientForAssign] = useState<string | null>(null);
    const router = useRouter();

    const activeUsers = users.filter(user => !user.archived);

    useEffect(() => {
        if (status === "unauthenticated") {
            router.push("/");
        }
    }, [status, router]);

    useEffect(() => {
        if (status === "authenticated") {
            setIsLoading(true);
            fetch("/api/users")
                .then(async (res) => {
                    if (!res.ok) {
                        throw new Error(`Erro ao buscar usuários: ${res.statusText}`);
                    }
                    return res.json();
                })
                .then((data) => setUsers(data || []))
                .catch((error) => console.error("Failed to fetch users:", error))
                .finally(() => setIsLoading(false));

            const fetchUserRole = async () => {
                try {
                    if (session?.user?.email) {
                        const email = session.user.email;
                        const response = await fetch(`/api/users/${email}`);
                        if (response.ok) {
                            const user = await response.json();
                            setIsAdmin(user?.role === "ADMIN" || user?.role === "DEVELOPER");
                        } else {
                            console.error(`Failed to fetch user role. Status: ${response.status}`);
                            const errorResponse = await response.json();
                            console.error("Error response:", errorResponse);
                        }
                    } else {
                        console.error("No email found in session.");
                    }
                } catch (error) {
                    console.error("Error fetching user role:", error);
                }
            };

            fetchUserRole();
        }
    }, [status, session]);

    const changeUserRole = async (userId: string, newRole: "ADMIN" | "VIEWER" | "DEVELOPER" | "COPY" | "DESIGNER" | "DESIGNER_SENIOR" | "DESIGNER_JUNIOR" | "GENERAL_ASSISTANT" | "CLIENT") => {
        try {
            const payload: { id: string; role: string; accessLevel?: "VIEWER" | "EDITOR" } = {
                id: userId,
                role: newRole
            };

            if (newRole === "ADMIN" || newRole === "DEVELOPER" || newRole === "DESIGNER_SENIOR" || newRole === "GENERAL_ASSISTANT") {
                payload.accessLevel = "EDITOR";
            } else if (newRole === "DESIGNER_JUNIOR") {
                payload.accessLevel = "VIEWER";
            }

            const response = await fetch(`/api/users`, {
                method: "PATCH",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify(payload),
            });

            if (!response.ok) {
                throw new Error("Erro ao atualizar a função do usuário");
            }

            setUsers((prevUsers) =>
                prevUsers.map((user) =>
                    user.id === userId
                        ? {
                            ...user,
                            role: newRole,
                            ...((newRole === "ADMIN" || newRole === "DEVELOPER" || newRole === "DESIGNER_SENIOR" || newRole === "GENERAL_ASSISTANT")
                                ? { accessLevel: "EDITOR" }
                                : newRole === "DESIGNER_JUNIOR"
                                    ? { accessLevel: "VIEWER" }
                                    : {})
                        }
                        : user
                )
            );
            toast.success("Função do usuário atualizada com sucesso!");
        } catch (error) {
            console.error("Erro ao mudar a permissão do usuário:", error);
            toast.error("Erro ao mudar a permissão do usuário.");
        }
    };

    const changeUserAccessLevel = async (userId: string, newAccessLevel: "VIEWER" | "EDITOR") => {
        try {
            const response = await fetch(`/api/users`, {
                method: "PATCH",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ id: userId, accessLevel: newAccessLevel }),
            });

            if (!response.ok) {
                throw new Error("Erro ao atualizar o nível de acesso do usuário");
            }

            setUsers((prevUsers) =>
                prevUsers.map((user) =>
                    user.id === userId ? { ...user, accessLevel: newAccessLevel } : user
                )
            );
            toast.success("Nível de acesso atualizado com sucesso!");
        } catch (error) {
            console.error("Erro ao mudar o nível de acesso do usuário:", error);
            toast.error("Erro ao mudar o nível de acesso do usuário.");
        }
    };

    const changeWorkArrangement = async (userId: string, newWorkArrangement: "ON_SITE" | "REMOTE" | "HYBRID") => {
        try {
            const response = await fetch(`/api/users`, {
                method: "PATCH",
                headers: { "Content-Type": "application/json" },
                body: JSON.stringify({ id: userId, workArrangement: newWorkArrangement }),
            });

            if (!response.ok) {
                throw new Error("Erro ao atualizar o modelo de trabalho do usuário");
            }

            setUsers((prevUsers) =>
                prevUsers.map((user) =>
                    user.id === userId ? { ...user, workArrangement: newWorkArrangement } : user
                )
            );
            toast.success("Modelo de trabalho atualizado com sucesso!");
        } catch (error) {
            console.error("Erro ao mudar o modelo de trabalho do usuário:", error);
            toast.error("Erro ao mudar o modelo de trabalho do usuário.");
        }
    };

    const handleDeleteUser = async (email: string) => {
        setIsLoading(true);
        try {
            const response = await fetch(`/api/users/${email}`, {
                method: "DELETE",
            });

            if (!response.ok) {
                const errorData = await response.json().catch(() => null);
                const errorMessage = errorData?.error || response.statusText;
                throw new Error(`Erro ao deletar o usuário: ${errorMessage} (${response.status})`);
            }

            setUsers((prevUsers) => prevUsers.filter(user => user.email !== email));
            toast.success("Usuário excluído com sucesso!");
        } catch (error) {
            console.error("Erro ao deletar o usuário:", error);
            toast.error(`Erro ao deletar o usuário: ${error instanceof Error ? error.message : 'Erro desconhecido'}`);
        } finally {
            setIsLoading(false);
        }
    };

    const assignClientToUser = async (userId: string) => {
        setAssignModalOpenFor(userId);
        setSelectedClientForAssign(null);
        try {
            const res = await fetch('/api/clients');
            if (!res.ok) throw new Error('Erro ao buscar clientes');
            const data: { id: string; name: string }[] = await res.json();
            const simplified = (data || []).map((c) => ({ id: c.id, name: c.name }));
            setClientsForAssign(simplified);
        } catch (error) {
            console.error('Erro ao buscar clientes para atribuição:', error);
            toast.error('Erro ao buscar clientes.');
            setAssignModalOpenFor(null);
        }
    };

    const confirmAssignClient = async () => {
        if (!assignModalOpenFor || !selectedClientForAssign) {
            toast.error('Selecione um cliente antes de confirmar.');
            return;
        }

        setIsLoading(true);
        try {
            const response = await fetch('/api/users/assign-client', {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ userId: assignModalOpenFor, clientId: selectedClientForAssign }),
            });

            if (!response.ok) {
                const err = await response.json().catch(() => null);
                throw new Error(err?.message || 'Erro ao atribuir cliente');
            }

            setUsers((prevUsers) =>
                prevUsers.map((u) => (u.id === assignModalOpenFor ? { ...u, role: 'CLIENT' } as User : u))
            );
            toast.success('Cliente atribuído com sucesso');
            setAssignModalOpenFor(null);
        } catch (error) {
            console.error('Erro ao confirmar atribuição de cliente:', error);
            toast.error('Erro ao atribuir cliente.');
        } finally {
            setIsLoading(false);
        }
    };

    const fomrmatedUsers = users.map((user) => {
        return {
            ...user,
            role: {
                'ADMIN': 'Administrador',
                'VIEWER': 'Usuário',
                'DEVELOPER': 'Desenvolvedor',
                'COPY': 'Copy',
                'DESIGNER': 'Designer',
                'DESIGNER_SENIOR': 'Designer Sênior',
                'DESIGNER_JUNIOR': 'Designer Junior',
                'GENERAL_ASSISTANT': 'Assistente Geral',
                'CLIENT': 'Cliente'
            }[user.role] || user.role,
            accessLevel: {
                'VIEWER': 'Visualizador',
                'EDITOR': 'Editor'
            }[user.accessLevel] || user.accessLevel,
            workArrangement: {
                'ON_SITE': 'Presencial',
                'REMOTE': 'Remoto',
                'HYBRID': 'Híbrido'
            }[user.workArrangement] || user.workArrangement
        };
    });

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <div className="p-4 xs:px-8 flex-grow">
                {isLoading ? (
                    <div className="min-h-[70vh] flex justify-center items-center">
                        <Loading />
                    </div>
                ) : isAdmin ? (
                    <div>
                        <Button
                            variant="outline"
                            onClick={() => router.push('/panel')}
                        >
                            <MoveLeft className="h-4 w-4" />
                            Voltar
                        </Button>
                        <h2 className="mt-6 text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800">Usuários</h2>
                        <div className="my-4 flex items-center gap-2">
                            <span className="text-sm">Ativos</span>
                            <Badge>{activeUsers.length}</Badge>
                        </div>
                        <Card>
                            <CardContent className="p-2 sm:p-4 space-y-2">
                                {activeUsers.length > 0 && (
                                    activeUsers.map((user) => (
                                        <div key={user.id} className="p-2 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 border-b border-zinc-200 dark:border-zinc-800">
                                            <div className="flex items-start gap-2 w-full sm:w-auto">
                                                <Avatar className="border-2 border-zinc-200 w-10 h-10 xs:w-16 xs:h-16">
                                                    <AvatarImage src={user.image ?? "https://github.com/shadcn.png"} alt={user.name ?? "Usuário"} />
                                                    <AvatarFallback>{user.name ? user.name[0] : "U"}</AvatarFallback>
                                                </Avatar>
                                                <div>
                                                    {user.role === "ADMIN" ? (
                                                        <p className="font-semibold">
                                                            {user.name}
                                                        </p>
                                                    ) : (
                                                        <p className="font-semibold">
                                                            {(user?.name)?.split(' ')[0]}
                                                        </p>
                                                    )}
                                                    <p className="text-[0.65rem] sm:text-sm text-zinc-500">{user.email}</p>
                                                    <div className="space-x-1">
                                                        <Badge
                                                            variant="outline"
                                                            className={`font-mono font-normal ${user.role === "CLIENT" && "border-primary2"}`}
                                                        >
                                                            {fomrmatedUsers.find(u => u?.id === user.id)?.role}
                                                        </Badge>
                                                        {user.role !== "CLIENT" && (
                                                            <Badge
                                                                variant="outline"
                                                                className="font-mono font-normal"
                                                            >
                                                                {fomrmatedUsers.find(u => u?.id === user.id)?.accessLevel}
                                                            </Badge>
                                                        )}
                                                        {user.role !== "CLIENT" && (
                                                            <Badge
                                                                variant="outline"
                                                                className="font-mono font-normal"
                                                            >
                                                                {fomrmatedUsers.find(u => u?.id === user.id)?.workArrangement}
                                                            </Badge>
                                                        )}
                                                        {user.role === "CLIENT" && (
                                                            <Badge
                                                                variant="primary"
                                                                className="font-mono font-normal"
                                                            >
                                                                {user.client?.name || "-"}
                                                            </Badge>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                            <div className="flex gap-2 items-center self-end sm:self-center mt-2 sm:mt-0">
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            title="Alterar papel"
                                                        >
                                                            <UserPen />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent className="w-48 mx-4">
                                                        <p className="text-xs px-2 py-1 text-muted-foreground">
                                                            Altere o papel do usuário
                                                        </p>
                                                        <DropdownMenuItem
                                                            className={user.role === "ADMIN" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserRole(user.id, "ADMIN")}
                                                        >
                                                            Administrador
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className={user.role === "VIEWER" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserRole(user.id, "VIEWER")}
                                                        >
                                                            Usuário
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className={user.role === "DEVELOPER" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserRole(user.id, "DEVELOPER")}
                                                        >
                                                            Desenvolvedor
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className={user.role === "COPY" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserRole(user.id, "COPY")}
                                                        >
                                                            Copy
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className={user.role === "DESIGNER" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserRole(user.id, "DESIGNER")}
                                                        >
                                                            Designer
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className={user.role === "DESIGNER_SENIOR" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserRole(user.id, "DESIGNER_SENIOR")}
                                                        >
                                                            Designer Sênior
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className={user.role === "DESIGNER_JUNIOR" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserRole(user.id, "DESIGNER_JUNIOR")}
                                                        >
                                                            Designer Júnior
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className={user.role === "GENERAL_ASSISTANT" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserRole(user.id, "GENERAL_ASSISTANT")}
                                                        >
                                                            Assistente Geral
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className={user.role === "CLIENT" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserRole(user.id, "CLIENT")}
                                                        >
                                                            Cliente
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>

                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            title="Alterar nível de acesso"
                                                        >
                                                            <LockKeyhole />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent className="w-36 mx-4">
                                                        <p className="text-xs px-2 py-1 text-muted-foreground">
                                                            Nível de acesso
                                                        </p>
                                                        <DropdownMenuItem
                                                            className={user.accessLevel === "VIEWER" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserAccessLevel(user.id, "VIEWER")}
                                                            disabled={["ADMIN", "DEVELOPER", "DESIGNER_SENIOR", "DESIGNER_JUNIOR", "GENERAL_ASSISTANT"].includes(user.role)}
                                                        >
                                                            Visualizador
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem
                                                            className={user.accessLevel === "EDITOR" ? "font-medium bg-accent" : ""}
                                                            onClick={() => changeUserAccessLevel(user.id, "EDITOR")}
                                                            disabled={["ADMIN", "DEVELOPER", "DESIGNER_SENIOR", "DESIGNER_JUNIOR", "GENERAL_ASSISTANT"].includes(user.role)}
                                                        >
                                                            Editor
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                                
                                                {user.role !== "CLIENT" && (
                                                    <DropdownMenu>
                                                        <DropdownMenuTrigger asChild>
                                                            <Button
                                                                variant="outline"
                                                                size="icon"
                                                                title="Alterar modelo de trabalho"
                                                            >
                                                                <Briefcase />
                                                            </Button>
                                                        </DropdownMenuTrigger>
                                                        <DropdownMenuContent className="w-36 mx-4">
                                                            <p className="text-xs px-2 py-1 text-muted-foreground">
                                                                Modelo de trabalho
                                                            </p>
                                                            <DropdownMenuItem
                                                                className={user.workArrangement === "ON_SITE" ? "font-medium bg-accent" : ""}
                                                                onClick={() => changeWorkArrangement(user.id, "ON_SITE")}
                                                            >
                                                                Presencial
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                className={user.workArrangement === "REMOTE" ? "font-medium bg-accent" : ""}
                                                                onClick={() => changeWorkArrangement(user.id, "REMOTE")}
                                                            >
                                                                Remoto
                                                            </DropdownMenuItem>
                                                            <DropdownMenuItem
                                                                className={user.workArrangement === "HYBRID" ? "font-medium bg-accent" : ""}
                                                                onClick={() => changeWorkArrangement(user.id, "HYBRID")}
                                                            >
                                                                Híbrido
                                                            </DropdownMenuItem>
                                                        </DropdownMenuContent>
                                                    </DropdownMenu>
                                                )}

                                                {user.role === "CLIENT" && (
                                                    <>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            title="Atribuir um cliente"
                                                            onClick={() => assignClientToUser(user.id)}
                                                        >
                                                            <UserPlus />
                                                        </Button>

                                                        <Dialog open={!!assignModalOpenFor} onOpenChange={(open) => { if (!open) setAssignModalOpenFor(null); }}>
                                                            <DialogContent>
                                                                <DialogHeader>
                                                                    <DialogTitle>Atribuir cliente</DialogTitle>
                                                                    <DialogDescription>
                                                                        Atribua um cliente existente a esse usuário. O usuário irá ter acesso a todos os materiais e informações desse cliente.
                                                                    </DialogDescription>
                                                                </DialogHeader>

                                                                <div className="mt-4">
                                                                    <Select onValueChange={(val) => setSelectedClientForAssign(val)}>
                                                                        <SelectTrigger>
                                                                            <SelectValue placeholder="Selecione um cliente" />
                                                                        </SelectTrigger>
                                                                        <SelectContent>
                                                                            {clientsForAssign.map((c) => (
                                                                                <SelectItem key={c.id} value={c.id}>{c.name}</SelectItem>
                                                                            ))}
                                                                        </SelectContent>
                                                                    </Select>
                                                                </div>

                                                                <DialogFooter className="mt-4">
                                                                    <Button variant="outline" onClick={() => setAssignModalOpenFor(null)}>Cancelar</Button>
                                                                    <Button onClick={confirmAssignClient} disabled={isLoading || !selectedClientForAssign}>Confirmar</Button>
                                                                </DialogFooter>
                                                            </DialogContent>
                                                        </Dialog>
                                                    </>
                                                )}

                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button variant="outline" size="icon" title="Excluir usuário">
                                                            <Trash size={16} color="red" />
                                                        </Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>
                                                                Deletar usuário
                                                            </AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Tem certeza que deseja deletar o usuário? Essa ação é irreversível.
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                                            <AlertDialogAction
                                                                onClick={() => {
                                                                    if (user.email) {
                                                                        handleDeleteUser(user.email);
                                                                    } else {
                                                                        toast.error("Não foi possível deletar o usuário: email não encontrado");
                                                                    }
                                                                }}
                                                                disabled={isLoading}
                                                            >
                                                                {isLoading ? <Ellipsis /> : "Confirmar"}
                                                            </AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            </div>
                                        </div>
                                    ))
                                )}
                            </CardContent>
                        </Card>
                    </div>
                ) : (
                    <NotAllowed page="/" />
                )}
            </div>
            <Footer />
        </div>
    );
};
