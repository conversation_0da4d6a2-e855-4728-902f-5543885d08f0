"use client"

import { <PERSON><PERSON> } from "@/app/components/footer";
import { Header } from "@/app/components/header";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem, DropdownMenuLabel, DropdownMenuShortcut } from '@/app/components/ui/dropdown-menu';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select";
import { MultiSelect } from "@/app/components/ui/multi-select";
import { toast } from "sonner";
import { CirclePlus, Cog, Edit, Ellipsis, FileCog, History, MoveLeft, Plus, PlusCircle, Trash2 } from "lucide-react";
import { useEffect, useState, useCallback, ChangeEvent } from "react";
import { Card, CardContent, CardDescription, CardH<PERSON><PERSON>, CardTitle } from "@/app/components/ui/card";
import { Separator } from "@/app/components/ui/separator";
import { useRouter } from "next/navigation";
import { Badge } from "@/app/components/ui/badge";
import EditQuoteDialog from "./edit-quote-dialog";
import BudgetTemplateModal from './template-modal';
import ExportPDFQuote from '@/app/components/export-pdf-quote';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from "@/app/components/ui/alert-dialog";
import { Textarea } from "@/app/components/ui/textarea";
import { formatPhoneOnInput, formatCurrency, parseSalary, formatPhoneNumber } from "@/lib/formatters";
import EditTypeQuoteDialog from "./edit-type-quote-dialog";

export default function QuotesPage() {
    const router = useRouter();
    const [showForm, setShowForm] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [clientName, setClientName] = useState("");
    const [clientPhoneNumber, setClientPhoneNumber] = useState("");
    const [clientPhoneFocused, setClientPhoneFocused] = useState(false);
    const [socialMedia, setSocialMedia] = useState<string[]>([]);
    const [socialMediaInput, setSocialMediaInput] = useState<string>('');
    const [servicesOptions, setServicesOptions] = useState<Array<{ value: string; label: string }>>([]);
    const [servicesMeta, setServicesMeta] = useState<Array<{ value: string; label: string; defaultValue: string }>>([]);
    const [budgetItemOptions, setBudgetItemOptions] = useState<Array<{ value: string; label: string }>>([]);
    const [selectedServices, setSelectedServices] = useState<string[]>([]);
    const [selectedBudgetItems, setSelectedBudgetItems] = useState<string[]>([]);
    const [serviceDetails, setServiceDetails] = useState<Record<string, { quantity?: string; customDescription?: string; notes?: string; include?: boolean }>>({});
    const [totalPrice, setTotalPrice] = useState<string>('');
    const [discount, setDiscount] = useState<string>('');
    const [totalPriceFocused, setTotalPriceFocused] = useState(false);
    const [discountFocused, setDiscountFocused] = useState(false);
    const [selectedBudgetType, setSelectedBudgetType] = useState<string>("");
    type ServiceDisplayRef = { Service?: { code?: string; name?: string }; BudgetItem?: { code?: string; name?: string }; customLabel?: string };
    type QuoteRecord = {
        socialMedia: string[]; id: string; client: string; clientPhoneNumber?: string | null; budgetType?: string | null; services?: ServiceDisplayRef[]; totalPrice?: number | null; discount?: number | null; createdAt?: string 
};
    const [quotes, setQuotes] = useState<QuoteRecord[]>([]);
    const [loadingQuotes, setLoadingQuotes] = useState<boolean>(false);

    const [budgetTemplates, setBudgetTemplates] = useState<{ value: string; label: string }[]>([]);
    const [templateMaps, setTemplateMaps] = useState<Record<string, Record<string, string>>>({});
    const BUDGET_TYPES = budgetTemplates.map(t => ({ value: t.value, label: t.label }));
    const [showCreateTemplateModal, setShowCreateTemplateModal] = useState(false);
    const [showEditTypeQuoteDialog, setShowEditTypeQuoteDialog] = useState(false);
    const [editingTemplate, setEditingTemplate] = useState<{ id?: string; name?: string; description?: string; services?: unknown[] } | null>(null);
    const [showEditTypeQuoteEditor, setShowEditTypeQuoteEditor] = useState(false);

    useEffect(() => {
        if (!selectedBudgetType) return;

        const fetchBudgetItems = async () => {
            setSelectedBudgetItems([]);
            setSelectedServices([]);
            try {
                const res = await fetch(`/api/budget-items?templateId=${encodeURIComponent(selectedBudgetType)}`);
                if (res.ok) {
                    const data = await res.json();
                    const opts = (data || []).map((s: unknown) => {
                        const asRec = s as Record<string, unknown>;
                        const id = (asRec['id'] ?? asRec['code'] ?? asRec['name']) as string | undefined;
                        const label = (asRec['name'] ?? asRec['label'] ?? asRec['code']) as string | undefined;
                        return { value: id ?? label ?? '', label: label ?? id ?? '' };
                    });
                    setBudgetItemOptions(opts);
                    try {
                        const tmplRes = await fetch(`/api/budget-templates?id=${encodeURIComponent(selectedBudgetType)}`);
                        if (tmplRes.ok) {
                            const tmpl = await tmplRes.json();
                            const svcOpts = (tmpl.services || []).map((s: unknown) => {
                                const asRec = s as Record<string, unknown>;
                                const key = String(asRec['key'] ?? asRec['id'] ?? '');
                                const label = String(asRec['label'] ?? asRec['name'] ?? key);
                                const defaultValue = String(asRec['defaultValue'] ?? '');
                                return { value: key, label, defaultValue } as unknown;
                            });
                            const rawOpts = svcOpts.length > 0 ? svcOpts.map((o: unknown) => { const r = o as Record<string, unknown>; return { value: String(r.value ?? '').trim(), label: String(r.label ?? '').trim() }; }) : [];
                            const map = new Map<string, string>();
                            for (const o of rawOpts) if (o.value && !map.has(o.value)) map.set(o.value, o.label);
                            const final = Array.from(map.entries()).map(([value, label]) => ({ value, label }));
                            const metaMap = new Map<string, { value: string; label: string; defaultValue: string }>();
                            for (const s of svcOpts as unknown as Array<Record<string, unknown>>) {
                                const v = String(s['value'] ?? '').trim();
                                const l = String(s['label'] ?? '').trim();
                                const d = String(s['defaultValue'] ?? '').trim();
                                if (v && !metaMap.has(v)) metaMap.set(v, { value: v, label: l, defaultValue: d });
                            }
                            const finalMeta = Array.from(metaMap.values());
                            setServicesOptions(final);
                            setServicesMeta(finalMeta);
                            setServiceDetails(prev => {
                                const next = { ...prev } as Record<string, { quantity?: string; customDescription?: string; notes?: string; include?: boolean }>;
                                for (const s of svcOpts) {
                                    const rec = s as Record<string, unknown>;
                                    const key = String(rec.value ?? '');
                                    const def = String(rec.defaultValue ?? '');
                                    if (!next[key]) {
                                        next[key] = { include: true, customDescription: def };
                                    }
                                }
                                return next;
                            });
                        } else {
                            setServicesOptions([]);
                        }
                    } catch {
                        setServicesOptions([]);
                        setServicesMeta([]);
                    }
                    return;
                }
            } catch {
            }

            setServicesOptions([]);
            setServicesMeta([]);
            setBudgetItemOptions([]);
        };

        setSelectedBudgetItems([]);
        setSelectedServices([]);

        fetchBudgetItems();
    }, [selectedBudgetType]);

    useEffect(() => {
        const loadTemplates = async () => {
            try {
                const res = await fetch('/api/budget-templates');
                if (!res.ok) return;
                const data = await res.json();
                setBudgetTemplates((data || []).map((t: unknown) => { const rec = t as Record<string, unknown>; return ({ value: String(rec.id ?? ''), label: String(rec.name ?? '') }); }));
            } catch {
            }
        };
        loadTemplates();
    }, []);

    const fetchQuotes = useCallback(async () => {
        setLoadingQuotes(true);
        try {
            const res = await fetch('/api/quotes');
            if (!res.ok) throw new Error('Erro ao buscar orçamentos');
            const data = await res.json();
            const raw = Array.isArray(data) ? (data as unknown[]) : [];

            const types = Array.from(new Set(raw.map(r => (r as Record<string, unknown>).budgetType).filter(Boolean))) as string[];
            const templateMap: Record<string, Record<string, string>> = {};

            for (const t of types) {
                try {
                    const r = await fetch(`/api/budget-templates?id=${encodeURIComponent(t)}`);
                    if (!r.ok) continue;
                    const tmpl = await r.json();
                    const map: Record<string, string> = {};
                    (tmpl.services || []).forEach((s: unknown) => {
                        const rec = s as Record<string, unknown>;
                        const key = String(rec.key ?? rec.id ?? '');
                        const label = String(rec.label ?? rec.name ?? key);
                        if (key) map[key] = label;
                    });
                    templateMap[t] = map;
                } catch {
                }
            }

            const enriched = raw.map((r) => {
                const rec = r as Record<string, unknown>;
                const budgetType = String(rec.budgetType ?? '');
                const servicesArr = Array.isArray(rec.services) ? (rec.services as unknown[]) : [];
                const map = budgetType ? templateMap[budgetType] ?? {} : {};
                const services = servicesArr.map((s) => {
                    const sr = s as Record<string, unknown>;
                    const svcRec = sr.Service as Record<string, unknown> | undefined;
                    const biRec = sr.BudgetItem as Record<string, unknown> | undefined;
                    const keyField = String(sr.key ?? '');
                    const code = keyField || String(svcRec?.code ?? biRec?.code ?? sr.customLabel ?? '');
                    const labelFromTemplate = (code && (map[code] ? map[code] : (() => {
                        for (const tkey of Object.keys(templateMap)) {
                            const m = templateMap[tkey];
                            if (m && m[code]) return m[code];
                        }
                        return undefined;
                    })()));
                    const label = (labelFromTemplate as string | undefined) ?? String(svcRec?.name ?? biRec?.name ?? sr.customLabel ?? '');
                    return { ...sr, customLabel: label || code } as unknown as ServiceDisplayRef;
                });
                return { ...(rec as Record<string, unknown>), services } as unknown as QuoteRecord;
            }) as QuoteRecord[];

            setTemplateMaps(templateMap);
            setQuotes(enriched);
        } catch (err) {
            console.error('Erro fetch quotes:', err);
            setQuotes([]);
        } finally {
            setLoadingQuotes(false);
        }
    }, [setLoadingQuotes, setTemplateMaps, setQuotes]);

    useEffect(() => { fetchQuotes(); }, [fetchQuotes]);

    const handleDeleteQuote = async (quoteId: string) => {
        try {
            const response = await fetch(`/api/quotes/${quoteId}`, {
                method: "DELETE",
            });

            if (!response.ok) {
                throw new Error("Erro ao excluir orçamento");
            }

            setQuotes(quotes.filter((q) => q.id !== quoteId));
            toast.success("Orçamento excluído com sucesso");
        } catch (error) {
            console.error("Erro ao excluir orçamento:", error);
            toast.error("Erro ao excluir orçamento");
        }
    };

    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <main className="p-4 xs:px-8 flex-grow">
                <div className="flex flex-col sm:flex-row justify-between sm:items-end gap-8">
                    <div>
                        <Button
                            variant="outline"
                            onClick={() => router.push('/panel')}
                        >
                            <MoveLeft className="h-4 w-4" />
                            Voltar
                        </Button>
                        <h1 className="mt-6 text-lg font-semibold border-b border-zinc-200 dark:border-zinc-800">Orçamentos</h1>
                        <p className="mt-2 text-sm text-zinc-600 dark:text-zinc-400">
                            Aqui você pode gerenciar os orçamentos, incluindo a criação, edição, visualização de detalhes e exportação para PDF.
                        </p>
                    </div>
                    <div className="flex items-center gap-2 justify-between">
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button
                                    variant="secondary"
                                    size="icon"
                                    className="border border-zinc-200 dark:border-zinc-600"
                                >
                                    <Cog className="h-4 w-4" />
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-56 ml-4">
                                <DropdownMenuLabel>Tipos de orçamento</DropdownMenuLabel>
                                <Separator className="my-2" />
                                <DropdownMenuItem
                                    onClick={() => setShowCreateTemplateModal(true)}
                                    className="font-geistSans border-none"
                                >
                                    Novo tipo
                                    <DropdownMenuShortcut>
                                        <CirclePlus className="h-4 w-4" />
                                    </DropdownMenuShortcut>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => setShowEditTypeQuoteDialog(true)}
                                    className="font-geistSans border-none"
                                >
                                    Ver ou editar
                                    <DropdownMenuShortcut>
                                        <FileCog className="h-4 w-4" />
                                    </DropdownMenuShortcut>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>

                        <Button className='bg-primary2 hover:bg-orange-600 text-white' onClick={() => setShowForm(s => !s)}>
                            {showForm ? 'Cancelar' : 'Novo orçamento'}
                        </Button>
                    </div>
                </div>

                <BudgetTemplateModal
                    isOpen={showCreateTemplateModal}
                    onClose={() => setShowCreateTemplateModal(false)}
                    onSaved={(templates) => { if (templates) setBudgetTemplates(templates); }}
                />

                {showEditTypeQuoteDialog && (
                    <div className="fixed inset-0 z-50 flex items-center justify-center">
                        <div className="absolute inset-0 bg-black/40" onClick={() => setShowEditTypeQuoteDialog(false)} />
                        <Card className="z-50 w-full max-w-2xl mx-4">
                            <CardHeader>
                                <CardTitle>Tipos de orçamento</CardTitle>
                                <CardDescription>Veja os tipos de orçamento criados e edite-os.</CardDescription>
                            </CardHeader>
                            <CardContent className="max-h-[70vh] overflow-y-auto">
                                <div className="space-y-2">
                                    {budgetTemplates.length === 0 && (
                                        <div className="text-sm text-zinc-500">Nenhum tipo de orçamento encontrado.</div>
                                    )}
                                    {budgetTemplates.map(t => (
                                        <div key={t.value} className="flex items-center justify-between border rounded p-3">
                                            <div>
                                                <div className="font-medium">{t.label}</div>
                                                <div className="uppercase text-sm text-zinc-500">
                                                    ID: {t.value.slice(0, 6)}
                                                </div>
                                            </div>
                                            <div className="flex gap-2">
                                                <Button
                                                    size="icon"
                                                    variant="outline"
                                                    onClick={async () => {
                                                        try {
                                                            const res = await fetch(`/api/budget-templates?id=${encodeURIComponent(t.value)}`);
                                                            if (!res.ok) { toast.error('Erro ao carregar tipo de orçamento'); return; }
                                                            const tmpl = await res.json();
                                                            setEditingTemplate(tmpl);
                                                            setShowEditTypeQuoteEditor(true);
                                                        } catch (err) {
                                                            console.error(err);
                                                            toast.error('Erro ao carregar tipo de orçamento');
                                                        }
                                                    }}
                                                    title="Editar tipo de orçamento"
                                                >
                                                    <Edit className="h-4 w-4" />
                                                </Button>
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button variant="outline" size="icon"><Trash2 className="h-4 w-4 text-red-500" /></Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>
                                                                Tem certeza que deseja excluir este tipo de orçamento?
                                                            </AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Esta ação não pode ser desfeita. Isso excluirá permanentemente este tipo de orçamento.
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                                            <AlertDialogAction asChild>
                                                                <Button
                                                                    onClick={async () => {
                                                                        try {
                                                                            const res = await fetch(`/api/budget-templates?id=${encodeURIComponent(t.value)}`, {
                                                                                method: 'DELETE',
                                                                            });
                                                                            if (!res.ok) { toast.error('Erro ao excluir tipo de orçamento'); return; }
                                                                            setBudgetTemplates(budgetTemplates.filter(b => b.value !== t.value));
                                                                            toast.success('Tipo de orçamento excluído');
                                                                        } catch (err) {
                                                                            console.error(err);
                                                                            toast.error('Erro ao excluir tipo de orçamento');
                                                                        }
                                                                    }}
                                                                    title="Excluir tipo de orçamento"
                                                                >
                                                                    Excluir
                                                                </Button>
                                                            </AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            </CardContent>
                            <div className="flex justify-end gap-2 p-4">
                                <Button variant="outline" onClick={() => setShowEditTypeQuoteDialog(false)}>Fechar</Button>
                            </div>
                        </Card>
                    </div>
                )}

                <EditTypeQuoteDialog
                    isOpen={showEditTypeQuoteEditor}
                    onClose={() => { setShowEditTypeQuoteEditor(false); setEditingTemplate(null); }}
                    initial={editingTemplate ?? undefined}
                    onSaved={(templates) => { if (templates) setBudgetTemplates(templates); setShowEditTypeQuoteDialog(false); }}
                />

                <Separator className="my-6" />

                {showForm && (
                    <Card className="mb-6">
                        <CardHeader>
                            <CardTitle className="flex items-center gap-2">
                                <div className="bg-green-50 dark:bg-green-950 p-2 rounded-full">
                                    <PlusCircle size={16} className="text-primary3" />
                                </div>
                                Novo orçamento
                            </CardTitle>
                            <CardDescription>Preencha os dados abaixo e clique em adicionar.</CardDescription>
                        </CardHeader>
                        <CardContent>
                            <div className="grid grid-cols-1 gap-4">
                                <div className="grid xs:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="budgetType" className="block text-sm font-medium mb-1">Tipo de orçamento</Label>
                                        <Select onValueChange={(v) => setSelectedBudgetType(v)} value={selectedBudgetType}>
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione um tipo de orçamento" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {BUDGET_TYPES.map(opt => (
                                                    <SelectItem key={opt.value} value={opt.value}>{opt.label}</SelectItem>
                                                ))}

                                                {BUDGET_TYPES.length === 0 && (
                                                    <SelectItem value="no-types" disabled>
                                                        Nenhum tipo de orçamento encontrado
                                                    </SelectItem>
                                                )}
                                            </SelectContent>
                                        </Select>
                                    </div>
                                    <div>
                                        <Label htmlFor="clientName" className="block text-sm font-medium mb-1">Cliente</Label>
                                        <Input id="clientName" value={clientName} onChange={(e) => setClientName(e.target.value)} placeholder="Nome do cliente" />
                                    </div>
                                </div>
                                <div className="grid xs:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="clientPhoneNumber" className="block text-sm font-medium mb-1">Número</Label>
                                        <Input
                                            id="clientPhoneNumber"
                                            value={clientPhoneFocused ? clientPhoneNumber : formatPhoneOnInput(clientPhoneNumber)}
                                            onChange={(e) => setClientPhoneNumber(e.target.value.replace(/\D/g, '').slice(0, 11))}
                                            onFocus={() => setClientPhoneFocused(true)}
                                            onBlur={() => setClientPhoneFocused(false)}
                                            placeholder="Telefone do cliente"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="socialMedia" className="block text-sm font-medium mb-1">Redes sociais</Label>
                                        <div className="flex gap-2">
                                            <Input
                                                id="socialMediaInput"
                                                value={socialMediaInput}
                                                onChange={(e) => setSocialMediaInput(e.target.value)}
                                                onKeyDown={(e) => {
                                                    if (e.key === 'Enter') {
                                                        e.preventDefault();
                                                        const val = socialMediaInput.trim();
                                                        if (val && !socialMedia.includes(val)) {
                                                            setSocialMedia(prev => [...prev, val]);
                                                        }
                                                        setSocialMediaInput('');
                                                    }
                                                }}
                                                placeholder="Digite e pressione Enter para adicionar"
                                            />
                                            <Button
                                                onClick={() => {
                                                    const val = socialMediaInput.trim();
                                                    if (!val) return;
                                                    if (!socialMedia.includes(val)) setSocialMedia(prev => [...prev, val]);
                                                    setSocialMediaInput('');
                                                }}
                                            >
                                                <Plus size={16} />
                                            </Button>
                                        </div>
                                        {socialMedia.length > 0 && (
                                            <div className="mt-2 flex flex-wrap gap-2">
                                                {socialMedia.map((s, idx) => (
                                                    <div key={idx} className="flex items-center">
                                                        <Badge variant="secondary" className="rounded-full">{s}</Badge>
                                                        <Button size="icon" variant="link" onClick={() => setSocialMedia(prev => prev.filter(x => x !== s))} title={`Remover ${s}`}>
                                                            <Trash2 className="h-4 w-4 text-red-500" />
                                                        </Button>
                                                    </div>
                                                ))}
                                            </div>
                                        )}
                                    </div>
                                    <div>
                                        <Label htmlFor="services" className="block text-sm font-medium mb-1">Serviços</Label>
                                        {budgetItemOptions.length > 0 ? (
                                            <MultiSelect
                                                options={budgetItemOptions}
                                                selected={selectedBudgetItems}
                                                onChange={(vals: string[]) => { setSelectedBudgetItems(vals); setSelectedServices([]); }}
                                            />
                                        ) : (
                                            <MultiSelect
                                                options={servicesOptions}
                                                selected={selectedServices}
                                                onChange={(vals: string[]) => {
                                                    setSelectedServices(vals);
                                                    setSelectedBudgetItems([]);
                                                    const next = { ...serviceDetails };
                                                    for (const v of vals) {
                                                        if (!next[v]) {
                                                            const defaultDesc = servicesMeta?.find(mm => mm.value === v)?.defaultValue ?? '';
                                                            next[v] = { include: true, customDescription: defaultDesc };
                                                        }
                                                    }
                                                    setServiceDetails(next);
                                                }}
                                            />
                                        )}
                                    </div>
                                </div>
                                <div className="grid xs:grid-cols-2 gap-4">
                                    <div>
                                        <Label htmlFor="totalPrice" className="block text-sm font-medium mb-1">Preço do orçamento (R$)</Label>
                                        <Input
                                            id="totalPrice"
                                            value={totalPriceFocused ? totalPrice : formatCurrency(totalPrice)}
                                            onChange={(e) => setTotalPrice(e.target.value.replace(/[^0-9,.,-]/g, '').replace(/\.+/g, '.'))}
                                            onFocus={() => setTotalPriceFocused(true)}
                                            onBlur={() => {
                                                setTotalPriceFocused(false);
                                                const parsed = parseSalary(totalPrice);
                                                setTotalPrice(parsed === null ? '' : String(parsed));
                                            }}
                                            placeholder="000,00"
                                        />
                                    </div>
                                    <div>
                                        <Label htmlFor="discount" className="block text-sm font-medium mb-1">Desconto (R$)</Label>
                                        <Input
                                            id="discount"
                                            value={discountFocused ? discount : formatCurrency(discount)}
                                            onChange={(e) => setDiscount(e.target.value.replace(/[^0-9,.,-]/g, '').replace(/\.+/g, '.'))}
                                            onFocus={() => setDiscountFocused(true)}
                                            onBlur={() => {
                                                setDiscountFocused(false);
                                                const parsed = parseSalary(discount);
                                                setDiscount(parsed === null ? '' : String(parsed));
                                            }}
                                            placeholder="000,00"
                                        />
                                    </div>
                                </div>
                                {selectedBudgetType && selectedServices.length > 0 && (
                                    <div className="mt-4">
                                        <h3 className="text-sm font-medium mb-2">Detalhes dos serviços selecionados</h3>
                                        <div className="grid gap-3">
                                            {selectedServices.map((s) => (
                                                <div key={s} className="p-4 border rounded">
                                                    <Badge
                                                        variant="success"
                                                        className="py-1 rounded-full mb-1"
                                                    >
                                                        {servicesOptions.find(o => o.value === s)?.label ?? s}
                                                    </Badge>
                                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                        <div className="space-y-1">
                                                            <Label>Descrição</Label>
                                                            <Textarea
                                                                value={serviceDetails[s]?.customDescription ?? ''}
                                                                onChange={(e) => setServiceDetails(prev => ({ ...prev, [s]: { ...(prev[s] ?? {}), customDescription: e.target.value } }))}
                                                                rows={5}
                                                            />
                                                        </div>
                                                        <div className="flex flex-col h-full justify-between">
                                                            <div className="space-y-1">
                                                                <Label>Quantidade</Label>
                                                                <Input
                                                                    type="text"
                                                                    value={serviceDetails[s]?.quantity} onChange={(e: ChangeEvent<HTMLInputElement>) => setServiceDetails(prev => ({ ...prev, [s]: { ...(prev[s] ?? {}), quantity: e.target.value } }))}
                                                                    placeholder={''}
                                                                />
                                                            </div>
                                                            <div className="flex flex-col gap-2">
                                                                <Label>Horas</Label>
                                                                <Input value={serviceDetails[s]?.notes ?? ''} onChange={(e: ChangeEvent<HTMLInputElement>) => setServiceDetails(prev => ({ ...prev, [s]: { ...(prev[s] ?? {}), notes: e.target.value } }))} />
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                )}
                            </div>

                            <div className="flex flex-col xs:flex-row justify-end gap-2 mt-4">
                                <Button variant="outline" onClick={() => setShowForm(false)}>Cancelar</Button>
                                <Button
                                    onClick={async () => {
                                        if (!clientName.trim()) {
                                            toast.error('Preencha o nome do cliente');
                                            return;
                                        }
                                        setIsSubmitting(true);
                                        try {
                                            const payloadServices = selectedServices.map(s => ({
                                                key: s,
                                                quantity: serviceDetails[s]?.quantity,
                                                customDescription: serviceDetails[s]?.customDescription,
                                                notes: serviceDetails[s]?.notes,
                                                include: serviceDetails[s]?.include,
                                                unitPrice: null,
                                                itemTotal: null,
                                                budgetTemplateId: selectedBudgetType || null,
                                                templateSnapshot: { quantity: serviceDetails[s]?.quantity ?? null, customDescription: serviceDetails[s]?.customDescription ?? null, notes: serviceDetails[s]?.notes ?? null }
                                            }));

                                            const res = await fetch('/api/quotes', {
                                                method: 'POST',
                                                headers: { 'Content-Type': 'application/json' },
                                                body: JSON.stringify({
                                                    client: clientName.trim(),
                                                    clientNumber: clientPhoneNumber.trim(),
                                                    budgetType: selectedBudgetType || null,
                                                    services: payloadServices,
                                                    budgetItemIds: selectedBudgetItems,
                                                    socialMedia: socialMedia,
                                                    totalPrice: totalPrice ? Number(totalPrice.replace(',', '.')) : null,
                                                    discount: discount ? Number(discount.replace(',', '.')) : null
                                                })
                                            });

                                            if (res.ok) {
                                                toast.success('Orçamento criado com sucesso');
                                                setClientName('');
                                                setClientPhoneNumber('');
                                                    setSocialMedia([]);
                                                setSelectedServices([]);
                                                setSelectedBudgetItems([]);
                                                setSelectedBudgetType('');
                                                setTotalPrice('');
                                                setDiscount('');
                                                setShowForm(false);
                                                await fetchQuotes();
                                            } else {
                                                const err = await res.json().catch(() => ({}));
                                                toast.error(err?.error || 'Erro ao criar orçamento');
                                            }
                                        } catch (error) {
                                            console.error('Erro criar orçamento:', error);
                                            toast.error('Erro ao criar orçamento');
                                        } finally {
                                            setIsSubmitting(false);
                                        }
                                    }}
                                    disabled={isSubmitting}
                                >
                                    {isSubmitting ? <Ellipsis /> : 'Adicionar orçamento'}
                                </Button>
                            </div>
                        </CardContent>
                    </Card>
                )}
                <Card>
                    <CardHeader className="">
                        <CardTitle className="font-semibold flex items-center gap-2">
                            <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                                <History size={16} className="text-primary2" />
                            </div>
                            Orçamento anteriores
                        </CardTitle>
                        <CardDescription>
                            Visualize e gerencie os orçamentos anteriores. Clique em um dos botões para editar, excluir ou exportar para PDF.
                        </CardDescription>
                    </CardHeader>
                    <CardContent>
                        {loadingQuotes ? (
                            <p className="text-muted-foreground text-sm"><Ellipsis className="animate-pulse" /></p>
                        ) : quotes.length === 0 ? (
                            <>
                                <Separator className="mb-2" />
                                <p className="text-muted-foreground text-sm">Nenhum orçamento encontrado</p>
                            </>
                        ) : (
                            <div className="flex flex-col gap-3">
                                {quotes.map((q: QuoteRecord) => (
                                    <div key={q.id} className="p-3 border rounded">
                                        <div className="flex justify-between items-start">
                                            <div>
                                                <div className="flex items-center">
                                                    <h4 className="font-medium">{q.client}</h4>
                                                    {q.socialMedia && q.socialMedia.length > 0 && (
                                                        <div className="text-xs text-zinc-500">
                                                            {q.socialMedia.map((sm, idx) => (
                                                                <Badge
                                                                    key={idx}
                                                                    variant="secondary"
                                                                    className="mx-1 rounded-full"
                                                                >
                                                                    {sm}
                                                                </Badge>
                                                            ))}
                                                        </div>
                                                    )}
                                                </div>
                                                <div className="text-sm text-zinc-600 border-b mb-2">
                                                    {formatPhoneNumber(q.clientPhoneNumber || '-')}
                                                </div>
                                                <div className="text-xs text-zinc-500">{q.budgetType ? (BUDGET_TYPES.find(b => b.value === q.budgetType)?.label ?? q.budgetType) : '—'}</div>
                                            </div>
                                            <div className="text-right text-sm text-zinc-500">{q.createdAt ? new Date(q.createdAt).toLocaleString() : ''}</div>
                                        </div>
                                        {q.services && q.services.length > 0 && (
                                            <div className="text-sm text-zinc-700">
                                                {q.services.map((s: ServiceDisplayRef) => {
                                                    const sr = s as Record<string, unknown>;
                                                    const key = String(sr.key ?? sr.customLabel ?? '');
                                                    const templateLabel = (q.budgetType ? (templateMaps[q.budgetType ?? ''] ?? {})[key] : undefined) ?? (() => {
                                                        for (const tmKey of Object.keys(templateMaps || {})) {
                                                            const m = templateMaps![tmKey];
                                                            if (m && m[key]) return m[key];
                                                        }
                                                        return undefined;
                                                    })();
                                                    const resolved = templateLabel ?? (sr.customLabel as string | undefined) ?? (s.Service?.name ?? s.BudgetItem?.name) ?? '-';
                                                    return resolved;
                                                }).join(', ')}
                                            </div>
                                        )}
                                        <div className="flex gap-4 flex-col xs:flex-row justify-between xs:items-center">
                                            <div className="mt-2 text-sm flex items-center gap-2">
                                                Preço: {q.totalPrice !== null && q.totalPrice !== undefined ? formatCurrency(q.totalPrice) : '—'}
                                                {q.discount ? (
                                                    <Badge
                                                        className="rounded-full bg-green-100 dark:bg-green-900/20 text-green-600 dark:text-green-400"
                                                    >
                                                        - {formatCurrency(q.discount)}
                                                    </Badge>
                                                ) : null}
                                            </div>
                                            <div className="flex gap-2">
                                                <EditQuoteDialog
                                                    quote={q}
                                                    onUpdated={fetchQuotes}
                                                    servicesOptions={Array.from(new Map(servicesOptions.map(o => [String(o.value).trim(), String(o.label).trim()])).entries()).map(([value, label]) => ({ value, label }))}
                                                    servicesMeta={servicesMeta}
                                                    marketingServices={[]}
                                                    budgetTypes={BUDGET_TYPES}
                                                    templateMaps={templateMaps}
                                                />
                                                <ExportPDFQuote quote={q} />
                                                <AlertDialog>
                                                    <AlertDialogTrigger asChild>
                                                        <Button
                                                            variant="outline"
                                                            size="icon"
                                                            title="Excluir orçamento"
                                                        >
                                                            <Trash2 size={16} className="text-red-500" />
                                                        </Button>
                                                    </AlertDialogTrigger>
                                                    <AlertDialogContent>
                                                        <AlertDialogHeader>
                                                            <AlertDialogTitle>
                                                                Tem certeza que deseja excluir este orçamento?
                                                            </AlertDialogTitle>
                                                            <AlertDialogDescription>
                                                                Esta ação não pode ser desfeita.
                                                            </AlertDialogDescription>
                                                        </AlertDialogHeader>
                                                        <AlertDialogFooter>
                                                            <AlertDialogCancel>Cancelar</AlertDialogCancel>
                                                            <AlertDialogAction
                                                                onClick={() => handleDeleteQuote(q.id)}
                                                            >
                                                                Continuiar
                                                            </AlertDialogAction>
                                                        </AlertDialogFooter>
                                                    </AlertDialogContent>
                                                </AlertDialog>
                                            </div>
                                        </div>
                                    </div>
                                ))}
                            </div>
                        )}
                    </CardContent>
                </Card>
            </main>
            <Footer />
        </div>
    )
}