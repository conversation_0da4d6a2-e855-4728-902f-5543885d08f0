"use client"

import { use<PERSON>allback, useEffect, useState } from 'react';
import { useSession } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import { format } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { Filter, ListTodo, RefreshCw, Search, ArrowDownAZ, ArrowDownWideNarrow, TriangleAlert, Info } from 'lucide-react';
import { toast } from "sonner";
import { Header } from '@/app/components/header';
import { Footer } from '@/app/components/footer';
import { Button } from '@/app/components/ui/button';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/app/components/ui/card';
import { Input } from '@/app/components/ui/input';
import { Badge } from '@/app/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/app/components/ui/select';
import Loading from '@/app/components/ui/loading';
import { MarkAsCompleted } from '@/app/components/mark-as-completed';
import { ActivityInfo } from '@/app/components/activity-info';
import { AddFeedUrlModal } from '@/app/components/add-feed-url-modal';
import { Separator } from '../components/ui/separator';
import { NotAllowed } from '../components/not-allowed';
import { isPendingDemand } from '@/lib/utils';
import { UpdateContentStatus } from '../components/update-content-status';
import { Tooltip, TooltipContent, TooltipTrigger, TooltipProvider } from '../components/ui/tooltip';
import { ContentAssignmentView } from '../components/content-assignment-view';
import { ChangeContentType } from '../components/change-content-type';

interface Client {
  id: string;
  name: string;
  instagramUsername?: string;
}

interface User {
  id: string;
  name: string | null;
  email: string;
  image: string | null;
}

interface Step {
  id: string;
  type: string;
  assignedTo: User;
}

interface WeeklyActivity {
  id: string;
  week: number;
  description: string;
  monthlyPlanning: {
    status: string;
    id: string;
    month: number;
    year: number;
    client: Client;
  };
}

interface Content {
  review: string;
  id: string;
  activityDate: string;
  contentType?: string;
  channel?: string;
  details?: string;
  destination?: string;
  caption?: string;
  status?: string;
  copywriting?: string;
  reference?: string;
  urlStructuringFeed?: string | string[];
  urlTypes?: string | string[];
  urlMediaTypes?: string | string[];
  urlThumbnails?: string[];
  urlFolder?: string;
  assignedToId?: string | null;
  assignedTo?: User | null;
  createdAt?: string;
  updatedAt?: string;
  weeklyActivity?: WeeklyActivity | null;
  type?: 'content' | 'general';
  title?: string;
  description?: string;
  priority?: string;
  position?: number;
  client?: Client;
  isLooseClient?: boolean;
  archived?: boolean;
  steps?: Step[];
}

const getUrlTypesFromContent = (content: Content): string[] => {
  const types = Array.isArray(content.urlTypes)
    ? content.urlTypes
    : content.urlTypes ? [content.urlTypes] : [];

  return types.length > 0 ? types : [];
};

const getUrlMediaTypesFromContent = (content: Content): string[] => {
  const mediaTypes = Array.isArray(content.urlMediaTypes)
    ? content.urlMediaTypes
    : content.urlMediaTypes ? [content.urlMediaTypes] : [];

  return mediaTypes.length > 0 ? mediaTypes : [];
};

const getUrlThumbnailsFromContent = (content: Content): string[] => {
  return content.urlThumbnails || [];
};

export default function MyDemandsPage() {
  const router = useRouter();
  const [isLoading, setIsLoading] = useState(true);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [demands, setDemands] = useState<Content[]>([]);
  const [filteredDemands, setFilteredDemands] = useState<Content[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('pendente');
  const [monthFilter, setMonthFilter] = useState('all');
  const [clientFilter, setClientFilter] = useState('all');
  const [viewMode, setViewMode] = useState<'week' | 'priority'>('week');
  const [userRole, setUserRole] = useState('');
  const { data: session, status } = useSession();

  useEffect(() => {
    const checkUserRole = async () => {
      if (session?.user?.email) {
        try {
          const response = await fetch(`/api/users/${session.user.email}`);
          if (response.ok) {
            const user = await response.json();
            setUserRole(user?.role || '');
          }
        } catch (error) {
          console.error("Erro ao verificar função do usuário:", error);
        }
      }
    };

    if (status === "authenticated") {
      checkUserRole();
    } else if (status === "unauthenticated") {
      router.push("/");
    }
  }, [status, session, router]);

  const fetchDemands = useCallback(async () => {
    try {
      const response = await fetch('/api/user/demands');
      if (!response.ok) {
        throw new Error('Falha ao buscar demandas');
      }
      const data = await response.json();

      const nonArchivedDemands = data.filter((demand: { archived: boolean; }) => !demand.archived);

      setDemands(nonArchivedDemands);
      setFilteredDemands(nonArchivedDemands);
    } catch (error) {
      console.error('Erro ao buscar demandas:', error);
      toast.error('Erro ao buscar demandas');
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    if (status === "authenticated") {
      fetchDemands();
    } else if (status === 'unauthenticated') {
      router.push('/');
    }
  }, [status, router, fetchDemands]);

  useEffect(() => {
    let result = [...demands];

    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(demand => {
        const clientNameMatch = demand.weeklyActivity?.monthlyPlanning?.client?.name?.toLowerCase().includes(term) ||
          demand.client?.name?.toLowerCase().includes(term) ||
          (term === 'não fixo' && demand.isLooseClient) || false;

        const contentTypeMatch = demand.contentType?.toLowerCase().includes(term) ||
          demand.title?.toLowerCase().includes(term) || false;

        const detailsMatch = (demand.details && demand.details.toLowerCase().includes(term)) ||
          (demand.description && demand.description.toLowerCase().includes(term)) || false;

        return clientNameMatch || contentTypeMatch || detailsMatch;
      });
    }

    result = result.filter(demand => {
      if (demand.type === 'general' || !demand.weeklyActivity?.monthlyPlanning) {
        return true;
      }

      const isApproved = demand.weeklyActivity.monthlyPlanning.status === 'aprovado';
      if (!isApproved) {
      }
      return isApproved;
    });

    if (statusFilter !== 'all') {
      if (statusFilter === 'pendente') {
        result = result.filter(demand => {
          const isPending = isPendingDemand(demand);
          return isPending;
        });
      } else if (statusFilter === 'concluído') {
        result = result.filter(demand =>
          demand.status === 'concluído' ||
          demand.status === 'captado' ||
          demand.status === 'pend. captação' ||
          demand.status === 'em revisão' ||
          demand.status === 'anúncio concluído'
        );
      } else if (statusFilter === 'alteração') {
        result = result.filter(demand => demand.status === 'alteração');
      } else {
        result = result.filter(demand => demand.status === statusFilter);
      }
    }

    if (monthFilter !== 'all') {
      const [month, year] = monthFilter.split('-').map(Number);

      result = result.filter(demand => {
        const date = new Date(demand.activityDate);
        const demandMonth = date.getMonth() + 1;
        const demandYear = date.getFullYear();
        const matches = demandMonth === month && demandYear === year;

        if (!matches) {
        }

        return matches;
      });
    }

    if (clientFilter !== 'all') {
      result = result.filter(demand => {
        if (demand.weeklyActivity?.monthlyPlanning?.client) {
          const matches = demand.weeklyActivity.monthlyPlanning.client.id === clientFilter;
          return matches;
        } else if (demand.client) {
          const matches = demand.client.id === clientFilter;
          return matches;
        }
        return false;
      });
    }

    setFilteredDemands(result);
  }, [demands, searchTerm, statusFilter, monthFilter, clientFilter]);

  const handleRefresh = () => {
    setIsRefreshing(true);
    fetchDemands().finally(() => setIsRefreshing(false));
  };

  const getClientOptions = () => {
    const options = [{ value: 'all', label: 'Todos os clientes' }];

    const uniqueClients = new Map();

    demands.forEach(demand => {
      if (demand.weeklyActivity && demand.weeklyActivity.monthlyPlanning) {
        const client = demand.weeklyActivity.monthlyPlanning.client;
        if (!uniqueClients.has(client.id)) {
          uniqueClients.set(client.id, client.name);
        }
      } else if (demand.type === 'general' && demand.client) {
        const clientId = demand.client.id;
        const clientName = demand.isLooseClient
          ? `${demand.client.name}`
          : demand.client.name;

        if (!uniqueClients.has(clientId)) {
          uniqueClients.set(clientId, clientName);
        }
      }
    });

    Array.from(uniqueClients.entries()).forEach(([id, name]) => {
      options.push({
        value: id,
        label: name
      });
    });

    return options.sort((a, b) => {
      if (a.value === 'all') return -1;
      if (b.value === 'all') return 1;
      return a.label.localeCompare(b.label);
    });
  };

  const getMonthOptions = () => {
    const options = [{ value: 'all', label: 'Todos os meses' }];
    const months = ['Janeiro', 'Fevereiro', 'Março', 'Abril', 'Maio', 'Junho', 'Julho', 'Agosto', 'Setembro', 'Outubro', 'Novembro', 'Dezembro'];

    const uniqueMonthYears = new Set();

    demands.forEach(demand => {
      const date = new Date(demand.activityDate);
      const month = date.getMonth() + 1;
      const year = date.getFullYear();
      const key = `${month}-${year}`;

      if (!uniqueMonthYears.has(key)) {
        uniqueMonthYears.add(key);
        options.push({
          value: key,
          label: `${months[month - 1]} ${year}`
        });
      }
    });

    return options.sort((a, b) => {
      if (a.value === 'all') return -1;
      if (b.value === 'all') return 1;

      const [monthA, yearA] = a.value.split('-').map(Number);
      const [monthB, yearB] = b.value.split('-').map(Number);

      if (yearA !== yearB) return yearB - yearA;
      return monthB - monthA;
    });
  };

  const groupDemandsByWeek = () => {
    const grouped: { [key: string]: Content[] } = {};

    filteredDemands.forEach(demand => {
      const date = new Date(demand.activityDate);
      const weekStart = new Date(date);
      weekStart.setDate(date.getDate() - date.getDay());
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekStart.getDate() + 6);

      const weekKey = `${format(weekStart, 'dd/MM/yyyy')} - ${format(weekEnd, 'dd/MM/yyyy')}`;

      if (!grouped[weekKey]) {
        grouped[weekKey] = [];
      }

      grouped[weekKey].push(demand);
    });

    Object.keys(grouped).forEach(key => {
      grouped[key].sort((a, b) => {
        const dateA = a.activityDate ? new Date(a.activityDate).getTime() : 0;
        const dateB = b.activityDate ? new Date(b.activityDate).getTime() : 0;
        return dateA - dateB;
      });
    });

    return grouped;
  };

  const groupDemandsByPriority = () => {
    const demandsToGroup = filteredDemands;

    const priorityGroups: { [key: string]: Content[] } = {
      'Urgente': [],
      'Alta': [],
      'Normal': [],
      'Baixa': [],
      'Sem prioridade': []
    };

    demandsToGroup.forEach(demand => {
      const priority = demand.priority
        ? demand.priority.charAt(0).toUpperCase() + demand.priority.slice(1)
        : 'Sem prioridade';

      if (priorityGroups[priority]) {
        priorityGroups[priority].push(demand);
      } else {
        priorityGroups['Sem prioridade'].push(demand);
      }
    });

    Object.keys(priorityGroups).forEach(key => {
      priorityGroups[key].sort((a, b) => {
        const posA = a.position || 9999;
        const posB = b.position || 9999;

        if (posA !== posB) {
          return posA - posB;
        }

        const dateA = a.activityDate ? new Date(a.activityDate).getTime() : 0;
        const dateB = b.activityDate ? new Date(b.activityDate).getTime() : 0;
        return dateA - dateB;
      });
    });

    const filteredGroups: { [key: string]: Content[] } = {};
    Object.keys(priorityGroups).forEach(key => {
      if (priorityGroups[key].length > 0) {
        filteredGroups[key] = priorityGroups[key];
      }
    });

    return filteredGroups;
  };

  const handleStatusUpdate = async (contentId: string, newStatus: string) => {
    try {
      const demand = demands.find(d => d.id === contentId);
      if (!demand) {
        throw new Error('Demanda não encontrada');
      }

      const apiUrl = demand.type === 'general'
        ? `/api/general-demands/${contentId}/status`
        : `/api/contents/${contentId}`;

      const response = await fetch(apiUrl, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status: newStatus }),
      });

      if (!response.ok) {
        throw new Error('Falha ao atualizar status');
      }

      setDemands(prevDemands =>
        prevDemands.map(demand =>
          demand.id === contentId
            ? { ...demand, status: newStatus }
            : demand
        )
      );

      toast.success('Status atualizado com sucesso');
    } catch (error) {
      console.error('Erro ao atualizar status:', error);
      toast.error('Erro ao atualizar status');
    }
  };

  const handleUrlUpdate = (contentId: string, newUrls: string[], newUrlTypes: string[], newMediaTypes?: string[], newUrlFolder?: string, newUrlThumbnails?: string[]) => {
    setDemands(prevDemands =>
      prevDemands.map(demand =>
        demand.id === contentId
          ? {
              ...demand,
              urlStructuringFeed: newUrls.length > 0 ? newUrls : undefined,
              urlTypes: newUrlTypes.length > 0 ? newUrlTypes : undefined,
              urlMediaTypes: newMediaTypes && newMediaTypes.length > 0 ? newMediaTypes : undefined,
              urlThumbnails: newUrlThumbnails && newUrlThumbnails.length > 0 ? newUrlThumbnails : undefined,
              urlFolder: newUrlFolder
            }
          : demand
      )
    );

    setFilteredDemands(prevDemands =>
      prevDemands.map(demand =>
        demand.id === contentId
          ? {
              ...demand,
              urlStructuringFeed: newUrls.length > 0 ? newUrls : undefined,
              urlTypes: newUrlTypes.length > 0 ? newUrlTypes : undefined,
              urlMediaTypes: newMediaTypes && newMediaTypes.length > 0 ? newMediaTypes : undefined,
              urlThumbnails: newUrlThumbnails && newUrlThumbnails.length > 0 ? newUrlThumbnails : undefined,
              urlFolder: newUrlFolder
            }
          : demand
      )
    );
  };

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <div className="p-4 xs:p-8 flex-grow">
        {isLoading ? (
          <div className="min-h-[70vh] flex justify-center items-center">
            <Loading />
          </div>
        ) : userRole === 'VIEWER' ? (
          <NotAllowed page="/" />
        ) : (
          <>
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center gap-3 group">
                <h1 className="text-sm xs:text-lg uppercase font-geistMono font-semibold tracking-tight">
                  Minhas Demandas
                </h1>
                <div className="bg-orange-50 dark:bg-orange-950 p-2 rounded-full">
                  <ListTodo size={24} color="#db5743" />
                </div>
              </div>
              <Button
                variant="outline"
                size="icon"
                onClick={handleRefresh}
                disabled={isRefreshing}
              >
                <RefreshCw size={16} className={isRefreshing ? 'animate-spin' : ''} />
              </Button>
            </div>

            <div className="mb-6">
              <div className="grid grid-cols-1 md:grid-cols-12 gap-4">
                <div className="relative md:col-span-4">
                  <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    type="search"
                    placeholder="Buscar por cliente, tipo ou detalhes..."
                    className="pl-8 w-full placeholder:text-sm"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="md:col-span-8 grid grid-cols-1 sm:grid-cols-3 gap-2">
                  <div>
                    <Select value={statusFilter} onValueChange={setStatusFilter}>
                      <SelectTrigger className="w-full">
                        <Filter className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="Status" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">Todos os status</SelectItem>
                        <SelectItem value="pendente">Pendentes</SelectItem>
                        <SelectItem value="alteração">Alterações</SelectItem>
                        <SelectItem value="concluído">Concluídos</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Select value={monthFilter} onValueChange={setMonthFilter}>
                      <SelectTrigger className="w-full">
                        <Filter className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="Mês" />
                      </SelectTrigger>
                      <SelectContent>
                        {getMonthOptions().map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Select value={clientFilter} onValueChange={setClientFilter}>
                      <SelectTrigger className="w-full">
                        <Filter className="h-4 w-4 mr-2" />
                        <SelectValue placeholder="Cliente" />
                      </SelectTrigger>
                      <SelectContent>
                        {getClientOptions().map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="md:col-span-12 mt-4 flex flex-col sm:flex-row justify-between gap-2 items-center">
                  <Button
                    variant="outline"
                    className="gap-2"
                    onClick={() => setViewMode(viewMode === 'week' ? 'priority' : 'week')}
                  >
                    {viewMode === 'priority' ? (
                      <>
                        <ArrowDownAZ className="h-4 w-4" />
                        Visualização por semana
                      </>
                    ) : (
                      <>
                        <ArrowDownWideNarrow className="h-4 w-4" />
                        Visualização por prioridade
                      </>
                    )}
                  </Button>
                  <p className='text-sm text-muted-foreground'>
                    {filteredDemands.length} demandas encontradas
                  </p>
                </div>
              </div>
            </div>

            {filteredDemands.length === 0 ? (
              <div className="text-center py-10">
                <p className="text-muted-foreground text-sm">Nenhuma demanda encontrada.</p>
              </div>
            ) : viewMode === 'week' ? (
              <div className="space-y-8">
                {Object.entries(groupDemandsByWeek()).map(([weekRange, weekDemands]) => (
                  <div key={weekRange}>
                    <div className="flex justify-between items-center mb-3 border-b pb-1">
                      <h2 className="text-base font-semibold">
                        {weekRange}
                      </h2>
                    </div>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {weekDemands.map((demand) => (
                        <Card key={demand.id} className="overflow-hidden">
                          <CardHeader className="p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <CardTitle className="text-base">
                                  {demand.contentType || demand.title}
                                  <br />
                                  <p className='text-sm text-zinc-700 dark:text-zinc-300'>
                                    {demand.destination || <span className='italic text-muted-foreground'>demanda pontual</span>}
                                  </p>
                                </CardTitle>
                                <CardDescription>
                                  {format(new Date(demand.activityDate), "dd 'de' MMMM, yyyy", { locale: ptBR })}
                                  <br />
                                  {demand.id.substring(0, 8).toUpperCase()}
                                </CardDescription>
                              </div>
                              <Badge
                                variant="outline"
                                className={demand.isLooseClient ? "border-dashed" : ""}
                              >
                                {demand.weeklyActivity?.monthlyPlanning?.client?.name ||
                                  demand.client?.name || 'Cliente não especificado'}
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="p-4 pt-0">
                            {(demand.details || demand.description) && (
                              <p className="text-sm mb-2">{demand.details || demand.description}</p>
                            )}
                            <div className="flex flex-col gap-1 mt-2">
                              <div className="flex gap-1">
                                {demand.priority ? (
                                  <div className="flex items-center gap-2">
                                    <Badge variant="outline" className={`
                                    ${demand.priority === 'alta' ? 'bg-red-100 text-red-800 border-red-300 dark:bg-red-800 dark:text-red-300 dark:border-red-900' : ''}
                                    ${demand.priority === 'normal' ? 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-800 dark:text-yellow-300 dark:border-yellow-900' : ''}
                                    ${demand.priority === 'baixa' ? 'bg-green-100 text-green-800 border-green-300 dark:bg-green-800 dark:text-green-300 dark:border-green-900' : ''}
                                    ${demand.priority === 'urgente' ? 'bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-800 dark:text-orange-300 dark:border-orange-900' : ''}
                                    `}
                                    >
                                      {demand.priority}
                                    </Badge>
                                  </div>
                                ) : demand.destination ? (
                                  <Badge variant="outline">{demand.destination}</Badge>
                                ) : null}
                              </div>
                              <ContentAssignmentView
                                assignedTo={demand.assignedTo}
                                steps={demand.steps}
                              />
                              <Separator className="my-2" />
                              {demand.review && (
                                <div className="flex flex-col items-start gap-2">
                                  <Badge variant="outline" className='bg-sky-200 text-sky-800 border-sky-300 dark:bg-sky-800 dark:text-sky-300 dark:border-sky-900'>
                                    <TriangleAlert size={16} className='mr-1' />
                                    Alteração solicitada
                                  </Badge>
                                  <p className="text-sm text-muted-foreground">
                                    {demand.review}
                                  </p>
                                </div>
                              )}
                            </div>
                          </CardContent>
                          <CardFooter className="p-4 pt-0 flex items-start justify-between">
                            <div>
                              <div className="flex items-center gap-2">
                                <UpdateContentStatus
                                  contentId={demand.id}
                                  currentStatus={demand.status || 'pendente'}
                                  isAdmin={userRole === 'ADMIN' || userRole === 'DEVELOPER'}
                                  onLocalUpdate={(newStatus) => handleStatusUpdate(demand.id, newStatus)}
                                />
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Info className="h-4 w-4 text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent className="mx-2 p-3 max-w-xs">
                                      <p className='text-xs'>
                                        Esse é o status real da demanda, você pode alterá-lo manualmente para <i className='font-semibold'>em andamento</i> quando começar a trabalhar nela. Quando você marca a demanda como concluída, o status mudará automaticamente para <i className='font-semibold'>em revisão</i>.
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                            <div className="flex gap-2">
                              <ActivityInfo
                                activity={{
                                  reference: demand.reference,
                                  copywriting: demand.copywriting,
                                  caption: demand.caption,
                                  url: Array.isArray(demand.urlStructuringFeed) ? demand.urlStructuringFeed : demand.urlStructuringFeed ? [demand.urlStructuringFeed] : undefined,
                                  urlFolder: demand.urlFolder
                                }}
                              />
                              <AddFeedUrlModal
                                contentId={demand.id}
                                currentUrl={demand.urlStructuringFeed}
                                currentUrlTypes={getUrlTypesFromContent(demand)}
                                currentMediaTypes={getUrlMediaTypesFromContent(demand)}
                                currentUrlThumbnails={getUrlThumbnailsFromContent(demand)}
                                currentUrlFolder={demand.urlFolder}
                                onUrlUpdated={handleUrlUpdate}
                                type={demand.type || 'content'}
                              />
                              <ChangeContentType
                                contentId={demand.id}
                                currentType={demand.contentType || 'feed'}
                                onSuccess={handleRefresh}
                              />
                              <MarkAsCompleted
                                contentId={demand.id}
                                currentStatus={demand.status || ''}
                                onStatusUpdate={(newStatus) => handleStatusUpdate(demand.id, newStatus)}
                                type={demand.type || 'content'}
                              />
                            </div>
                          </CardFooter>
                        </Card>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="space-y-8">
                {Object.entries(groupDemandsByPriority()).map(([priority, demands]) => (
                  <div key={priority}>
                    <div className="flex justify-between items-center mb-3 border-b pb-1">
                      <h2 className="text-base font-semibold flex items-center gap-2">
                        <Badge variant="outline" className={`
                          ${priority === 'Urgente' ? 'bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-800 dark:text-orange-300 dark:border-orange-900' : ''}
                          ${priority === 'Alta' ? 'bg-red-100 text-red-800 border-red-300 dark:bg-red-800 dark:text-red-300 dark:border-red-900' : ''}
                          ${priority === 'Normal' ? 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-800 dark:text-yellow-300 dark:border-yellow-900' : ''}
                          ${priority === 'Baixa' ? 'bg-green-100 text-green-800 border-green-300 dark:bg-green-800 dark:text-green-300 dark:border-green-900' : ''}
                          ${priority === 'Sem prioridade' ? 'bg-gray-100 text-gray-800 border-gray-300 dark:bg-gray-800 dark:text-gray-300 dark:border-gray-900' : ''}
                        `}>
                          {priority}
                        </Badge>
                        <span>({demands.length} {demands.length === 1 ? 'demanda' : 'demandas'})</span>
                      </h2>
                    </div>

                    <div className="space-y-2">
                      {demands.map((demand) => (
                        <Card key={demand.id} className="overflow-hidden">
                          <CardHeader className="p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <CardTitle className="text-base">
                                  {demand.contentType || demand.title}
                                  <br />
                                  <p className='text-sm text-zinc-700 dark:text-zinc-300'>
                                    {demand.destination || <span className='italic text-muted-foreground'>demanda pontual</span>}
                                  </p>
                                </CardTitle>
                                <CardDescription>
                                  {format(new Date(demand.activityDate), "dd 'de' MMMM, yyyy", { locale: ptBR })}
                                  <br />
                                  {demand.id.substring(0, 8).toUpperCase()}
                                </CardDescription>
                              </div>
                              <Badge
                                variant="outline"
                                className={demand.isLooseClient ? "border-dashed" : ""}
                              >
                                {demand.weeklyActivity?.monthlyPlanning?.client?.name ||
                                  demand.client?.name || 'Cliente não especificado'}
                              </Badge>
                            </div>
                          </CardHeader>
                          <CardContent className="p-4 pt-0">
                            {(demand.details || demand.description) && (
                              <p className="text-sm mb-2">{demand.details || demand.description}</p>
                            )}
                            <div className="flex flex-col gap-1 mt-2">
                              <div className="flex gap-1">
                                {demand.priority ? (
                                  <div className="flex items-center gap-2">
                                    <Badge variant="outline" className={`
                                    ${demand.priority === 'alta' ? 'bg-red-100 text-red-800 border-red-300 dark:bg-red-800 dark:text-red-300 dark:border-red-900' : ''}
                                    ${demand.priority === 'normal' ? 'bg-yellow-100 text-yellow-800 border-yellow-300 dark:bg-yellow-800 dark:text-yellow-300 dark:border-yellow-900' : ''}
                                    ${demand.priority === 'baixa' ? 'bg-green-100 text-green-800 border-green-300 dark:bg-green-800 dark:text-green-300 dark:border-green-900' : ''}
                                    ${demand.priority === 'urgente' ? 'bg-orange-100 text-orange-800 border-orange-300 dark:bg-orange-800 dark:text-orange-300 dark:border-orange-900' : ''}
                                    `}
                                    >
                                      {demand.priority}
                                    </Badge>
                                  </div>
                                ) : demand.destination ? (
                                  <Badge variant="outline">{demand.destination}</Badge>
                                ) : null}
                              </div>
                              <ContentAssignmentView
                                assignedTo={demand.assignedTo}
                                steps={demand.steps}
                              />
                              <Separator className="my-2" />
                              {demand.review && (
                                <div className="flex flex-col items-start gap-2">
                                  <Badge variant="outline" className='bg-sky-200 text-sky-800 border-sky-300 dark:bg-sky-800 dark:text-sky-300 dark:border-sky-900'>
                                    <TriangleAlert size={16} className='mr-1' />
                                    Alteração solicitada
                                  </Badge>
                                  <p className="text-sm text-muted-foreground">
                                    {demand.review}
                                  </p>
                                </div>
                              )}
                            </div>
                          </CardContent>
                          <CardFooter className="p-4 pt-0 flex items-start justify-between">
                            <div>
                              <div className="flex items-center gap-2">
                                <UpdateContentStatus
                                  contentId={demand.id}
                                  currentStatus={demand.status || 'pendente'}
                                  isAdmin={userRole === 'ADMIN' || userRole === 'DEVELOPER'}
                                  onLocalUpdate={(newStatus) => handleStatusUpdate(demand.id, newStatus)}
                                />
                                <TooltipProvider>
                                  <Tooltip>
                                    <TooltipTrigger asChild>
                                      <Info className="h-4 w-4 text-muted-foreground" />
                                    </TooltipTrigger>
                                    <TooltipContent className="mx-2 p-3 max-w-xs">
                                      <p className='text-xs'>
                                        Esse é o status real da demanda, você pode alterá-lo manualmente para <i className='font-semibold'>em andamento</i> quando começar a trabalhar nela. Quando você marca a demanda como concluída, o status mudará automaticamente para <i className='font-semibold'>em revisão</i>.
                                      </p>
                                    </TooltipContent>
                                  </Tooltip>
                                </TooltipProvider>
                              </div>
                            </div>
                            <div className="flex gap-2">
                              <ActivityInfo
                                activity={{
                                  reference: demand.reference,
                                  copywriting: demand.copywriting,
                                  caption: demand.caption,
                                  url: Array.isArray(demand.urlStructuringFeed) ? demand.urlStructuringFeed : demand.urlStructuringFeed ? [demand.urlStructuringFeed] : undefined,
                                  urlFolder: demand.urlFolder
                                }}
                              />
                              <AddFeedUrlModal
                                contentId={demand.id}
                                currentUrl={demand.urlStructuringFeed}
                                currentUrlTypes={getUrlTypesFromContent(demand)}
                                currentMediaTypes={getUrlMediaTypesFromContent(demand)}
                                currentUrlThumbnails={getUrlThumbnailsFromContent(demand)}
                                currentUrlFolder={demand.urlFolder}
                                onUrlUpdated={handleUrlUpdate}
                                type={demand.type || 'content'}
                              />
                              <ChangeContentType
                                contentId={demand.id}
                                currentType={demand.contentType || 'feed'}
                                onSuccess={handleRefresh}
                              />
                              <MarkAsCompleted
                                contentId={demand.id}
                                currentStatus={demand.status || ''}
                                onStatusUpdate={(newStatus) => handleStatusUpdate(demand.id, newStatus)}
                                type={demand.type || 'content'}
                              />
                            </div>
                          </CardFooter>
                        </Card>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </>
        )}
      </div>
      <Footer />
    </div>
  );
}
