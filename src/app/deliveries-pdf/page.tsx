"use client"

import { DeliveriesContent } from "@/app/components/deliveries-content";
import { Client } from "@prisma/client";
import { useSearchParams } from "next/navigation";
import { Suspense, useEffect, useState } from "react";
import Loading from "../components/ui/loading";
import { Ellipsis } from "lucide-react";

function DeliveriesPDFContent() {
    const searchParams = useSearchParams();
    const id = searchParams.get("id");
    const month = searchParams.get("month");
    const filter = searchParams.get("filter") as "todos" | "com-legenda" | "sem-legenda" || "todos";
    const selectedItems = searchParams.get("selectedItems")?.split(',') || [];

    const [client, setClient] = useState<Client>();
    const [isReady, setIsReady] = useState(false);

    useEffect(() => {
        if (!id) return;

        const fetchClientData = async () => {
            try {
                const response = await fetch(`/api/clients/${id}?include=monthlyPlannings.activities.contents`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                const data = await response.json();
                setClient(data);
            } catch (error) {
                console.error("Erro ao carregar dados do cliente:", error);
                setIsReady(true); // Libera a tela mesmo em caso de erro de fetch
            }
        };

        fetchClientData();
    }, [id]);

    useEffect(() => {
        if (!client) return;

        // Atraso para garantir que o DOM foi renderizado com os dados do cliente
        const renderTimeout = setTimeout(() => {
            const images = document.querySelectorAll('img[data-pdf-image="true"]');
            const totalImages = images.length;

            if (totalImages === 0) {
                setIsReady(true);
                return;
            }

            let loadedCount = 0;

            const imageLoaded = () => {
                loadedCount++;
                if (loadedCount >= totalImages) {
                    // Adiciona um pequeno delay extra para garantir a renderização final
                    setTimeout(() => setIsReady(true), 500);
                }
            };

            images.forEach(imgElement => {
                const img = imgElement as HTMLImageElement; // Type casting
                if (img.complete) {
                    imageLoaded();
                } else {
                    img.onload = imageLoaded;
                    img.onerror = () => {
                        console.warn('Falha ao carregar imagem para PDF, continuando...', img.src);
                        imageLoaded(); // Conta como "carregada" para não bloquear a impressão
                    };
                }
            });

            const safetyTimeout = setTimeout(() => {
                console.warn("Timeout de segurança atingido. Forçando a prontidão do PDF.");
                setIsReady(true);
            }, 15000);

            return () => clearTimeout(safetyTimeout);

        }, 500); 

        return () => clearTimeout(renderTimeout);

    }, [client]);

    if (!client && !isReady) return <Loading />;
    if (!client && isReady) return <div>Erro ao carregar dados do cliente. Verifique o ID e tente novamente.</div>;

    return (
        <div className="pdf-container">
            <style dangerouslySetInnerHTML={{
                __html: `
                    .pdf-caption-container {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        margin-top: 16px !important;
                        padding-top: 12px !important;
                        padding-bottom: 16px !important;
                        border-top: 1px solid #eee !important;
                        width: 100% !important;
                        box-sizing: border-box !important;
                        page-break-inside: avoid !important;
                        break-inside: avoid !important;
                    }
                    .pdf-caption {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        font-size: 14px !important;
                        width: 100% !important;
                        color: #222 !important;
                    }
                    .pdf-caption span {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        font-weight: 700 !important;
                        margin-bottom: 8px !important;
                        color: #222 !important;
                    }
                    .pdf-caption-text {
                        display: block !important;
                        visibility: visible !important;
                        opacity: 1 !important;
                        margin-top: 4px !important;
                        white-space: pre-wrap !important;
                        word-break: break-word !important;
                        text-align: justify !important;
                        color: #333 !important;
                        font-weight: 400 !important;
                        line-height: 1.4 !important;
                        font-size: 13px !important;
                    }
                `
            }} />
            <div className="pdf-content">
                <DeliveriesContent
                    clientId={id || ""}
                    client={client}
                    isPdfMode={true}
                    initialMonth={month || undefined}
                    compactView={true}
                    initialCaptionFilter={filter}
                    selectedItemsForPdf={selectedItems}
                />
            </div>

            {!isReady && <div className="pdf-loading"><Ellipsis /></div>}
        </div>
    );
}

export default function DeliveriesPDF() {
    return (
        <Suspense fallback={<Loading />}>
            <DeliveriesPDFContent />
        </Suspense>
    );
}
