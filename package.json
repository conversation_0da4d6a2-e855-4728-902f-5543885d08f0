{"name": "b4-desk", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "postinstall": "prisma generate", "start": "next start", "lint": "next lint", "setup": "./scripts/setup.sh", "setup:dev": "cp .env.development .env && echo Ambiente de desenvolvimento configurado ✅", "setup:prod": "cp .env.production .env && echo Ambiente de produção configurado ✅⚠️", "db:migrate": "./scripts/safe-db.sh pnpm exec prisma migrate dev", "db:reset": "./scripts/safe-db.sh pnpm exec prisma migrate reset", "db:deploy": "./scripts/safe-db.sh pnpm exec prisma migrate deploy", "db:studio": "./scripts/safe-db.sh pnpm exec prisma studio", "db:seed": "./scripts/safe-db.sh pnpm exec prisma db seed", "db:status": "./scripts/safe-db.sh pnpm exec prisma migrate status", "db:generate": "./scripts/safe-db.sh pnpm exec prisma generate", "backup:create": "./scripts/backup.sh", "backup:restore": "./scripts/restore.sh", "migrate:prod": "echo '⚠️  PRODUÇÃO: Certifique-se de ter backup!' && read -p 'Digite CONFIRMO para continuar: ' confirm && [ \"$confirm\" = \"CONFIRMO\" ] && pnpm exec prisma migrate deploy", "reset:prod": "echo '🚨 PERIGO: <PERSON><PERSON> vai DELETAR todos os dados!' && echo 'Use backup:restore em vez disso!' && exit 1"}, "dependencies": {"@auth/prisma-adapter": "^2.7.4", "@dnd-kit/core": "^6.3.1", "@dnd-kit/sortable": "^10.0.0", "@dnd-kit/utilities": "^3.2.2", "@prisma/client": "^6.2.1", "@radix-ui/react-alert-dialog": "^1.1.5", "@radix-ui/react-avatar": "^1.1.2", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.5", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-tooltip": "^1.1.8", "@sparticuz/chromium": "^133.0.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "googleapis": "^148.0.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.0", "lucide-react": "^0.471.1", "next": "15.2.4", "next-auth": "^4.24.11", "next-pwa": "^5.6.0", "next-themes": "^0.4.4", "prisma": "^6.2.1", "puppeteer-core": "^24.5.0", "react": "^19.1.0", "react-day-picker": "8.10.1", "react-dom": "^19.1.0", "recharts": "^2.15.1", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "tsx": "^4.19.2"}, "devDependencies": {"@babel/core": "^7.27.1", "@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "babel-loader": "^10.0.0", "eslint": "^9", "eslint-config-next": "15.1.4", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5.7.3"}}