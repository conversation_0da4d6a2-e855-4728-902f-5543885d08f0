/* TODO: 🔳 Verificar se é necessário */

/* 
.pdf-single-image-row {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    width: 100%;
    margin: 16px 0;
    gap: 0;
}

.pdf-card-single {
    width: 100%;
    background: none;
    box-shadow: none;
    border: none;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}
.pdf-card-single img {
    width: 100% !important;
    aspect-ratio: 1/1 !important;
    max-width: 900px !important;
    max-height: 900px !important;
    min-width: 320px;
    min-height: 320px;
    object-fit: contain !important;
    border-radius: 16px;
    margin: 0 auto;
    display: block;
}

.pdf-container {
    font-size: 11px;
    line-height: 1.2;
    max-width: 1100px;
    margin: 0 auto;
    padding: 5mm;
}

.pdf-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    justify-content: flex-start;
    align-items: flex-start;
    margin-top: 10px;
}

.pdf-card {
    width: 220px;
    max-width: 220px;
    min-width: 180px;
    flex: 0 0 220px;
    margin: 0;
    break-inside: avoid;
    page-break-inside: avoid;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 4px rgba(0,0,0,0.04);
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 8px 4px 12px 4px;
}

.pdf-image-container {
    width: 100%;
    aspect-ratio: 1/1;
    position: relative;
    overflow: hidden;
    background-color: #f7f7f7;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pdf-image-container img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    display: block;
}

@media print {
    body {
        margin: 0;
        padding: 0;
    }

    .pdf-container {
        padding: 3mm !important;
    }

    .pdf-grid {
        gap: 6px !important;
    }

    .pdf-card {
        page-break-inside: avoid;
        break-inside: avoid;
    }

    h1,
    h2,
    h3,
    h4 {
        page-break-after: avoid;
    }

    h1,
    h2,
    h3 {
        margin: 0 0 5px 0 !important;
        padding: 0 !important;
    }

    .pdf-grid>*:nth-child(-n+6) {
        page-break-after: avoid;
        break-after: avoid;
    }

    .no-print {
        display: none !important;
    }
}

.pdf-demand-group {
    break-inside: avoid;
    page-break-inside: avoid;
    margin-bottom: 20px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 15px;
}

.pdf-demand-group:last-child {
    margin-bottom: 0;
}

.pdf-demand-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;
    font-size: 13px;
}

.pdf-content-type {
    font-weight: bold;
}

.pdf-date {
    color: #555;
}

.pdf-destination {
    font-style: italic;
    color: #333;
    background-color: #f9f9f9;
    padding: 2px 6px;
    border-radius: 4px;
}

.pdf-caption-container {
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #eee;
}

.pdf-caption {
    font-size: 12px;
}

.pdf-caption span {
    font-weight: bold;
}

.pdf-caption-text {
    margin-top: 4px;
    white-space: pre-wrap;
    word-wrap: break-word;
    text-align: justify;
} */
